#!/bin/bash

echo "🔍 检查项目编译状态..."

# 项目路径
PROJECT_PATH="./tempFileXcode.xcodeproj"

if [ ! -d "$PROJECT_PATH" ]; then
    echo "❌ 错误: 未找到项目文件 $PROJECT_PATH"
    exit 1
fi

echo "📁 项目文件: $PROJECT_PATH"

# 检查编译
echo "🔨 开始编译检查..."

xcodebuild -project "$PROJECT_PATH" -scheme tempFileXcode -configuration Debug clean build 2>&1 | \
while IFS= read -r line; do
    if [[ "$line" == *"error:"* ]]; then
        echo "❌ 编译错误: $line"
    elif [[ "$line" == *"warning:"* ]]; then
        echo "⚠️  编译警告: $line"
    elif [[ "$line" == *"BUILD SUCCEEDED"* ]]; then
        echo "✅ 编译成功!"
    elif [[ "$line" == *"BUILD FAILED"* ]]; then
        echo "❌ 编译失败!"
    fi
done

echo "✅ 编译检查完成"