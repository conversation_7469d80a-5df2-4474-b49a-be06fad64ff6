Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project tempFileXcode.xcodeproj -scheme tempFileXcode -configuration Debug build

--- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:macOS, arch:arm64, id:00006034-001249943E44001C, name:My Mac }
{ platform:macOS, arch:x86_64, id:00006034-001249943E44001C, name:My Mac }
{ platform:macOS, name:Any Mac }
ComputePackagePrebuildTargetDependencyGraph

Prepare packages

CreateBuildRequest

SendProjectDescription

CreateBuildOperation

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (1 target)
    Target 'tempFileXcode' in project 'tempFileXcode' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/momc --dry-run --action generate --swift-version 5.0 --sdkroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk --macosx-deployment-target 26.0 --module tempFileXcode /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/tempBox.xcdatamodeld /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/CoreDataGenerated/tempBox

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc --version

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

Build description signature: 989c000d49210e3e292640a79ce187d3
Build description path: /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/XCBuildData/989c000d49210e3e292640a79ce187d3.xcbuilddata
ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx26.0-25A5279m-28806144b0707e398aac92880fa50324.sdkstatcache
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk -o /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx26.0-25A5279m-28806144b0707e398aac92880fa50324.sdkstatcache

SwiftDriver tempFileXcode normal arm64 com.apple.xcode.tools.swift.compiler (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    builtin-SwiftDriver -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name tempFileXcode -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/tempFileXcode.SwiftFileList -DDEBUG -default-isolation\=MainActor -enable-bare-slash-regex -enable-upcoming-feature MemberImportVisibility -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk -target arm64-apple-macos26.0 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Products/Debug -F /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Products/Debug -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64 -c -j14 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx26.0-25A5279m-28806144b0707e398aac92880fa50324.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/tempFileXcode-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -explicit-module-build -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules -clang-scanner-module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -sdk-module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/tempFileXcode.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/tempFileXcode_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/tempFileXcode-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/tempFileXcode-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/tempFileXcode-all-target-headers.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/tempFileXcode-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/tempFileXcode-Swift.h -working-directory /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode -experimental-emit-module-separately -disable-cmo

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Products/Debug/tempFileXcode.app/Contents/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/empty-tempFileXcode.plist (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/empty-tempFileXcode.plist -producttype com.apple.product-type.application -genpkginfo /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Products/Debug/tempFileXcode.app/Contents/PkgInfo -expandbuildsettings -platform macosx -additionalcontentfile /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/assetcatalog_generated_info.plist -o /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Products/Debug/tempFileXcode.app/Contents/Info.plist

SwiftCompile normal arm64 Compiling\ SearchService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/SearchService.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/SearchService.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ PersistenceController.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Models/PersistenceController.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Models/PersistenceController.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ DragDetectionService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/DragDetectionService.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/DragDetectionService.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ EnhancedMainContentView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/EnhancedMainContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/EnhancedMainContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ DropZoneView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/DropZoneView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/DropZoneView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    
/Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/DropZoneView.swift:53:18: warning: capture of non-sendable type 'Content.Type' in an isolated closure
    private func handleDrop(providers: [NSItemProvider]) -> Bool {
                 ^
/Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/DropZoneView.swift:53:18: warning: capture of non-sendable type 'Content.Type' in an isolated closure
    private func handleDrop(providers: [NSItemProvider]) -> Bool {
                 ^
/Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/DropZoneView.swift:53:18: warning: capture of non-sendable type 'Content.Type' in an isolated closure
    private func handleDrop(providers: [NSItemProvider]) -> Bool {
                 ^
/Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/DropZoneView.swift:225:30: warning: consider using asynchronous alternative function
                    provider.loadItem(forTypeIdentifier: "public.image", options: nil) { item, error in
                             ^
Foundation.NSItemProvider.loadItem:2:11: note: 'loadItem(forTypeIdentifier:options:)' declared here
open func loadItem(forTypeIdentifier typeIdentifier: String, options: [AnyHashable : Any]? = nil) async throws -> any NSSecureCoding}
          ^

SwiftCompile normal arm64 Compiling\ NewBatchContentView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/NewBatchContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/NewBatchContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftEmitModule normal arm64 Emitting\ module\ for\ tempFileXcode (in target 'tempFileXcode' from project 'tempFileXcode')

EmitSwiftModule normal arm64 (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ ViewModeManager.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ViewModeManager.swift (in target 'tempFileXcode' from project 'tempFileXcode')
Failed frontend command:
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -c /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Models/ColorExtensions.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Models/ContentData.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Models/ContentManagerError.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Models/ContentType.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Models/CoreDataExtensions.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Models/PersistenceController.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/BatchService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/CloudSyncService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/ContentService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/ContentServiceProtocol.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/DragDetectionService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/DragDropWindowManager.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/ExportService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/FilePreviewService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/FileTypeManager.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/HotkeyService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/PasteboardMonitor.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/PermissionManager.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/SearchService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/SearchServiceProtocol.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/SelectionManager.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/SmartOrganizationService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/StatusBarService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/SystemPermissionManager.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/BatchContentView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/BatchInfoView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/BatchSettingsPopover.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/BatchSidebarView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/CloudSyncView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ContentDetailView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ContentDropHandler.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ContentEditView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/DragDropOverlayWindow.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/DragDropWindow.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/DropZoneView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/EnhancedContentCard.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/EnhancedMainContentView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/EnhancedPreviewView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/EnhancedViewComponents.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ExportSheet.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/InteractiveButton.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/MainContentView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ModernBatchSelector.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ModernContentCard.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ModernContentView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/NewBatchContentView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/OptimizedContentCard.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/QuickPasteWindow.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/SettingsView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/SmartOrganizationView.swift -primary-file /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ViewModeManager.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/ContentManagerApp.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/CoreDataGenerated/tempBox/AppSettings+CoreDataClass.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/CoreDataGenerated/tempBox/AppSettings+CoreDataProperties.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/CoreDataGenerated/tempBox/Batch+CoreDataClass.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/CoreDataGenerated/tempBox/Batch+CoreDataProperties.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/CoreDataGenerated/tempBox/ContentItem+CoreDataClass.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/CoreDataGenerated/tempBox/ContentItem+CoreDataProperties.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/CoreDataGenerated/tempBox/Tag+CoreDataClass.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/CoreDataGenerated/tempBox/Tag+CoreDataProperties.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/CoreDataGenerated/tempBox/tempBox+CoreDataModel.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/GeneratedAssetSymbols.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/ViewModeManager.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/ViewModeManager.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/ViewModeManager.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/ViewModeManager.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64 -target arm64-apple-macos26.0 -module-can-import-version AppKit 2665.8 2665.8.0 -module-can-import-version DeveloperToolsSupport 23.0.4 23.0.4 -module-can-import-version SwiftUI 7.0.67.1 7.0.67 -disable-cross-import-overlay-search -swift-module-cross-import CoreData /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftcrossimport/CloudKit.swiftoverlay -swift-module-cross-import CoreData /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk/System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftcrossimport/CloudKit.swiftoverlay -swift-module-cross-import QuickLook /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/QuickLook.framework/Modules/QuickLook.swiftcrossimport/SwiftUI.swiftoverlay -swift-module-cross-import WebKit /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Cryptexes/OS/System/Library/Frameworks/WebKit.framework/Modules/WebKit.swiftcrossimport/SwiftUI.swiftoverlay -swift-module-cross-import WebKit /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk/System/Library/Frameworks/WebKit.framework/Modules/WebKit.swiftcrossimport/SwiftUI.swiftoverlay -load-resolved-plugin /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/swift/host/plugins/libFoundationMacros.dylib\#/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin/swift-plugin-server\#FoundationMacros -load-resolved-plugin /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/swift/host/plugins/libObservationMacros.dylib\#/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin/swift-plugin-server\#ObservationMacros -load-resolved-plugin /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/swift/host/plugins/libPreviewsMacros.dylib\#/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin/swift-plugin-server\#PreviewsMacros -load-resolved-plugin /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/swift/host/plugins/libSwiftMacros.dylib\#/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin/swift-plugin-server\#SwiftMacros -load-resolved-plugin /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib/swift/host/plugins/libSwiftUIMacros.dylib\#/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/bin/swift-plugin-server\#SwiftUIMacros -disable-implicit-swift-modules -Xcc -fno-implicit-modules -Xcc -fno-implicit-module-maps -explicit-swift-module-map-file /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/tempFileXcode-dependencies-1.json -Xllvm -aarch64-use-tbi -enable-objc-interop -stack-check -sdk /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX26.0.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Products/Debug -F /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Products/Debug -no-color-diagnostics -Xcc -fno-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=5 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/SwiftExplicitPrecompiledModules -profile-generate -profile-coverage-mapping -swift-version 5 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/tempFileXcode_const_extract_protocols.json -enable-upcoming-feature MemberImportVisibility -enable-experimental-feature DebugDescriptionMacro -enable-bare-slash-regex -default-isolation\=MainActor -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx26.0-25A5279m-28806144b0707e398aac92880fa50324.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/tempFileXcode-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/tempFileXcode-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/tempFileXcode-all-target-headers.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/tempFileXcode-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Products/Debug/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/DerivedSources -Xcc -DDEBUG\=1 -no-auto-bridging-header-chaining -module-name tempFileXcode -frontend-parseable-output -disable-clang-spi -target-sdk-version 26.0 -target-sdk-name macosx26.0 -clang-target arm64-apple-macos26.0 -o /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Build/Intermediates.noindex/tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/ViewModeManager.o -index-unit-output-path /tempFileXcode.build/Debug/tempFileXcode.build/Objects-normal/arm64/ViewModeManager.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/tempFileXcode-fihfxhugbyjilscdsahmxchondic/Index.noindex/DataStore -index-system-modules

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ViewModeManager.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    
/Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ViewModeManager.swift:138:10: warning: 'onChange(of:perform:)' was deprecated in macOS 14.0: Use `onChange` with a two or zero parameter action closure instead.
        .onChange(of: items) { newItems in
         ^
/Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ViewModeManager.swift:449:47: error: member 'tertiary' in 'Color?' produces result of type 'some ShapeStyle', but context expects 'Color?'
                            .foregroundColor(.tertiary)
                                              ^
/Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ViewModeManager.swift:449:47: error: instance member 'tertiary' cannot be used on type 'Color?'
                            .foregroundColor(.tertiary)
                                             ~^~~~~~~~

SwiftCompile normal arm64 Compiling\ OptimizedContentCard.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/OptimizedContentCard.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/OptimizedContentCard.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftDriverJobDiscovery normal arm64 Compiling PersistenceController.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftDriverJobDiscovery normal arm64 Compiling DragDetectionService.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftDriverJobDiscovery normal arm64 Compiling SearchService.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 Compiling\ EnhancedPreviewView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/EnhancedPreviewView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/EnhancedPreviewView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ HotkeyService.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/HotkeyService.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Services/HotkeyService.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ ContentEditView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ContentEditView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ContentEditView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ BatchSidebarView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/BatchSidebarView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/BatchSidebarView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ CloudSyncView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/CloudSyncView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/CloudSyncView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ MainContentView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/MainContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/MainContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ InteractiveButton.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/InteractiveButton.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/InteractiveButton.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ ContentManagerApp.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/ContentManagerApp.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/ContentManagerApp.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ ExportSheet.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ExportSheet.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ExportSheet.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftDriverJobDiscovery normal arm64 Compiling DropZoneView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 Compiling\ ModernContentView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ModernContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ModernContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftDriverJobDiscovery normal arm64 Compiling EnhancedMainContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 Compiling\ BatchContentView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/BatchContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/BatchContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftDriverJobDiscovery normal arm64 Compiling InteractiveButton.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 Compiling\ SettingsView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/SettingsView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/SettingsView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ QuickPasteWindow.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/QuickPasteWindow.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/QuickPasteWindow.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftCompile normal arm64 Compiling\ SmartOrganizationView.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/SmartOrganizationView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/SmartOrganizationView.swift (in target 'tempFileXcode' from project 'tempFileXcode')
    cd /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode
    

SwiftDriverJobDiscovery normal arm64 Compiling HotkeyService.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftDriverJobDiscovery normal arm64 Compiling ContentManagerApp.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftDriverJobDiscovery normal arm64 Compiling ExportSheet.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftDriverJobDiscovery normal arm64 Compiling ContentEditView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftDriverJobDiscovery normal arm64 Compiling MainContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftDriverJobDiscovery normal arm64 Compiling ModernContentView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftDriverJobDiscovery normal arm64 Compiling CloudSyncView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftDriverJobDiscovery normal arm64 Compiling EnhancedPreviewView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftDriverJobDiscovery normal arm64 Compiling SettingsView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

SwiftDriverJobDiscovery normal arm64 Compiling SmartOrganizationView.swift (in target 'tempFileXcode' from project 'tempFileXcode')

** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 Compiling\ ViewModeManager.swift /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ViewModeManager.swift (in target 'tempFileXcode' from project 'tempFileXcode')
	SwiftCompile normal arm64 /Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode/tempFileXcode/Views/ViewModeManager.swift (in target 'tempFileXcode' from project 'tempFileXcode')
	Building project tempFileXcode with scheme tempFileXcode and configuration Debug
(3 failures)
