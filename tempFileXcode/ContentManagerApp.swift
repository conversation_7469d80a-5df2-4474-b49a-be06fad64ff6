import SwiftUI
import CoreData
import Combine
import UniformTypeIdentifiers

@main
struct ContentManagerApp: App {
    @StateObject private var persistenceController = PersistenceController.shared
    @StateObject private var appState = AppState()
    @StateObject private var contentService: ContentService
    @StateObject private var searchService: SearchService
    @StateObject private var pasteboardMonitor: PasteboardMonitor
    @StateObject private var hotkeyService: HotkeyService
    @StateObject private var batchService: BatchService
    @StateObject private var quickPasteManager: QuickPasteManager
    @StateObject private var exportService: ExportService
    @StateObject private var statusBarManager: StatusBarManager
    @StateObject private var dragDropManager: DragDropWindowManager
    @StateObject private var dragDetectionService: DragDetectionService

    init() {
        // 创建服务实例
        let contentService = ContentService()
        let searchService = SearchService()
        let pasteboardMonitor = PasteboardMonitor()
        let hotkeyService = HotkeyService()
        let batchService = BatchService()
        let quickPasteManager = QuickPasteManager(
            contentService: contentService,
            pasteboardMonitor: pasteboardMonitor,
            batchService: batchService
        )
        let statusBarManager = StatusBarManager()
        let dragDropManager = DragDropWindowManager(
            contentService: contentService,
            batchService: batchService
        )
        let dragDetectionService = DragDetectionService()

        // 初始化StateObject
        _contentService = StateObject(wrappedValue: contentService)
        _searchService = StateObject(wrappedValue: searchService)
        _pasteboardMonitor = StateObject(wrappedValue: pasteboardMonitor)
        _hotkeyService = StateObject(wrappedValue: hotkeyService)
        _batchService = StateObject(wrappedValue: batchService)
        _quickPasteManager = StateObject(wrappedValue: quickPasteManager)
        _exportService = StateObject(wrappedValue: ExportService(contentService: contentService))
        _statusBarManager = StateObject(wrappedValue: statusBarManager)
        _dragDropManager = StateObject(wrappedValue: dragDropManager)
        _dragDetectionService = StateObject(wrappedValue: dragDetectionService)

        // 设置依赖关系
        statusBarManager.setDependencies(
            contentService: contentService,
            batchService: batchService,
            pasteboardMonitor: pasteboardMonitor,
            quickPasteManager: quickPasteManager,
            dragDetectionService: dragDetectionService
        )
        
        // 设置拖拽检测服务双向引用
        dragDetectionService.setDragDropManager(dragDropManager)
        dragDropManager.setDragDetectionService(dragDetectionService)
    }
    
    var body: some Scene {
        WindowGroup {
            NewBatchContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(appState)
                .environmentObject(contentService)
                .environmentObject(searchService)
                .environmentObject(pasteboardMonitor)
                .environmentObject(hotkeyService)
                .environmentObject(batchService)
                .environmentObject(exportService)
                .environmentObject(statusBarManager)
                .environmentObject(quickPasteManager)
                .environmentObject(dragDropManager)
                .environmentObject(dragDetectionService)
                .onAppear {
                    setupServices()
                }
        }
        .defaultSize(width: 1200, height: 800)
        .commands {
            CommandGroup(replacing: .newItem) {
                Button("快速粘贴") {
                    quickPasteManager.showQuickPasteWindow()
                }
                .keyboardShortcut("v", modifiers: [.command, .shift])
                
                Button("拖拽文件窗口") {
                    dragDetectionService.showDragDropWindowManually()
                }
                .keyboardShortcut("d", modifiers: [.command, .shift])
            }

            CommandGroup(after: .newItem) {
                Button("开始剪贴板监听") {
                    pasteboardMonitor.startMonitoring()
                }
                .disabled(pasteboardMonitor.isMonitoring)

                Button("停止剪贴板监听") {
                    pasteboardMonitor.stopMonitoring()
                }
                .disabled(!pasteboardMonitor.isMonitoring)
            }
        }
    }
    
    private func setupServices() {
        // Setup hotkey callback
        hotkeyService.setHotkeyCallback {
            DispatchQueue.main.async {
                quickPasteManager.toggleQuickPasteWindow()
            }
        }

        // Setup pasteboard monitoring callback
        pasteboardMonitor.onContentDetected = { content in
            // 只有在检测到有用内容时才显示快速粘贴窗口
            DispatchQueue.main.async {
                // 检查内容是否有效且不为空
                if self.isValidContentForQuickPaste(content) {
                    quickPasteManager.showQuickPasteWindow()
                    NSLog("Detected clipboard change: \(content.displayTitle)")
                }
            }
        }

        // Start pasteboard monitoring
        pasteboardMonitor.startMonitoring()
        
        // 启动拖拽检测服务
        dragDetectionService.startMonitoring()
    }
    
    // MARK: - Helper Methods
    private func isValidContentForQuickPaste(_ content: PasteboardContent) -> Bool {
        switch content.type {
        case .text:
            // 检查文本内容是否有效
            guard let text = content.text else { 
                print("No text content found")
                return false 
            }
            
            // 性能优化：对于长文本，避免完整的trim操作
            let isEmpty: Bool
            if text.count > 1000 {
                let prefix = String(text.prefix(100))
                let suffix = String(text.suffix(100))
                isEmpty = (prefix + suffix).trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            } else {
                isEmpty = text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            }
            
            guard !isEmpty else { 
                print("Empty text content")
                return false 
            }
            
            // 更智能的文件路径检测：只过滤明显的完整路径
            // 检查是否是完整的文件路径（必须满足多个条件）
            let isLikelyFilePath = isCompleteFilePath(text)
            if isLikelyFilePath {
                print("Filtered out file path content")
                return false
            }
            
            let logText = text.count > 50 ? String(text.prefix(50)) + "..." : text
            print("Valid text content detected: '\(logText)' (length: \(text.count))")
            return true
            
        case .image:
            // 图片内容（截图或复制的图片）有效
            return content.fileSize > 0
            
        case .file:
            // 文件类型不显示快速添加窗口（避免复制文件时误触发）
            return false
        }
    }
    
    // MARK: - 智能文件路径检测
    private func isCompleteFilePath(_ text: String) -> Bool {
        // 如果文本太短，不太可能是完整路径
        guard text.count > 20 else { return false }
        
        // 如果包含多行，通常不是单个文件路径
        let lines = text.components(separatedBy: .newlines)
        if lines.count > 2 {
            return false
        }
        
        // 检查是否是单一的完整路径
        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 必须是绝对路径或file://协议开头
        guard trimmedText.hasPrefix("/") || trimmedText.hasPrefix("file://") else {
            return false
        }
        
        // 路径分段数量检查 - 必须有足够的层级
        let pathComponents = trimmedText.components(separatedBy: "/")
        guard pathComponents.count >= 5 else { return false } // 更严格要求
        
        // 检查是否整个文本就是一个完整的文件路径（没有其他文字）
        let isJustPath = lines.count == 1 && trimmedText == text.trimmingCharacters(in: .whitespacesAndNewlines)
        guard isJustPath else { return false }
        
        // 检查路径特征组合 - 必须同时满足多个条件
        let hasSystemPath = trimmedText.contains("/Users/") || 
                           trimmedText.contains("/Applications/") || 
                           trimmedText.contains("/System/") ||
                           trimmedText.contains("/Library/")
        
        let hasSpecificExtension = trimmedText.contains(".app/") || 
                                  trimmedText.contains(".xcodeproj/") ||
                                  trimmedText.contains(".build/") ||
                                  trimmedText.contains(".framework/")
        
        let hasDevPath = trimmedText.contains("/DerivedData/") ||
                        trimmedText.contains("/Build/") ||
                        trimmedText.contains("/Xcode/")
        
        // 只有当它真的看起来是一个完整的开发相关的系统路径时才过滤
        let isLikelyBuildPath = hasSystemPath && hasDevPath && (hasSpecificExtension || pathComponents.count > 8)
        
        // 超长路径（可能是构建路径）
        let isVeryLongBuildPath = trimmedText.count > 150 && pathComponents.count > 10 && hasDevPath
        
        let result = isLikelyBuildPath || isVeryLongBuildPath
        if result {
            print("Filtered build path: length=\(trimmedText.count), components=\(pathComponents.count), hasSystemPath=\(hasSystemPath), hasDevPath=\(hasDevPath)")
        } else {
            print("Allowed text with path keywords: \(trimmedText.prefix(100))")
        }
        
        return result
    }
    
    private func showFileSelectionOnLaunch() {
        let panel = NSOpenPanel()
        panel.title = "选择要导入的文件"
        panel.message = "选择您要导入到TempBox的文件"
        panel.allowsMultipleSelection = true
        panel.canChooseDirectories = false
        panel.canChooseFiles = true
        panel.allowedContentTypes = [.data, .text, .image, .movie, .audio, .pdf]
        
        let response = panel.runModal()
        if response == .OK {
            Task {
                // 确保有当前批次
                if batchService.currentBatch == nil {
                    do {
                        let newBatch = try batchService.createNewBatch(name: "导入批次", notes: "应用启动时创建")
                        batchService.setCurrentBatch(newBatch)
                    } catch {
                        NSLog("Failed to create batch: \(error)")
                        return
                    }
                }
                
                // 导入选择的文件
                for url in panel.urls {
                    do {
                        let contentData = try ContentData.fromFile(at: url)
                        _ = try await contentService.addContent(contentData, toBatch: batchService.currentBatch)
                        NSLog("Successfully imported file: \(url.lastPathComponent)")
                    } catch {
                        NSLog("Failed to import file \(url.lastPathComponent): \(error)")
                    }
                }
            }
        } else {
            // 用户取消了文件选择，退出应用
            NSApplication.shared.terminate(nil)
        }
    }
    

}

// MARK: - App State Management
class AppState: ObservableObject {
    @Published var selectedViewMode: ViewMode = .timeline {
        didSet { saveUserPreferences() }
    }
    @Published var searchText: String = "" {
        didSet { saveUserPreferences() }
    }
    @Published var selectedTags: Set<String> = [] {
        didSet { saveUserPreferences() }
    }
    @Published var selectedContentTypes: Set<ContentType> = [] {
        didSet { saveUserPreferences() }
    }
    @Published var isQuickPasteWindowVisible: Bool = false
    
    init() {
        // Load user preferences
        loadUserPreferences()
    }
    
    private func loadUserPreferences() {
        // 从UserDefaults加载用户偏好设置
        if let savedViewMode = UserDefaults.standard.string(forKey: "selectedViewMode"),
           let viewMode = ViewMode(rawValue: savedViewMode) {
            selectedViewMode = viewMode
        } else {
            selectedViewMode = .timeline
        }
        
        // 加载搜索文本
        searchText = UserDefaults.standard.string(forKey: "searchText") ?? ""
        
        // 加载选中的标签
        if let savedTags = UserDefaults.standard.array(forKey: "selectedTags") as? [String] {
            selectedTags = Set(savedTags)
        }
        
        // 加载选中的内容类型
        if let savedTypes = UserDefaults.standard.array(forKey: "selectedContentTypes") as? [String] {
            selectedContentTypes = Set(savedTypes.compactMap { ContentType(rawValue: $0) })
        }
    }
    
    private func saveUserPreferences() {
        UserDefaults.standard.set(selectedViewMode.rawValue, forKey: "selectedViewMode")
        UserDefaults.standard.set(searchText, forKey: "searchText")
        UserDefaults.standard.set(Array(selectedTags), forKey: "selectedTags")
        UserDefaults.standard.set(Array(selectedContentTypes.map { $0.rawValue }), forKey: "selectedContentTypes")
    }
}

// MARK: - View Mode Enum
enum ViewMode: String, CaseIterable {
    case timeline = "timeline"
    case type = "type"
    
    var displayName: String {
        switch self {
        case .timeline:
            return "时间视图"
        case .type:
            return "类型视图"
        }
    }
    
    var systemImage: String {
        switch self {
        case .timeline:
            return "clock"
        case .type:
            return "square.grid.2x2"
        }
    }
}

// MARK: - Global Drag Monitoring (Disabled for now)
// Global drag monitoring implementation can be added later if needed