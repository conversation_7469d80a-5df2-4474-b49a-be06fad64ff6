import Foundation
import UniformTypeIdentifiers

// MARK: - Content Data Transfer Object
struct ContentData {
    let id: UUID
    let title: String?
    let content: String?
    let contentType: ContentType
    let data: Data?
    let filePath: String?
    let fileName: String?
    let fileSize: Int64
    let mimeType: String?
    let notes: String?
    let tags: [String]
    let createdAt: Date
    let updatedAt: Date
    let expiresAt: Date?
    let isPermanent: Bool
    
    // MARK: - Initializers
    init(
        id: UUID = UUID(),
        title: String? = nil,
        content: String? = nil,
        contentType: ContentType,
        data: Data? = nil,
        filePath: String? = nil,
        fileName: String? = nil,
        fileSize: Int64 = 0,
        mimeType: String? = nil,
        notes: String? = nil,
        tags: [String] = [],
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        expiresAt: Date? = nil,
        isPermanent: Bool = false
    ) {
        self.id = id
        self.title = title
        self.content = content
        self.contentType = contentType
        self.data = data
        self.filePath = filePath
        self.fileName = fileName
        self.fileSize = fileSize
        self.mimeType = mimeType
        self.notes = notes
        self.tags = tags
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.expiresAt = expiresAt
        self.isPermanent = isPermanent
    }
    
    // MARK: - Factory Methods
    @MainActor
    static func fromText(_ text: String, title: String? = nil) -> ContentData {
        // 极限性能优化：所有操作都延迟到真正需要时
        let fileSize = Int64(text.utf8.count) // O(1)复杂度的字节数计算
        
        // 优化标题生成 - 只在需要时才计算
        let optimizedTitle: String
        if let title = title {
            optimizedTitle = title
        } else if text.isEmpty {
            optimizedTitle = "空文本"
        } else {
            // 使用高效的字符截取
            let maxTitleLength = min(50, text.count)
            if maxTitleLength == text.count {
                optimizedTitle = text
            } else {
                let endIndex = text.index(text.startIndex, offsetBy: maxTitleLength)
                optimizedTitle = String(text[..<endIndex])
            }
        }
        
        return ContentData(
            title: optimizedTitle,
            content: text,
            contentType: .text,
            data: nil, // 完全延迟生成
            fileSize: fileSize,
            mimeType: "text/plain"
        )
    }
    
    // MARK: - 高性能数据获取
    func getTextData() -> Data? {
        if let existingData = data {
            return existingData
        }
        // 在需要时才生成
        return content?.data(using: .utf8)
    }
    
    @MainActor
    static func fromImage(_ imageData: Data, fileName: String? = nil) -> ContentData {
        let name = fileName ?? "image_\(Date().timeIntervalSince1970).png"
        return ContentData(
            title: name,
            contentType: .image,
            data: imageData,
            fileName: name,
            fileSize: Int64(imageData.count),
            mimeType: "image/png"
        )
    }
    
    @MainActor
    static func fromFile(at url: URL) throws -> ContentData {
        print("🔐 智能文件访问开始: \(url.path)")
        print("🔐 URL类型: \(url.isFileURL ? "文件URL" : "其他类型")")

        // 使用智能文件访问策略
        let accessResult = try performIntelligentFileAccess(for: url)
        let data = accessResult.data
        let accessMethod = accessResult.method

        print("🔐 文件访问成功，方法: \(accessMethod)，大小: \(data.count) bytes")

        // 创建ContentData对象
        return try createContentData(from: url, data: data, accessMethod: accessMethod)
    }

    /// 智能文件访问结果
    private struct FileAccessResult {
        let data: Data
        let method: String
    }

    /// 执行智能文件访问策略
    private static func performIntelligentFileAccess(for url: URL) throws -> FileAccessResult {
        print("🔍 开始智能文件访问策略")

        // 首先确保安全作用域访问
        let needsSecurityScope = url.startAccessingSecurityScopedResource()
        defer {
            if needsSecurityScope {
                url.stopAccessingSecurityScopedResource()
                print("🔐 已释放安全作用域访问")
            }
        }

        // 策略1: 直接访问（最高效）
        if let result = tryDirectAccess(url: url) {
            return result
        }

        // 策略2: 书签恢复访问
        if let result = tryBookmarkAccess(url: url) {
            return result
        }

        // 策略3: URL变体访问
        if let result = tryURLVariants(url: url) {
            return result
        }

        // 策略4: 文件协调器访问
        if let result = tryCoordinatedAccess(url: url) {
            return result
        }

        // 所有策略都失败，抛出权限错误
        print("🔐 所有访问策略都失败，需要用户授权")
        throw ContentManagerError.permissionDenied("需要用户授权访问文件: \(url.lastPathComponent)")
    }

    /// 策略1: 直接访问（增强版）
    private static func tryDirectAccess(url: URL) -> FileAccessResult? {
        // 首先尝试不使用安全作用域的直接访问
        do {
            let data = try Data(contentsOf: url)
            print("✅ 直接访问成功（无需安全作用域）")
            return FileAccessResult(data: data, method: "direct")
        } catch {
            print("❌ 直接访问失败: \(error)")
        }
        
        // 如果直接访问失败，尝试使用安全作用域访问
        let needsSecurityScope = url.startAccessingSecurityScopedResource()
        defer {
            if needsSecurityScope {
                url.stopAccessingSecurityScopedResource()
            }
        }
        
        do {
            let data = try Data(contentsOf: url)
            print("✅ 安全作用域直接访问成功")
            return FileAccessResult(data: data, method: "direct_scoped")
        } catch {
            print("❌ 安全作用域直接访问失败: \(error)")
            return nil
        }
    }

    /// 策略2: 书签恢复访问
    private static func tryBookmarkAccess(url: URL) -> FileAccessResult? {
        guard let bookmarkURL = PermissionManager.shared.restoreFromBookmark(for: url.path) else {
            print("📚 没有找到书签")
            return nil
        }

        let bookmarkAccess = bookmarkURL.startAccessingSecurityScopedResource()
        defer {
            if bookmarkAccess {
                bookmarkURL.stopAccessingSecurityScopedResource()
            }
        }

        do {
            let data = try Data(contentsOf: bookmarkURL)
            print("✅ 书签访问成功")
            return FileAccessResult(data: data, method: "bookmark")
        } catch {
            print("❌ 书签访问失败: \(error)")
            return nil
        }
    }

    /// 策略3: URL变体访问
    private static func tryURLVariants(url: URL) -> FileAccessResult? {
        let variants = [
            ("标准化", url.standardized),
            ("符号链接解析", url.resolvingSymlinksInPath()),
            ("标准化+符号链接", url.standardized.resolvingSymlinksInPath())
        ]

        for (name, variantURL) in variants {
            do {
                let data = try Data(contentsOf: variantURL)
                print("✅ \(name)访问成功")
                return FileAccessResult(data: data, method: name.lowercased())
            } catch {
                print("❌ \(name)访问失败: \(error)")
            }
        }

        return nil
    }

    /// 策略4: 文件协调器访问
    private static func tryCoordinatedAccess(url: URL) -> FileAccessResult? {
        var coordinatorError: NSError?
        var coordinatedData: Data?

        NSFileCoordinator().coordinate(readingItemAt: url, options: [], error: &coordinatorError) { coordinatedURL in
            do {
                coordinatedData = try Data(contentsOf: coordinatedURL)
                print("✅ 文件协调器访问成功")
            } catch {
                print("❌ 文件协调器内部访问失败: \(error)")
            }
        }

        if let error = coordinatorError {
            print("❌ 文件协调器错误: \(error)")
        }

        if let data = coordinatedData {
            return FileAccessResult(data: data, method: "coordinated")
        }

        return nil
    }

    /// 创建ContentData对象 - 增强版本
    private static func createContentData(from url: URL, data: Data, accessMethod: String) throws -> ContentData {
        let fileName = url.lastPathComponent
        let contentType = ContentType.detectType(from: url)

        print("🔍 创建ContentData - 文件: \(fileName), 类型: \(contentType), 大小: \(data.count) bytes")

        var content: String?
        var mimeType: String?

        // 智能内容处理
        switch contentType {
        case .text:
            content = extractTextContent(from: data, fileName: fileName)
            mimeType = determineMimeType(for: url, defaultType: "text/plain")

        case .image:
            mimeType = determineMimeType(for: url, defaultType: "image/\(url.pathExtension.lowercased())")

        case .file:
            // 尝试检测是否为文本文件（即使扩展名不是文本类型）
            if let textContent = String(data: data, encoding: .utf8),
               isLikelyTextContent(textContent) {
                content = textContent
                mimeType = "text/plain"
                print("🔍 检测到文本内容（扩展名: \(url.pathExtension)）")
            } else {
                mimeType = determineMimeType(for: url, defaultType: "application/octet-stream")
            }
        }

        // 验证数据完整性
        guard !data.isEmpty else {
            throw ContentManagerError.invalidContent("文件为空: \(fileName)")
        }

        // 检查文件大小限制（例如100MB）
        let maxFileSize: Int64 = 100 * 1024 * 1024
        if data.count > maxFileSize {
            throw ContentManagerError.contentTooLarge("文件过大: \(fileName) (\(data.count) bytes)")
        }

        print("✅ ContentData创建成功 - 方法: \(accessMethod), MIME: \(mimeType ?? "unknown")")

        return ContentData(
            title: fileName,
            content: content,
            contentType: contentType,
            data: data,
            filePath: url.path,
            fileName: fileName,
            fileSize: Int64(data.count),
            mimeType: mimeType
        )
    }

    /// 提取文本内容
    private static func extractTextContent(from data: Data, fileName: String) -> String? {
        // 尝试多种编码
        let encodings: [String.Encoding] = [.utf8, .utf16, .ascii, .isoLatin1]

        for encoding in encodings {
            if let content = String(data: data, encoding: encoding) {
                print("🔤 成功使用 \(encoding) 编码解析文本")
                return content
            }
        }

        print("⚠️ 无法解析文本内容: \(fileName)")
        return nil
    }

    /// 确定MIME类型
    private static func determineMimeType(for url: URL, defaultType: String) -> String {
        if #available(macOS 11.0, *) {
            if let utType = UTType(filenameExtension: url.pathExtension),
               let mimeType = utType.preferredMIMEType {
                return mimeType
            }
        }

        // 回退到基于扩展名的简单映射
        let fileExtension = url.pathExtension.lowercased()
        let commonMimeTypes: [String: String] = [
            "txt": "text/plain",
            "md": "text/markdown",
            "json": "application/json",
            "xml": "application/xml",
            "html": "text/html",
            "css": "text/css",
            "js": "application/javascript",
            "pdf": "application/pdf",
            "jpg": "image/jpeg",
            "jpeg": "image/jpeg",
            "png": "image/png",
            "gif": "image/gif",
            "svg": "image/svg+xml"
        ]

        return commonMimeTypes[fileExtension] ?? defaultType
    }

    /// 检查是否可能是文本内容
    private static func isLikelyTextContent(_ content: String) -> Bool {
        // 检查是否包含过多的控制字符或二进制数据
        let controlCharCount = content.filter { char in
            char.isASCII && char.asciiValue! < 32 && !char.isWhitespace
        }.count
        let totalCharCount = content.count

        // 如果控制字符超过5%，可能不是文本文件
        return totalCharCount > 0 && Double(controlCharCount) / Double(totalCharCount) < 0.05
    }
    
    // MARK: - Computed Properties
    var displayTitle: String {
        if let title = title, !title.isEmpty {
            return title
        }
        
        if let fileName = fileName {
            return fileName
        }
        
        if let content = content, !content.isEmpty {
            // 极限性能优化：避免不必要的字符串操作
            if content.count <= 50 {
                return content
            } else {
                // 使用更高效的prefix方法
                return String(content.prefix(50))
            }
        }
        
        return "Untitled \(contentType.displayName)"
    }
    
    var formattedFileSize: String {
        // 性能优化：使用静态 formatter 避免重复创建
        return Self.fileSizeFormatter.string(fromByteCount: fileSize)
    }
    
    // 静态 formatter 避免重复创建
    private static let fileSizeFormatter: ByteCountFormatter = {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useBytes, .useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter
    }()
    
    var isExpired: Bool {
        guard let expiresAt = expiresAt else { return false }
        return Date() > expiresAt
    }
    
    var remainingTime: TimeInterval? {
        guard let expiresAt = expiresAt else { return nil }
        let remaining = expiresAt.timeIntervalSinceNow
        return remaining > 0 ? remaining : 0
    }
    
    // MARK: - Validation
    var isValid: Bool {
        // Basic validation rules
        guard fileSize >= 0 else { return false }
        
        switch contentType {
        case .text:
            return content != nil && !content!.isEmpty
        case .image, .file:
            return data != nil && !data!.isEmpty
        }
    }
    
    // MARK: - Update Methods
    func updatingTitle(_ newTitle: String) -> ContentData {
        ContentData(
            id: id,
            title: newTitle,
            content: content,
            contentType: contentType,
            data: data,
            filePath: filePath,
            fileName: fileName,
            fileSize: fileSize,
            mimeType: mimeType,
            notes: notes,
            tags: tags,
            createdAt: createdAt,
            updatedAt: Date(),
            expiresAt: expiresAt,
            isPermanent: isPermanent
        )
    }
    
    func updatingNotes(_ newNotes: String) -> ContentData {
        ContentData(
            id: id,
            title: title,
            content: content,
            contentType: contentType,
            data: data,
            filePath: filePath,
            fileName: fileName,
            fileSize: fileSize,
            mimeType: mimeType,
            notes: newNotes,
            tags: tags,
            createdAt: createdAt,
            updatedAt: Date(),
            expiresAt: expiresAt,
            isPermanent: isPermanent
        )
    }
    
    func updatingTags(_ newTags: [String]) -> ContentData {
        ContentData(
            id: id,
            title: title,
            content: content,
            contentType: contentType,
            data: data,
            filePath: filePath,
            fileName: fileName,
            fileSize: fileSize,
            mimeType: mimeType,
            notes: notes,
            tags: newTags,
            createdAt: createdAt,
            updatedAt: Date(),
            expiresAt: expiresAt,
            isPermanent: isPermanent
        )
    }
    
    func updatingExpiration(_ newExpiresAt: Date?, isPermanent: Bool) -> ContentData {
        ContentData(
            id: id,
            title: title,
            content: content,
            contentType: contentType,
            data: data,
            filePath: filePath,
            fileName: fileName,
            fileSize: fileSize,
            mimeType: mimeType,
            notes: notes,
            tags: tags,
            createdAt: createdAt,
            updatedAt: Date(),
            expiresAt: newExpiresAt,
            isPermanent: isPermanent
        )
    }
}