import CoreData
import Foundation
import Combine
import os.log

// MARK: - Persistence Controller
class PersistenceController: ObservableObject {
    static let shared = PersistenceController()
    
    // MARK: - Core Data Stack
    lazy var container: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "tempBox")
        
        // Configure persistent store description
        let storeDescription = container.persistentStoreDescriptions.first
        storeDescription?.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
        storeDescription?.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)
        
        container.loadPersistentStores { storeDescription, error in
            if let error = error as NSError? {
                Logger.database.error("Core Data error: \(error), \(error.userInfo)")
                fatalError("Unresolved error \(error), \(error.userInfo)")
            }
        }
        
        // Configure view context
        container.viewContext.automaticallyMergesChangesFromParent = true
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        
        return container
    }()
    
    // MARK: - Preview Support
    static var preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        let viewContext = result.container.viewContext
        
        // Create sample data for previews
        createSampleData(in: viewContext)
        
        do {
            try viewContext.save()
        } catch {
            Logger.database.error("Preview data creation failed: \(error)")
        }
        
        return result
    }()
    
    // MARK: - Initializers
    init(inMemory: Bool = false) {
        container = NSPersistentContainer(name: "tempBox")
        
        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        }
        
        container.loadPersistentStores { _, error in
            if let error = error as NSError? {
                Logger.database.error("Core Data error: \(error), \(error.userInfo)")
                fatalError("Unresolved error \(error), \(error.userInfo)")
            }
        }
        
        container.viewContext.automaticallyMergesChangesFromParent = true
    }
    
    // MARK: - Core Data Operations
    func save() {
        let context = container.viewContext
        
        if context.hasChanges {
            do {
                try context.save()
                Logger.database.info("Context saved successfully")
            } catch {
                Logger.database.error("Save error: \(error)")
                let nsError = error as NSError
                fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
            }
        }
    }
    
    func saveContext() throws {
        let context = container.viewContext
        
        if context.hasChanges {
            try context.save()
            Logger.database.info("Context saved successfully")
        }
    }
    
    // MARK: - Background Context Operations
    func performBackgroundTask<T>(_ block: @escaping (NSManagedObjectContext) throws -> T) async throws -> T {
        return try await withCheckedThrowingContinuation { continuation in
            container.performBackgroundTask { context in
                do {
                    let result = try block(context)
                    continuation.resume(returning: result)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - Database Maintenance
    func cleanupExpiredContent() async throws {
        try await performBackgroundTask { context in
            let request: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            request.predicate = NSPredicate(format: "expiresAt != nil AND expiresAt < %@", Date() as NSDate)
            
            let expiredItems = try context.fetch(request)
            Logger.database.info("Found \(expiredItems.count) expired items to cleanup")
            
            for item in expiredItems {
                // Delete associated files
                if let filePath = item.filePath {
                    try? FileManager.default.removeItem(atPath: filePath)
                }
                context.delete(item)
            }
            
            if context.hasChanges {
                try context.save()
                Logger.database.info("Cleanup completed, deleted \(expiredItems.count) items")
            }
        }
    }
    
    func vacuumDatabase() async throws {
        try await performBackgroundTask { context in
            // Force a save to ensure all changes are persisted
            if context.hasChanges {
                try context.save()
            }
            
            // Perform database maintenance
            let coordinator = context.persistentStoreCoordinator
            guard let store = coordinator?.persistentStores.first else {
                throw ContentManagerError.databaseError("No persistent store found")
            }
            
            // Migrate to a new store to compact the database
            let storeURL = store.url!
            let tempURL = storeURL.appendingPathExtension("temp")
            
            try coordinator?.migratePersistentStore(
                store,
                to: tempURL,
                options: nil,
                withType: NSSQLiteStoreType
            )
            
            // Replace the original store with the compacted one
            try FileManager.default.removeItem(at: storeURL)
            try FileManager.default.moveItem(at: tempURL, to: storeURL)
            
            Logger.database.info("Database vacuum completed")
        }
    }
    
    // MARK: - Migration Support
    func migrateIfNeeded() throws {
        let storeURL = container.persistentStoreDescriptions.first?.url
        guard let url = storeURL else {
            throw ContentManagerError.migrationFailed("Store URL not found")
        }
        
        // Check if migration is needed
        let metadata = try NSPersistentStoreCoordinator.metadataForPersistentStore(
            ofType: NSSQLiteStoreType,
            at: url,
            options: nil
        )
        
        let model = container.managedObjectModel
        if !model.isConfiguration(withName: nil, compatibleWithStoreMetadata: metadata) {
            Logger.database.info("Migration needed, starting migration process")
            
            // Perform migration
            _ = NSMigrationManager(sourceModel: model, destinationModel: model)
            // Add migration logic here if needed
            
            Logger.database.info("Migration completed successfully")
        }
    }
    
    // MARK: - Statistics
    func getDatabaseStatistics() async throws -> DatabaseStatistics {
        return try await performBackgroundTask { context in
            let contentRequest: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            let totalItems = try context.count(for: contentRequest)
            
            let textRequest: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            textRequest.predicate = NSPredicate(format: "contentType == %@", ContentType.text.rawValue)
            let textItems = try context.count(for: textRequest)
            
            let imageRequest: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            imageRequest.predicate = NSPredicate(format: "contentType == %@", ContentType.image.rawValue)
            let imageItems = try context.count(for: imageRequest)
            
            let fileRequest: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            fileRequest.predicate = NSPredicate(format: "contentType == %@", ContentType.file.rawValue)
            let fileItems = try context.count(for: fileRequest)
            
            let tagRequest: NSFetchRequest<Tag> = Tag.fetchRequest()
            let totalTags = try context.count(for: tagRequest)
            
            // Calculate total file size
            let sizeRequest: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            let items = try context.fetch(sizeRequest)
            let totalSize = items.reduce(0) { $0 + $1.fileSize }
            
            return DatabaseStatistics(
                totalItems: totalItems,
                textItems: textItems,
                imageItems: imageItems,
                fileItems: fileItems,
                totalTags: totalTags,
                totalSize: totalSize
            )
        }
    }
    
    // MARK: - Sample Data Creation
    private static func createSampleData(in context: NSManagedObjectContext) {
        // Create sample tags
        let workTag = Tag(context: context)
        workTag.id = UUID()
        workTag.name = "工作"
        workTag.color = "blue"
        
        let studyTag = Tag(context: context)
        studyTag.id = UUID()
        studyTag.name = "学习"
        studyTag.color = "green"
        
        let ideaTag = Tag(context: context)
        ideaTag.id = UUID()
        ideaTag.name = "想法"
        ideaTag.color = "purple"
        
        // Create sample content items
        let textItem = ContentItem(context: context)
        textItem.id = UUID()
        textItem.title = "会议记录"
        textItem.content = "今天的会议讨论了项目进度和下一步计划..."
        textItem.contentType = ContentType.text.rawValue
        textItem.createdAt = Date()
        textItem.updatedAt = Date()
        textItem.fileSize = 100
        textItem.isPermanent = false
        textItem.addToTags(workTag)
        
        let imageItem = ContentItem(context: context)
        imageItem.id = UUID()
        imageItem.title = "屏幕截图"
        imageItem.contentType = ContentType.image.rawValue
        imageItem.fileName = "screenshot.png"
        imageItem.createdAt = Date().addingTimeInterval(-3600)
        imageItem.updatedAt = Date().addingTimeInterval(-3600)
        imageItem.fileSize = 2048
        imageItem.isPermanent = true
        imageItem.addToTags(studyTag)
        
        let fileItem = ContentItem(context: context)
        fileItem.id = UUID()
        fileItem.title = "项目文档"
        fileItem.contentType = ContentType.file.rawValue
        fileItem.fileName = "project.pdf"
        fileItem.createdAt = Date().addingTimeInterval(-7200)
        fileItem.updatedAt = Date().addingTimeInterval(-7200)
        fileItem.fileSize = 5120
        fileItem.isPermanent = false
        fileItem.expiresAt = Date().addingTimeInterval(86400 * 7) // 7 days
        fileItem.addToTags(workTag)
        fileItem.addToTags(ideaTag)
        
        // Create app settings
        let settings = AppSettings(context: context)
        settings.id = UUID()
        settings.defaultStorageMode = "temporary"
        settings.globalHotkey = "cmd+shift+v"
        settings.autoCleanupEnabled = true
        settings.cleanupInterval = 30
    }
}

// MARK: - Database Statistics
struct DatabaseStatistics {
    let totalItems: Int
    let textItems: Int
    let imageItems: Int
    let fileItems: Int
    let totalTags: Int
    let totalSize: Int64
    
    var formattedTotalSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useBytes, .useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: totalSize)
    }
}

// MARK: - Logger Extension
extension Logger {
    static let database = Logger(subsystem: "com.contentmanager", category: "database")
    static let fileOperations = Logger(subsystem: "com.contentmanager", category: "files")
    static let userInterface = Logger(subsystem: "com.contentmanager", category: "ui")
    static let pasteboard = Logger(subsystem: "com.contentmanager", category: "pasteboard")
    static let hotkeys = Logger(subsystem: "com.contentmanager", category: "hotkeys")
}