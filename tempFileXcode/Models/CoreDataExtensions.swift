import CoreData
import Foundation
import SwiftUI

// MARK: - ContentItem Extensions
extension ContentItem {
    
    // MARK: - Convenience Initializers
    convenience init(from contentData: ContentData, context: NSManagedObjectContext) {
        self.init(context: context)
        
        self.id = contentData.id
        self.title = contentData.title
        self.content = contentData.content
        self.contentType = contentData.contentType.rawValue
        self.filePath = contentData.filePath
        self.fileName = contentData.fileName
        self.fileSize = contentData.fileSize
        self.mimeType = contentData.mimeType
        self.notes = contentData.notes
        self.createdAt = contentData.createdAt
        self.updatedAt = contentData.updatedAt
        self.expiresAt = contentData.expiresAt
        self.isPermanent = contentData.isPermanent
        
        // Add tags
        for tagName in contentData.tags {
            let tag = Tag.findOrCreate(name: tagName, in: context)
            self.addToTags(tag)
        }
    }
    
    // MARK: - Computed Properties
    var contentTypeEnum: ContentType {
        get {
            return ContentType(rawValue: contentType ?? "") ?? .text
        }
        set {
            contentType = newValue.rawValue
        }
    }

    var tagNames: [String] {
        return (tags?.allObjects as? [Tag])?.compactMap { $0.name } ?? []
    }
    
    var displayTitle: String {
        if let title = title, !title.isEmpty {
            return title
        }
        
        if let fileName = fileName {
            return fileName
        }
        
        if let content = content, !content.isEmpty {
            // 性能优化：避免对长文本做 prefix 操作
            if content.count <= 50 {
                return content
            } else {
                let endIndex = content.index(content.startIndex, offsetBy: 50)
                return String(content[..<endIndex])
            }
        }
        
        return "Untitled \(contentTypeEnum.displayName)"
    }
    
    var formattedFileSize: String {
        // 性能优化：使用静态 formatter
        return Self.fileSizeFormatter.string(fromByteCount: fileSize)
    }
    
    // 静态 formatter 避免重复创建
    private static let fileSizeFormatter: ByteCountFormatter = {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useBytes, .useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter
    }()
    
    var formattedCreatedDate: String {
        guard let createdAt = createdAt else { return "" }
        // 性能优化：使用静态 formatter
        return Self.dateFormatter.localizedString(for: createdAt, relativeTo: Date())
    }
    
    // 静态 date formatter
    private static let dateFormatter: RelativeDateTimeFormatter = {
        let formatter = RelativeDateTimeFormatter()
        formatter.dateTimeStyle = .named
        return formatter
    }()
    
    var isExpired: Bool {
        guard let expiresAt = expiresAt else { return false }
        return Date() > expiresAt
    }
    
    var remainingTime: TimeInterval? {
        guard let expiresAt = expiresAt else { return nil }
        let remaining = expiresAt.timeIntervalSinceNow
        return remaining > 0 ? remaining : 0
    }
    
    var formattedRemainingTime: String? {
        guard let remaining = remainingTime else { return nil }
        
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.day, .hour, .minute]
        formatter.unitsStyle = .abbreviated
        formatter.maximumUnitCount = 2
        
        return formatter.string(from: remaining)
    }
    
    // MARK: - Conversion Methods
    func toContentData() -> ContentData {
        return ContentData(
            id: id ?? UUID(),
            title: title,
            content: content,
            contentType: contentTypeEnum,
            data: nil, // Data is stored separately
            filePath: filePath,
            fileName: fileName,
            fileSize: fileSize,
            mimeType: mimeType,
            notes: notes,
            tags: tagNames,
            createdAt: createdAt ?? Date(),
            updatedAt: updatedAt ?? Date(),
            expiresAt: expiresAt,
            isPermanent: isPermanent
        )
    }
    
    // MARK: - Update Methods
    func updateFrom(_ contentData: ContentData) {
        self.title = contentData.title
        self.content = contentData.content
        self.notes = contentData.notes
        self.updatedAt = Date()
        self.expiresAt = contentData.expiresAt
        self.isPermanent = contentData.isPermanent
        
        // Update tags
        if let currentTags = tags?.allObjects as? [Tag] {
            for tag in currentTags {
                self.removeFromTags(tag)
            }
        }
        
        guard let context = managedObjectContext else { return }
        for tagName in contentData.tags {
            let tag = Tag.findOrCreate(name: tagName, in: context)
            self.addToTags(tag)
        }
    }
    
    // MARK: - File Operations
    func getFileURL() -> URL? {
        guard let filePath = filePath else { return nil }
        return URL(fileURLWithPath: filePath)
    }
    
    func loadFileData() throws -> Data? {
        guard let url = getFileURL() else { return nil }
        return try Data(contentsOf: url)
    }
    
    // MARK: - 性能优化：获取完整文本内容
    func getFullTextContent() throws -> String? {
        // 如果是小文本，直接返回数据库中的内容
        if let content = content, !content.contains("...[更多字符]") {
            return content
        }
        
        // 对于大文本，从文件中读取完整内容
        guard let data = try loadFileData(),
              let fullContent = String(data: data, encoding: .utf8) else {
            return content // 退回到数据库中的摘要
        }
        
        return fullContent
    }
    
    // MARK: - Fetch Requests
    static func allItemsFetchRequest() -> NSFetchRequest<ContentItem> {
        let request = ContentItem.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)]
        return request
    }

    static func itemsByTypeFetchRequest(_ type: ContentType) -> NSFetchRequest<ContentItem> {
        let request = ContentItem.fetchRequest()
        request.predicate = NSPredicate(format: "contentType == %@", type.rawValue)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)]
        return request
    }

    static func expiredItemsFetchRequest() -> NSFetchRequest<ContentItem> {
        let request = ContentItem.fetchRequest()
        request.predicate = NSPredicate(format: "expiresAt != nil AND expiresAt < %@", Date() as NSDate)
        return request
    }

    static func searchFetchRequest(query: String) -> NSFetchRequest<ContentItem> {
        let request = ContentItem.fetchRequest()
        let titlePredicate = NSPredicate(format: "title CONTAINS[cd] %@", query)
        let contentPredicate = NSPredicate(format: "content CONTAINS[cd] %@", query)
        let notesPredicate = NSPredicate(format: "notes CONTAINS[cd] %@", query)
        let fileNamePredicate = NSPredicate(format: "fileName CONTAINS[cd] %@", query)

        request.predicate = NSCompoundPredicate(orPredicateWithSubpredicates: [
            titlePredicate, contentPredicate, notesPredicate, fileNamePredicate
        ])
        request.sortDescriptors = [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)]
        return request
    }
}

// MARK: - Tag Extensions
extension Tag {
    
    // MARK: - Convenience Methods
    static func findOrCreate(name: String, in context: NSManagedObjectContext) -> Tag {
        let request: NSFetchRequest<Tag> = Tag.fetchRequest()
        request.predicate = NSPredicate(format: "name == %@", name)
        request.fetchLimit = 1
        
        if let existingTag = try? context.fetch(request).first {
            return existingTag
        }
        
        let newTag = Tag(context: context)
        newTag.id = UUID()
        newTag.name = name
        newTag.color = defaultColorForTag(name)
        
        return newTag
    }
    
    private static func defaultColorForTag(_ name: String) -> String {
        let colors = Color.availableTagColors
        let index = abs(name.hashValue) % colors.count
        return colors[index]
    }
    
    // MARK: - Computed Properties
    var contentItemsCount: Int {
        return contentItems?.count ?? 0
    }
    
    var sortedContentItems: [ContentItem] {
        let items = contentItems?.allObjects as? [ContentItem] ?? []
        return items.sorted { ($0.createdAt ?? Date.distantPast) > ($1.createdAt ?? Date.distantPast) }
    }
    
    // MARK: - Fetch Requests
    static func allTagsFetchRequest() -> NSFetchRequest<Tag> {
        let request = Tag.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(keyPath: \Tag.name, ascending: true)]
        return request
    }

    static func popularTagsFetchRequest(limit: Int = 10) -> NSFetchRequest<Tag> {
        let request = Tag.fetchRequest()
        // Sort by relationship count using string-based key path for Core Data aggregate functions
        request.sortDescriptors = [NSSortDescriptor(key: "contentItems.@count", ascending: false)]
        request.fetchLimit = limit
        return request
    }
}

// MARK: - AppSettings Extensions
extension AppSettings {
    
    // MARK: - Singleton Access
    static func current(in context: NSManagedObjectContext) -> AppSettings {
        let request: NSFetchRequest<AppSettings> = AppSettings.fetchRequest()
        request.fetchLimit = 1
        
        if let settings = try? context.fetch(request).first {
            return settings
        }
        
        // Create default settings
        let settings = AppSettings(context: context)
        settings.id = UUID()
        settings.defaultStorageMode = "temporary"
        settings.globalHotkey = "cmd+shift+v"
        settings.autoCleanupEnabled = true
        settings.cleanupInterval = 30
        
        return settings
    }
    
    // MARK: - Computed Properties
    var defaultStorageModeEnum: StorageMode {
        get {
            return StorageMode(rawValue: defaultStorageMode ?? "temporary") ?? .temporary
        }
        set {
            defaultStorageMode = newValue.rawValue
        }
    }
    
    var defaultExportURL: URL? {
        guard let path = defaultExportPath else { return nil }
        return URL(fileURLWithPath: path)
    }
    
    // MARK: - Fetch Requests
    // Note: fetchRequest() is automatically generated by Core Data
}

// MARK: - Storage Mode Enum
enum StorageMode: String, CaseIterable {
    case temporary = "temporary"
    case permanent = "permanent"
    
    var displayName: String {
        switch self {
        case .temporary:
            return "临时存储"
        case .permanent:
            return "永久存储"
        }
    }
    
    var description: String {
        switch self {
        case .temporary:
            return "内容将在设定时间后自动删除"
        case .permanent:
            return "内容将永久保存，直到手动删除"
        }
    }
}

// MARK: - Core Data Utilities
extension NSManagedObjectContext {
    
    func saveIfNeeded() throws {
        if hasChanges {
            try save()
        }
    }
    
    func performAndWait<T>(_ block: () throws -> T) throws -> T {
        var result: Result<T, Error>!
        performAndWait {
            result = Result { try block() }
        }
        return try result.get()
    }
}