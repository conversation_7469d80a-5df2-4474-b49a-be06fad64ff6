import Foundation

// MARK: - Content Type Enumeration
enum ContentType: String, CaseIterable, Codable {
    case text = "text"
    case image = "image"
    case file = "file"
    
    var displayName: String {
        switch self {
        case .text:
            return "文字"
        case .image:
            return "图片"
        case .file:
            return "文件"
        }
    }
    
    var systemImage: String {
        switch self {
        case .text:
            return "doc.text"
        case .image:
            return "photo"
        case .file:
            return "doc"
        }
    }
    
    var color: String {
        switch self {
        case .text:
            return "blue"
        case .image:
            return "green"
        case .file:
            return "orange"
        }
    }
    
    // MARK: - Content Type Detection
    static func detectType(from data: Data) -> ContentType {
        // Check for image data
        if isImageData(data) {
            return .image
        }
        
        // Check for text data
        if isTextData(data) {
            return .text
        }
        
        // Default to file
        return .file
    }
    
    static func detectType(from url: URL) -> ContentType {
        let pathExtension = url.pathExtension.lowercased()
        
        // Image extensions
        let imageExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "tiff", "webp", "heic", "heif"]
        if imageExtensions.contains(pathExtension) {
            return .image
        }
        
        // Text extensions
        let textExtensions = ["txt", "md", "rtf", "html", "xml", "json", "csv", "log"]
        if textExtensions.contains(pathExtension) {
            return .text
        }
        
        // Default to file
        return .file
    }
    
    static func detectType(from string: String) -> ContentType {
        // Always return text for string content
        return .text
    }
    
    // MARK: - Private Helper Methods
    private static func isImageData(_ data: Data) -> Bool {
        guard data.count >= 4 else { return false }
        
        let bytes = data.prefix(4)
        let header = bytes.withUnsafeBytes { $0.load(as: UInt32.self) }
        
        // Check common image file signatures
        switch header {
        case 0x474E5089: // PNG
            return true
        case 0xE0FFD8FF, 0xE1FFD8FF, 0xE2FFD8FF, 0xE3FFD8FF: // JPEG
            return true
        case 0x38464947: // GIF
            return true
        case 0x002A4949, 0x2A004D4D: // TIFF
            return true
        default:
            return false
        }
    }
    
    private static func isTextData(_ data: Data) -> Bool {
        // Try to decode as UTF-8 text
        return String(data: data, encoding: .utf8) != nil
    }
}