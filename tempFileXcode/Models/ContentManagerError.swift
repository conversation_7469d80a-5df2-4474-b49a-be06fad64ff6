import Foundation

// MARK: - Content Manager Error Types
enum ContentManagerError: LocalizedError, Equatable {
    // Database Errors
    case databaseError(String)
    case dataCorruption(String)
    case migrationFailed(String)
    
    // File Operation Errors
    case fileOperationError(String)
    case fileNotFound(String)
    case fileAccessDenied(String)
    case insufficientStorage(String)
    case invalidFileFormat(String)
    
    // Export Errors
    case exportError(String)
    case exportCancelled
    case exportPathInvalid(String)
    
    // Pasteboard Errors
    case pasteboardError(String)
    case pasteboardEmpty
    case unsupportedPasteboardType(String)
    
    // Hotkey Registration Errors
    case hotkeyRegistrationError(String)
    case hotkeyConflict(String)
    case hotkeyPermissionDenied
    
    // Content Validation Errors
    case invalidContent(String)
    case contentTooLarge(String)
    case unsupportedContentType(String)
    
    // Batch Management Errors
    case noBatchAvailable
    case batchNotFound(String)
    
    // Network Errors (for future use)
    case networkError(String)
    case networkTimeout
    case networkUnavailable
    
    // General Errors
    case unknown(String)
    case operationCancelled
    case permissionDenied(String)
    
    // MARK: - Error Descriptions
    var errorDescription: String? {
        switch self {
        // Database Errors
        case .databaseError(let message):
            return "数据库错误: \(message)"
        case .dataCorruption(let message):
            return "数据损坏: \(message)"
        case .migrationFailed(let message):
            return "数据迁移失败: \(message)"
            
        // File Operation Errors
        case .fileOperationError(let message):
            return "文件操作错误: \(message)"
        case .fileNotFound(let path):
            return "文件未找到: \(path)"
        case .fileAccessDenied(let path):
            return "文件访问被拒绝: \(path)"
        case .insufficientStorage(let message):
            return "存储空间不足: \(message)"
        case .invalidFileFormat(let format):
            return "不支持的文件格式: \(format)"
            
        // Export Errors
        case .exportError(let message):
            return "导出错误: \(message)"
        case .exportCancelled:
            return "导出已取消"
        case .exportPathInvalid(let path):
            return "导出路径无效: \(path)"
            
        // Pasteboard Errors
        case .pasteboardError(let message):
            return "剪贴板错误: \(message)"
        case .pasteboardEmpty:
            return "剪贴板为空"
        case .unsupportedPasteboardType(let type):
            return "不支持的剪贴板类型: \(type)"
            
        // Hotkey Registration Errors
        case .hotkeyRegistrationError(let message):
            return "快捷键注册错误: \(message)"
        case .hotkeyConflict(let hotkey):
            return "快捷键冲突: \(hotkey)"
        case .hotkeyPermissionDenied:
            return "快捷键权限被拒绝，请在系统偏好设置中授权"
            
        // Content Validation Errors
        case .invalidContent(let message):
            return "内容无效: \(message)"
        case .contentTooLarge(let size):
            return "内容过大: \(size)"
        case .unsupportedContentType(let type):
            return "不支持的内容类型: \(type)"
            
        // Batch Management Errors
        case .noBatchAvailable:
            return "没有可用的批次，请先创建一个批次"
        case .batchNotFound(let batchId):
            return "批次未找到: \(batchId)"
            
        // Network Errors
        case .networkError(let message):
            return "网络错误: \(message)"
        case .networkTimeout:
            return "网络超时"
        case .networkUnavailable:
            return "网络不可用"
            
        // General Errors
        case .unknown(let message):
            return "未知错误: \(message)"
        case .operationCancelled:
            return "操作已取消"
        case .permissionDenied(let message):
            return "权限被拒绝: \(message)"
        }
    }
    
    // MARK: - Recovery Suggestions
    var recoverySuggestion: String? {
        switch self {
        case .databaseError, .dataCorruption:
            return "请尝试重启应用，如果问题持续存在，可能需要重置数据库。"
        case .fileAccessDenied:
            return "请检查文件权限，或选择其他位置保存文件。"
        case .insufficientStorage:
            return "请清理磁盘空间后重试。"
        case .hotkeyPermissionDenied:
            return "请在系统偏好设置 > 安全性与隐私 > 辅助功能中授权此应用。"
        case .pasteboardEmpty:
            return "请先复制一些内容到剪贴板。"
        case .networkUnavailable:
            return "请检查网络连接后重试。"
        case .exportPathInvalid:
            return "请选择一个有效的导出路径。"
        default:
            return "请重试操作，如果问题持续存在，请联系技术支持。"
        }
    }
    
    // MARK: - Error Codes
    var errorCode: Int {
        switch self {
        case .databaseError: return 1001
        case .dataCorruption: return 1002
        case .migrationFailed: return 1003
        case .fileOperationError: return 2001
        case .fileNotFound: return 2002
        case .fileAccessDenied: return 2003
        case .insufficientStorage: return 2004
        case .invalidFileFormat: return 2005
        case .exportError: return 3001
        case .exportCancelled: return 3002
        case .exportPathInvalid: return 3003
        case .pasteboardError: return 4001
        case .pasteboardEmpty: return 4002
        case .unsupportedPasteboardType: return 4003
        case .hotkeyRegistrationError: return 5001
        case .hotkeyConflict: return 5002
        case .hotkeyPermissionDenied: return 5003
        case .invalidContent: return 6001
        case .contentTooLarge: return 6002
        case .unsupportedContentType: return 6003
        case .noBatchAvailable: return 6004
        case .batchNotFound: return 6005
        case .networkError: return 7001
        case .networkTimeout: return 7002
        case .networkUnavailable: return 7003
        case .unknown: return 9001
        case .operationCancelled: return 9002
        case .permissionDenied: return 9003
        }
    }
    
    // MARK: - Severity Levels
    enum Severity {
        case low      // User can continue working
        case medium   // Feature unavailable but app functional
        case high     // App functionality significantly impacted
        case critical // App may crash or data loss possible
    }
    
    var severity: Severity {
        switch self {
        case .operationCancelled, .exportCancelled, .pasteboardEmpty:
            return .low
        case .hotkeyRegistrationError, .hotkeyConflict, .unsupportedPasteboardType, .unsupportedContentType:
            return .medium
        case .fileOperationError, .exportError, .pasteboardError, .networkError:
            return .medium
        case .fileAccessDenied, .insufficientStorage, .hotkeyPermissionDenied:
            return .high
        case .databaseError, .dataCorruption, .migrationFailed:
            return .critical
        default:
            return .medium
        }
    }
    
    // MARK: - User-Facing Error Information
    struct ErrorInfo {
        let title: String
        let message: String
        let suggestion: String?
        let severity: Severity
        let canRetry: Bool
    }
    
    var errorInfo: ErrorInfo {
        return ErrorInfo(
            title: localizedTitle,
            message: errorDescription ?? "发生了未知错误",
            suggestion: recoverySuggestion,
            severity: severity,
            canRetry: canRetry
        )
    }
    
    private var localizedTitle: String {
        switch self {
        case .databaseError, .dataCorruption, .migrationFailed:
            return "数据库错误"
        case .fileOperationError, .fileNotFound, .fileAccessDenied, .insufficientStorage, .invalidFileFormat:
            return "文件错误"
        case .exportError, .exportCancelled, .exportPathInvalid:
            return "导出错误"
        case .pasteboardError, .pasteboardEmpty, .unsupportedPasteboardType:
            return "剪贴板错误"
        case .hotkeyRegistrationError, .hotkeyConflict, .hotkeyPermissionDenied:
            return "快捷键错误"
        case .invalidContent, .contentTooLarge, .unsupportedContentType, .noBatchAvailable, .batchNotFound:
            return "内容错误"
        case .networkError, .networkTimeout, .networkUnavailable:
            return "网络错误"
        case .unknown, .operationCancelled, .permissionDenied:
            return "系统错误"
        }
    }
    
    private var canRetry: Bool {
        switch self {
        case .operationCancelled, .exportCancelled, .dataCorruption:
            return false
        case .networkTimeout, .networkUnavailable, .pasteboardEmpty:
            return true
        case .fileAccessDenied, .hotkeyPermissionDenied:
            return false
        default:
            return true
        }
    }
}