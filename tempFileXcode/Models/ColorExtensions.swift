import SwiftUI

// MARK: - Color Extensions for Tag Colors
extension Color {
    
    /// 根据颜色字符串返回对应的系统颜色
    init(tagColor colorName: String) {
        switch colorName.lowercased() {
        case "blue":
            self = .blue
        case "green":
            self = .green
        case "red":
            self = .red
        case "orange":
            self = .orange
        case "purple":
            self = .purple
        case "pink":
            self = .pink
        case "yellow":
            self = .yellow
        case "gray", "grey":
            self = .gray
        case "cyan":
            self = .cyan
        case "mint":
            if #available(macOS 12.0, *) {
                self = .mint
            } else {
                self = .cyan
            }
        case "teal":
            if #available(macOS 12.0, *) {
                self = .teal
            } else {
                self = .cyan
            }
        case "indigo":
            if #available(macOS 12.0, *) {
                self = .indigo
            } else {
                self = .purple
            }
        default:
            // 默认使用蓝色
            self = .blue
        }
    }
    
    /// 获取所有可用的标签颜色
    static var availableTagColors: [String] {
        if #available(macOS 12.0, *) {
            return ["blue", "green", "red", "orange", "purple", "pink", "yellow", "gray", "cyan", "mint", "teal", "indigo"]
        } else {
            return ["blue", "green", "red", "orange", "purple", "pink", "yellow", "gray", "cyan"]
        }
    }
    
    /// 获取颜色名称的本地化显示名称
    static func displayName(for colorName: String) -> String {
        switch colorName.lowercased() {
        case "blue":
            return "蓝色"
        case "green":
            return "绿色"
        case "red":
            return "红色"
        case "orange":
            return "橙色"
        case "purple":
            return "紫色"
        case "pink":
            return "粉色"
        case "yellow":
            return "黄色"
        case "gray", "grey":
            return "灰色"
        case "cyan":
            return "青色"
        case "mint":
            return "薄荷色"
        case "teal":
            return "青绿色"
        case "indigo":
            return "靛蓝色"
        default:
            return colorName.capitalized
        }
    }
}