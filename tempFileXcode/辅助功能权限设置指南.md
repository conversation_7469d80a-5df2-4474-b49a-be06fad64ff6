# 辅助功能权限设置指南

## 问题描述
应用需要辅助功能权限来实现全局快捷键功能，但在系统设置中可能找不到应用选项。

## 解决方案

### 方法一：通过应用内设置
1. 打开应用
2. 进入"设置"页面
3. 在"全局快捷键"部分，点击"请求权限"按钮
4. 按照弹窗指引操作

### 方法二：手动添加到系统设置
1. 打开"系统设置"（macOS Ventura及以上）或"系统偏好设置"（较早版本）
2. 点击"隐私与安全性"
3. 在左侧列表中找到"辅助功能"
4. 点击右侧的"+"按钮
5. 导航到应用程序位置：
   ```
   ~/Library/Developer/Xcode/DerivedData/tempFileXcode-*/Build/Products/Debug/tempFileXcode.app
   ```
6. 选择应用并添加
7. 确保应用旁边的开关是开启状态

### 方法三：使用终端命令查找应用路径
```bash
# 查找应用路径
find ~/Library/Developer/Xcode/DerivedData -name "tempFileXcode.app" -type d 2>/dev/null

# 或者使用更精确的搜索
find ~/Library/Developer/Xcode/DerivedData -path "*/Build/Products/Debug/tempFileXcode.app" 2>/dev/null
```

### 方法四：重置权限数据库（高级用户）
如果应用仍然无法获得权限，可以尝试重置权限数据库：
```bash
# 重置辅助功能权限数据库（需要重启）
sudo tccutil reset Accessibility
```

## 验证权限设置
1. 在应用的设置页面查看"辅助功能权限"状态
2. 状态应显示为"已授权"（绿色）
3. 尝试使用全局快捷键 ⌘⇧V

## 常见问题

### Q: 为什么在辅助功能列表中找不到应用？
A: 这通常是因为应用是开发版本，系统可能无法自动识别。需要手动添加应用路径。

### Q: 添加后仍然提示需要权限？
A: 请确保：
- 应用旁边的开关是开启状态
- 重启应用
- 如果仍有问题，尝试移除后重新添加

### Q: 应用界面卡住了怎么办？
A: 新版本已修复此问题。权限检查现在在后台进行，不会阻塞界面。

## 技术说明
- 应用使用 Carbon 框架实现全局快捷键
- 需要辅助功能权限来监听系统级键盘事件
- 权限检查已优化，避免启动时立即弹窗
- 支持手动请求权限，提供详细的设置指引
