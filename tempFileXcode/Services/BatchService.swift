import Foundation
import CoreData
import Combine
import os.log

// MARK: - Batch Service
class BatchService: ObservableObject {
    @Published var currentBatch: NSManagedObject?
    @Published var activeBatch: NSManagedObject? // 活跃批次，用于拖拽文件自动添加
    @Published var batches: [NSManagedObject] = []

    private let persistenceController: PersistenceController
    private let logger = Logger(subsystem: "com.contentmanager.app", category: "BatchService")

    init(persistenceController: PersistenceController? = nil) {
        self.persistenceController = persistenceController ?? PersistenceController.shared
        loadBatches()
        createDefaultBatchIfNeeded()
        loadActiveBatchFromUserDefaults()
    }
    
    // MARK: - Batch Management
    func createNewBatch(name: String? = nil, notes: String? = nil) throws -> NSManagedObject {
        let context = persistenceController.container.viewContext
        
        var newBatch: NSManagedObject!
        
        try context.performAndWait {
            let batch = NSEntityDescription.insertNewObject(forEntityName: "Batch", into: context)
            
            let batchName = name ?? generateBatchName()
            batch.setValue(UUID(), forKey: "id")
            batch.setValue(batchName, forKey: "name")
            batch.setValue(notes, forKey: "notes")
            batch.setValue(Date(), forKey: "createdAt")
            batch.setValue(Date(), forKey: "updatedAt")
            
            try context.save()
            newBatch = batch
            logger.info("Created new batch: \(batchName)")
        }
        
        // 在主线程更新UI
        DispatchQueue.main.async {
            self.batches.insert(newBatch, at: 0)
            // 新创建的批次自动设置为活跃批次
            self.setActiveBatch(newBatch)
        }
        
        return newBatch
    }

    func setCurrentBatch(_ batch: NSManagedObject) {
        currentBatch = batch
        logger.info("Set current batch")
    }
    
    // MARK: - Active Batch Management
    func setActiveBatch(_ batch: NSManagedObject) {
        activeBatch = batch
        saveActiveBatchToUserDefaults(batch)
        logger.info("Set active batch: \(batch.value(forKey: "name") as? String ?? "unknown")")
    }
    
    func getActiveBatch() -> NSManagedObject? {
        return activeBatch
    }
    
    // 确保有活跃批次
    private func ensureActiveBatch() {
        logger.info("ensureActiveBatch called - activeBatch: \(self.activeBatch?.value(forKey: "name") as? String ?? "nil"), batches.count: \(self.batches.count)")

        if activeBatch == nil && !batches.isEmpty {
            // 优先选择当前批次作为活跃批次
            if let currentBatch = currentBatch {
                activeBatch = currentBatch
                let batchName = currentBatch.value(forKey: "name") as? String ?? "未知批次"
                logger.info("Set current batch as active batch: \(batchName)")
                print("✅ 设置当前批次为活跃批次: \(batchName)")
            } else {
                // 如果没有当前批次，选择第一个批次
                activeBatch = batches.first
                let batchName = batches.first?.value(forKey: "name") as? String ?? "未知批次"
                logger.info("Set first batch as active batch: \(batchName)")
                print("✅ 设置第一个批次为活跃批次: \(batchName)")
            }
        } else if activeBatch != nil {
            let batchName = activeBatch?.value(forKey: "name") as? String ?? "未知批次"
            logger.info("Active batch already exists: \(batchName)")
            print("ℹ️ 活跃批次已存在: \(batchName)")
        }
    }

    func deleteBatch(_ batch: NSManagedObject) throws {
        let context = persistenceController.container.viewContext
        
        // 如果是当前批次，清空当前批次
        if currentBatch == batch {
            currentBatch = nil
        }
        
        // 如果是活跃批次，需要重新设置活跃批次
        if activeBatch == batch {
            clearActiveBatchFromUserDefaults()
            activeBatch = nil
            // 删除后自动设置第一个批次为活跃批次
            if let firstBatch = batches.first(where: { $0 != batch }) {
                setActiveBatch(firstBatch)
            }
        }
        
        // 从数组中移除
        if let index = batches.firstIndex(of: batch) {
            batches.remove(at: index)
        }
        
        context.delete(batch)
        try context.save()
        
        logger.info("Deleted batch")
    }

    func updateBatch(_ batch: NSManagedObject, name: String? = nil, notes: String? = nil) throws {
        let context = persistenceController.container.viewContext
        
        if let name = name {
            batch.setValue(name, forKey: "name")
        }
        if let notes = notes {
            batch.setValue(notes, forKey: "notes")
        }
        batch.setValue(Date(), forKey: "updatedAt")
        
        try context.save()
        logger.info("Updated batch")
    }

    // MARK: - Content Management
    func addContentToBatch(_ contentItem: NSManagedObject, batch: NSManagedObject? = nil) throws {
        let targetBatch = batch ?? currentBatch
        guard let targetBatch = targetBatch else {
            throw ContentManagerError.noBatchAvailable
        }
        
        contentItem.setValue(targetBatch, forKey: "batch")
        
        let context = persistenceController.container.viewContext
        try context.save()
        
        logger.info("Added content to batch")
    }
    
    // 添加内容到活跃批次（用于拖拽文件）
    func addContentToActiveBatch(_ contentItem: NSManagedObject) throws {
        guard let activeBatch = activeBatch else {
            throw ContentManagerError.noBatchAvailable
        }
        
        contentItem.setValue(activeBatch, forKey: "batch")
        
        let context = persistenceController.container.viewContext
        try context.save()
        
        logger.info("Added content to active batch: \(activeBatch.value(forKey: "name") as? String ?? "unknown")")
    }

    func moveContentToBatch(_ contentItem: NSManagedObject, toBatch: NSManagedObject) throws {
        contentItem.setValue(toBatch, forKey: "batch")
        
        let context = persistenceController.container.viewContext
        try context.save()
        
        logger.info("Moved content to batch")
    }
    
    // MARK: - Private Methods
    private func loadBatches() {
        let context = persistenceController.container.viewContext
        let request = NSFetchRequest<NSManagedObject>(entityName: "Batch")
        
        // 按创建时间降序排列
        let sortDescriptor = NSSortDescriptor(key: "createdAt", ascending: false)
        request.sortDescriptors = [sortDescriptor]
        
        do {
            self.batches = try context.fetch(request)
            logger.info("Loaded \(self.batches.count) batches")
        } catch {
            logger.error("Failed to load batches: \(error)")
            self.batches = []
        }
    }

    private func createDefaultBatchIfNeeded() {
        if batches.isEmpty {
            do {
                let defaultBatch = try createDefaultBatch()
                currentBatch = defaultBatch
                activeBatch = defaultBatch  // 默认批次也设置为活跃批次
                logger.info("Created default batch and set as active")
            } catch {
                logger.error("Failed to create default batch: \(error)")
            }
        } else {
            // 如果有批次但没有当前批次，设置第一个为当前批次
            if currentBatch == nil {
                currentBatch = batches.first
            }
            // 确保活跃批次设置
            ensureActiveBatch()
        }
    }

    private func createDefaultBatch() throws -> NSManagedObject {
        return try createNewBatch(name: "默认批次", notes: "自动创建的默认批次")
    }

    private func generateBatchName() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return "批次 \(formatter.string(from: Date()))"
    }
    
    // MARK: - 活跃批次持久化
    private func saveActiveBatchToUserDefaults(_ batch: NSManagedObject) {
        guard let objectID = batch.objectID.uriRepresentation().absoluteString.data(using: .utf8) else {
            logger.error("Failed to save active batch: unable to get object ID")
            return
        }
        
        UserDefaults.standard.set(objectID, forKey: "ActiveBatchObjectID")
        UserDefaults.standard.synchronize()
        
        let batchName = batch.value(forKey: "name") as? String ?? "未知批次"
        logger.info("✅ 已保存活跃批次到UserDefaults: \(batchName)")
    }
    
    private func loadActiveBatchFromUserDefaults() {
        guard let objectIDData = UserDefaults.standard.data(forKey: "ActiveBatchObjectID"),
              let objectIDString = String(data: objectIDData, encoding: .utf8),
              let objectIDURL = URL(string: objectIDString) else {
            logger.info("ℹ️ 没有找到已保存的活跃批次")
            return
        }
        
        let context = persistenceController.container.viewContext
        guard let coordinator = context.persistentStoreCoordinator,
              let objectID = coordinator.managedObjectID(forURIRepresentation: objectIDURL) else {
            logger.error("❌ 无法从URI创建ObjectID")
            clearActiveBatchFromUserDefaults()
            return
        }
        
        do {
            let batch = try context.existingObject(with: objectID)
            
            // 验证批次是否仍然存在于当前批次列表中
            if batches.contains(batch) {
                activeBatch = batch
                let batchName = batch.value(forKey: "name") as? String ?? "未知批次"
                logger.info("✅ 已从UserDefaults恢复活跃批次: \(batchName)")
            } else {
                logger.warning("⚠️ 保存的活跃批次不存在于当前批次列表中，清除保存的设置")
                clearActiveBatchFromUserDefaults()
            }
        } catch {
            logger.error("❌ 无法加载保存的活跃批次: \(error)")
            clearActiveBatchFromUserDefaults()
        }
    }
    
    private func clearActiveBatchFromUserDefaults() {
        UserDefaults.standard.removeObject(forKey: "ActiveBatchObjectID")
        UserDefaults.standard.synchronize()
        logger.info("🗑️ 已清除UserDefaults中的活跃批次设置")
    }
}

// MARK: - Batch Extensions
// 暂时注释掉，等Core Data类生成后再启用
/*
extension Batch {
    var contentItemsArray: [ContentItem] {
        let items = contentItems?.allObjects as? [ContentItem] ?? []
        return items.sorted { ($0.createdAt ?? Date.distantPast) > ($1.createdAt ?? Date.distantPast) }
    }

    var contentCount: Int {
        return contentItems?.count ?? 0
    }

    var displayName: String {
        return name ?? "未命名批次"
    }

    var formattedCreatedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdAt ?? Date())
    }

    // 获取批次中不同类型内容的统计
    var contentTypeStats: [String: Int] {
        var stats: [String: Int] = [:]
        for item in contentItemsArray {
            let type = item.contentType ?? "unknown"
            stats[type, default: 0] += 1
        }
        return stats
    }
}
*/
