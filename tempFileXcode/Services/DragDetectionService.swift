import Foundation
import AppKit
import SwiftUI
import UniformTypeIdentifiers
import Combine
import os.log

/// 简化版全局拖拽检测服务 - 基于Yoink最佳实践
@MainActor
class DragDetectionService: NSObject, ObservableObject {
    
    // MARK: - Properties
    @Published var isDragActive = false
    
    private let logger = Logger(subsystem: "com.contentmanager.app", category: "DragDetectionService")
    private var dragDropManager: DragDropWindowManager?
    private var isMonitoring = false
    
    // 简化的检测状态
    private var globalEventMonitor: Any?
    private var dragStartLocation: NSPoint?
    private var isDragging = false
    
    // 拖拽验证参数
    private var dragStartTime: Date?
    private let minimumDragDistance: CGFloat = 15.0 // 最小拖拽距离（像素）
    private let minimumDragDuration: TimeInterval = 0.2 // 最小拖拽持续时间（秒）
    private var lastDragPasteboardCheck: Date?
    
    // MARK: - Setup
    func setDragDropManager(_ manager: DragDropWindowManager) {
        self.dragDropManager = manager
    }
    
    /// 开始监听全局拖拽事件
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        setupGlobalDragDetection()
        isMonitoring = true
        logger.info("✅ 简化版拖拽检测服务已启动")
    }
    
    /// 停止监听
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        if let monitor = globalEventMonitor {
            NSEvent.removeMonitor(monitor)
            globalEventMonitor = nil
        }
        
        // 重置所有状态
        isMonitoring = false
        isDragging = false
        dragStartLocation = nil
        dragStartTime = nil
        lastDragPasteboardCheck = nil
        
        logger.info("🛑 拖拽检测服务已停止")
    }
    
    // MARK: - 核心拖拽检测机制（简化版）
    private func setupGlobalDragDetection() {
        // 单一的全局事件监听器 - 监听鼠标拖拽事件
        globalEventMonitor = NSEvent.addGlobalMonitorForEvents(matching: [
            .leftMouseDragged,
            .rightMouseDragged,
            .leftMouseUp,
            .rightMouseUp
        ]) { [weak self] event in
            Task { @MainActor in
                self?.handleGlobalMouseEvent(event)
            }
        }
        
        logger.info("🎯 全局拖拽检测已设置完成")
    }
    
    /// 处理全局鼠标事件 - 简化逻辑
    private func handleGlobalMouseEvent(_ event: NSEvent) {
        switch event.type {
        case .leftMouseDragged, .rightMouseDragged:
            handleDragEvent(event)
        case .leftMouseUp, .rightMouseUp:
            handleMouseUpEvent(event)
        default:
            break
        }
    }
    
    /// 处理拖拽事件（增强验证）
    private func handleDragEvent(_ event: NSEvent) {
        let currentLocation = event.locationInWindow
        let now = Date()
        
        // 记录拖拽开始状态
        if !isDragging {
            dragStartLocation = currentLocation
            dragStartTime = now
            isDragging = true
            return // 刚开始拖拽时不检查，避免误触发
        }
        
        // 验证拖拽是否满足最小条件
        guard let startLocation = dragStartLocation,
              let startTime = dragStartTime else { return }
        
        let dragDistance = sqrt(pow(currentLocation.x - startLocation.x, 2) + pow(currentLocation.y - startLocation.y, 2))
        let dragDuration = now.timeIntervalSince(startTime)
        
        // 只有拖拽距离和持续时间都达到阈值才检查文件
        if dragDistance >= minimumDragDistance && dragDuration >= minimumDragDuration {
            // 限制检查频率，避免过度检查
            if lastDragPasteboardCheck == nil || now.timeIntervalSince(lastDragPasteboardCheck!) > 0.2 {
                lastDragPasteboardCheck = now
                checkForActiveFileDragWithValidation()
            }
        }
    }
    
    /// 处理鼠标释放事件
    private func handleMouseUpEvent(_ event: NSEvent) {
        // 重置所有拖拽状态
        isDragging = false
        dragStartLocation = nil
        dragStartTime = nil
        lastDragPasteboardCheck = nil
        
        // 延迟结束拖拽操作，给文件处理留出时间
        if isDragActive {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: {
                self.endDragOperation()
            })
        }
    }
    
    /// 检查当前是否有文件正在被拖拽（最严格验证版）
    private func checkForActiveFileDragWithValidation() {
        // 避免重复触发
        guard !isDragActive else { return }
        
        // 第一步：检查拖拽剪贴板是否存在
        let dragPasteboard = NSPasteboard(name: .drag)
        
        // CRITICAL: 首先检查拖拽剪贴板是否真的有内容
        guard let pasteboardItems = dragPasteboard.pasteboardItems, !pasteboardItems.isEmpty else {
            logger.debug("🔍 拖拽剪贴板为空，忽略")
            return
        }
        
        // 检查是否有实际的文件URL数据
        guard dragPasteboard.canReadItem(withDataConformingToTypes: [UTType.fileURL.identifier]) else {
            logger.debug("🔍 拖拽剪贴板中没有文件URL类型数据")
            return
        }
        
        // 读取并验证文件URLs
        guard let fileURLs = dragPasteboard.readObjects(forClasses: [NSURL.self], options: nil) as? [URL],
              !fileURLs.isEmpty else {
            logger.debug("🔍 无法读取文件URLs或为空")
            return
        }
        
        // 验证文件的真实性和可访问性
        let validFiles = fileURLs.filter { url in
            url.isFileURL && FileManager.default.fileExists(atPath: url.path)
        }
        
        guard !validFiles.isEmpty else {
            logger.debug("🔍 没有找到有效的文件URLs")
            return
        }
        
        // 简单验证 - 确保这是真实的文件拖拽操作
        guard isDefinitelyFileDragOperation(files: validFiles) else {
            logger.debug("🔍 不是真实的文件拖拽操作")
            return
        }
        
        // 通过所有验证，触发拖拽窗口
        logger.info("✅ 验证通过，检测到文件拖拽: \(validFiles.map(\.lastPathComponent))")
        startDragOperation()
    }
    
    /// 简化的文件拖拽验证 - 只检查基本条件
    private func isDefinitelyFileDragOperation(files: [URL]) -> Bool {
        // 检查1：文件数量必须合理
        guard files.count > 0 && files.count <= 50 else {
            logger.debug("🔍 文件数量不合理(\(files.count))")
            return false
        }
        
        // 检查2：验证文件实际存在
        let validFiles = files.filter { url in
            FileManager.default.fileExists(atPath: url.path)
        }
        
        guard validFiles.count > 0 else {
            logger.debug("🔍 没有有效的文件")
            return false
        }
        
        // 检查3：排除明显的系统内部路径
        let systemPaths = ["/System/Library/", "/usr/lib/", "/private/var/folders/"]
        let hasSystemFiles = files.contains { url in
            systemPaths.contains { url.path.hasPrefix($0) }
        }
        
        if hasSystemFiles {
            logger.debug("🔍 包含系统内部路径文件")
            return false
        }
        
        logger.debug("🔍 文件拖拽验证通过 - 文件数:\(validFiles.count)")
        return true
    }
    
    /// 最严格检测：确认这不是文件拖拽操作
    private func isDefinitelyNotFileDrag() -> Bool {
        guard let startLocation = dragStartLocation,
              let startTime = dragStartTime else { 
            logger.debug("🔍 没有拖拽起始位置或时间，认为不是文件拖拽")
            return true 
        }
        
        let currentMouseLocation = NSEvent.mouseLocation
        let dragDistance = sqrt(pow(currentMouseLocation.x - startLocation.x, 2) + pow(currentMouseLocation.y - startLocation.y, 2))
        let dragDuration = Date().timeIntervalSince(startTime)
        
        // 获取拖拽剪贴板
        let dragPasteboard = NSPasteboard(name: .drag)
        
        // 检查1：超短距离拖拽 - 99%不是文件拖拽
        if dragDistance < 10 {
            logger.debug("🔍 超短距离拖拽(\(dragDistance))，确认不是文件拖拽")
            return true
        }
        
        // 检查2：超短时间拖拽 - 通常是点击或文本选择
        if dragDuration < 0.1 {
            logger.debug("🔍 超短时间拖拽(\(dragDuration))，确认不是文件拖拽")  
            return true
        }
        
        // 检查3：检查是否有文件URL类型
        let hasFileURLs = dragPasteboard.canReadItem(withDataConformingToTypes: [UTType.fileURL.identifier])
        if !hasFileURLs {
            logger.debug("🔍 拖拽剪贴板中没有文件URL，确认不是文件拖拽")
            return true
        }
        
        // 检查4：检查是否只有文本内容（常见的文本选择场景）
        let hasTextContent = dragPasteboard.canReadItem(withDataConformingToTypes: [UTType.plainText.identifier, UTType.utf8PlainText.identifier])
        let hasImageContent = dragPasteboard.canReadItem(withDataConformingToTypes: [UTType.image.identifier])
        
        if hasTextContent && !hasImageContent && dragDistance < 30 {
            logger.debug("🔍 短距离文本拖拽(\(dragDistance))，确认不是文件拖拽")
            return true
        }
        
        // 检查5：在文本编辑应用中的短距离拖拽
        let isTextApp = isCurrentAppTextEditor()
        if isTextApp && dragDistance < 40 {
            logger.debug("🔍 文本编辑应用中短距离拖拽(\(dragDistance))，确认不是文件拖拽")
            return true
        }
        
        // 检查6：检查当前应用上下文 - 如果在我们自己的应用中拖拽
        if isCurrentAppOurApp() {
            logger.debug("🔍 在TempBox应用内拖拽，确认不是文件拖拽")
            return true
        }
        
        logger.debug("🔍 通过所有检查 - 距离:\(dragDistance), 时间:\(dragDuration), 有文件URL:\(hasFileURLs)")
        return false
    }
    
    /// 检查当前应用是否是我们自己的应用
    private func isCurrentAppOurApp() -> Bool {
        guard let frontmostApp = NSWorkspace.shared.frontmostApplication else { return false }
        let bundleId = frontmostApp.bundleIdentifier ?? ""
        return bundleId.contains("tempBox") || bundleId.contains("ContentManager")
    }
    
    /// 检查当前应用是否为文本编辑器
    private func isCurrentAppTextEditor() -> Bool {
        guard let frontmostApp = NSWorkspace.shared.frontmostApplication else { return false }
        
        let textEditingApps = [
            "TextEdit", "Pages", "Word", "Xcode", "Visual Studio Code", 
            "Sublime Text", "Terminal", "iTerm", "Notes", "Typora",
            "MacVim", "Vim", "Code", "Atom", "WebStorm", "IntelliJ"
        ]
        
        let appName = frontmostApp.localizedName ?? ""
        let bundleId = frontmostApp.bundleIdentifier ?? ""
        
        return textEditingApps.contains { pattern in
            appName.localizedCaseInsensitiveContains(pattern) || 
            bundleId.localizedCaseInsensitiveContains(pattern.lowercased())
        }
    }
    
    // MARK: - 拖拽操作管理
    
    /// 开始拖拽操作
    func startDragOperation() {
        guard !isDragActive else { return }
        
        isDragActive = true
        dragDropManager?.showWindow()
        logger.info("✅ 拖拽操作开始 - 显示窗口")
        
        // 设置自动关闭定时器（10秒）
        DispatchQueue.main.asyncAfter(deadline: .now() + 10.0, execute: {
            if self.isDragActive {
                self.logger.info("⏰ 拖拽窗口自动关闭（超时）")
                self.endDragOperation()
            }
        })
    }
    
    /// 结束拖拽操作
    func endDragOperation() {
        guard isDragActive else { return }
        
        isDragActive = false
        dragDropManager?.hideWindow()
        logger.info("🛑 拖拽操作结束")
    }
    
    // MARK: - 窗口管理
    
    /// 显示拖拽窗口
    func showDragDropWindow() {
        dragDropManager?.showWindow()
        logger.info("📱 显示拖拽窗口")
    }
    
    /// 隐藏拖拽窗口
    func hideDragDropWindow() {
        dragDropManager?.hideWindow()
        logger.info("🙈 隐藏拖拽窗口")
    }
    
    /// 手动触发拖拽窗口（通过快捷键等）
    func showDragDropWindowManually() {
        isDragActive = true
        dragDropManager?.showWindow()
        logger.info("🔧 手动显示拖拽窗口")
        
        // 10秒后自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 10.0, execute: {
            if self.isDragActive {
                self.endDragOperation()
            }
        })
    }
    
    // MARK: - 清理
    deinit {
        // 同步清理，避免actor隔离问题
        if let monitor = globalEventMonitor {
            NSEvent.removeMonitor(monitor)
        }
    }
}