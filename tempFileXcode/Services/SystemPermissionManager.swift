import Foundation
import AppKit
import SwiftUI
import Combine

/// 系统级权限管理器 - 处理macOS系统权限和持久化授权（优化版）
@MainActor
class SystemPermissionManager: ObservableObject {
    static let shared = SystemPermissionManager()
    
    @Published var hasFullDiskAccess: Bool = false
    @Published var trustedFolders: [String] = []
    
    private let userDefaults = UserDefaults.standard
    private let trustedFoldersKey = "TrustedFolders"
    private let fullDiskAccessKey = "HasFullDiskAccess"
    
    // 权限缓存机制
    private var permissionCache: [String: Bool] = [:]
    private var lastPermissionCheck: Date?
    private let cacheValidityDuration: TimeInterval = 300 // 5分钟缓存
    
    // 文件访问权限缓存
    private var folderAccessCache: [String: Date] = [:]
    private let folderCacheValidityDuration: TimeInterval = 1800 // 30分钟缓存
    
    private init() {
        loadSavedState()
        loadPermissionCache()
    }
    
    private func loadSavedState() {
        hasFullDiskAccess = userDefaults.bool(forKey: fullDiskAccessKey)
        trustedFolders = userDefaults.stringArray(forKey: trustedFoldersKey) ?? []
    }
    
    /// 加载权限缓存
    private func loadPermissionCache() {
        if let cacheData = userDefaults.data(forKey: "PermissionCache"),
           let cache = try? JSONDecoder().decode([String: Bool].self, from: cacheData) {
            permissionCache = cache
        }
        
        if let folderCacheData = userDefaults.data(forKey: "FolderAccessCache"),
           let folderCache = try? JSONDecoder().decode([String: Date].self, from: folderCacheData) {
            folderAccessCache = folderCache
        }
        
        lastPermissionCheck = userDefaults.object(forKey: "LastPermissionCheck") as? Date
    }
    
    /// 保存权限缓存
    private func savePermissionCache() {
        if let cacheData = try? JSONEncoder().encode(permissionCache) {
            userDefaults.set(cacheData, forKey: "PermissionCache")
        }
        
        if let folderCacheData = try? JSONEncoder().encode(folderAccessCache) {
            userDefaults.set(folderCacheData, forKey: "FolderAccessCache")
        }
        
        userDefaults.set(lastPermissionCheck, forKey: "LastPermissionCheck")
    }
    
    // MARK: - 系统权限检查（优化版带缓存）
    
    /// 检查是否有完整磁盘访问权限（带缓存）
    func checkFullDiskAccess() -> Bool {
        let now = Date()
        
        // 检查缓存是否有效
        if let lastCheck = lastPermissionCheck,
           now.timeIntervalSince(lastCheck) < cacheValidityDuration,
           let cachedResult = permissionCache["fullDiskAccess"] {
            return cachedResult
        }
        
        // 执行实际检查
        let hasAccess = performFullDiskAccessCheck()
        
        // 更新缓存
        permissionCache["fullDiskAccess"] = hasAccess
        lastPermissionCheck = now
        savePermissionCache()
        
        // 更新发布的状态
        hasFullDiskAccess = hasAccess
        userDefaults.set(hasAccess, forKey: fullDiskAccessKey)
        
        return hasAccess
    }
    
    /// 执行实际的完整磁盘访问权限检查
    private func performFullDiskAccessCheck() -> Bool {
        let testPaths = [
            "/Library/Application Support/com.apple.TCC/TCC.db",
            "/System/Library/CoreServices/Finder.app/Contents/Info.plist",
            "/private/var/db/dslocal/nodes/Default/users/root.plist"
        ]
        
        for path in testPaths {
            let url = URL(fileURLWithPath: path)
            do {
                _ = try url.resourceValues(forKeys: [.isReadableKey])
                print("✅ 检测到完整磁盘访问权限")
                return true
            } catch {
                continue
            }
        }
        
        print("❌ 未检测到完整磁盘访问权限")
        return false
    }
    
    /// 检查特定文件夹是否已被信任（带缓存）
    func isFolderTrusted(_ folderPath: String) -> Bool {
        let now = Date()
        
        // 检查文件夹访问缓存
        if let lastAccess = folderAccessCache[folderPath],
           now.timeIntervalSince(lastAccess) < folderCacheValidityDuration {
            return true
        }
        
        // 检查是否在信任列表中
        let trustedFolders = userDefaults.stringArray(forKey: trustedFoldersKey) ?? []
        let isTrusted = trustedFolders.contains { folderPath.hasPrefix($0) }
        
        if isTrusted {
            // 更新访问缓存
            folderAccessCache[folderPath] = now
            savePermissionCache()
        }
        
        return isTrusted
    }
    
    /// 快速检查文件是否可访问（智能权限检查）
    func canAccessFile(_ fileURL: URL) -> Bool {
        let filePath = fileURL.path
        
        // 首先检查是否有完整磁盘访问权限
        if checkFullDiskAccess() {
            return true
        }
        
        // 检查文件所在目录是否被信任
        let parentDirectory = fileURL.deletingLastPathComponent().path
        if isFolderTrusted(parentDirectory) {
            return true
        }
        
        // 检查常见的用户目录（通常有权限访问）
        let userHomePath = NSHomeDirectory()
        let commonAccessiblePaths = [
            "\(userHomePath)/Desktop",
            "\(userHomePath)/Documents",
            "\(userHomePath)/Downloads",
            "\(userHomePath)/Pictures",
            "\(userHomePath)/Movies",
            "\(userHomePath)/Music"
        ]
        
        for accessiblePath in commonAccessiblePaths {
            if filePath.hasPrefix(accessiblePath) {
                // 将此路径添加到信任列表
                addTrustedFolder(accessiblePath)
                return true
            }
        }
        
        return false
    }
    
    /// 批量检查文件访问权限
    func canAccessFiles(_ fileURLs: [URL]) -> (accessible: [URL], needsAuth: [URL]) {
        var accessible: [URL] = []
        var needsAuth: [URL] = []
        
        for url in fileURLs {
            if canAccessFile(url) {
                accessible.append(url)
            } else {
                needsAuth.append(url)
            }
        }
        
        return (accessible, needsAuth)
    }
    
    /// 添加信任的文件夹
    func addTrustedFolder(_ folderPath: String) {
        var folders = trustedFolders
        if !folders.contains(folderPath) {
            folders.append(folderPath)
            trustedFolders = folders
            userDefaults.set(folders, forKey: trustedFoldersKey)
            print("📁 添加信任文件夹: \(folderPath)")
        }
    }
    
    /// 获取所有信任的文件夹
    func getTrustedFolders() -> [String] {
        return userDefaults.stringArray(forKey: trustedFoldersKey) ?? []
    }
    
    // MARK: - 智能权限引导
    
    /// 显示系统权限设置引导
    func showSystemPermissionGuide(completion: @escaping (Bool) -> Void) {
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "🔐 一次性解决文件访问权限"
            alert.informativeText = """
            为了避免每次拖拽都需要授权，建议您进行以下设置：
            
            🎯 推荐方案（一劳永逸）：
            1. 授予应用"完整磁盘访问权限"
            2. 或者授权常用文件夹的访问权限
            
            📱 设置步骤：
            • macOS Ventura+: 系统设置 → 隐私与安全性 → 完整磁盘访问权限
            • macOS Monterey-: 系统偏好设置 → 安全性与隐私 → 隐私 → 完整磁盘访问权限
            
            ⚡ 或者点击"授权文件夹"来授权特定文件夹的访问权限
            """
            alert.alertStyle = .informational
            
            alert.addButton(withTitle: "打开系统设置")
            alert.addButton(withTitle: "授权文件夹")
            alert.addButton(withTitle: "稍后设置")
            
            let response = alert.runModal()
            
            switch response {
            case .alertFirstButtonReturn:
                self.openSystemPreferences()
                completion(false)
            case .alertSecondButtonReturn:
                self.showFolderAuthorizationDialog(completion: completion)
            default:
                completion(false)
            }
        }
    }
    
    /// 显示文件夹授权对话框
    private func showFolderAuthorizationDialog(completion: @escaping (Bool) -> Void) {
        DispatchQueue.main.async {
            let openPanel = NSOpenPanel()
            openPanel.title = "授权文件夹访问"
            openPanel.message = """
            选择您经常拖拽文件的文件夹来授权访问：
            
            💡 建议选择：
            • 桌面 (Desktop)
            • 文档 (Documents) 
            • 下载 (Downloads)
            • 或您的工作文件夹
            
            授权后，从这些文件夹拖拽文件将不再需要重复授权。
            """
            openPanel.prompt = "授权访问"
            openPanel.allowsMultipleSelection = true
            openPanel.canChooseDirectories = true
            openPanel.canChooseFiles = false
            
            // 设置默认位置为用户主目录
            openPanel.directoryURL = FileManager.default.homeDirectoryForCurrentUser
            
            openPanel.begin { response in
                if response == .OK {
                    Task { @MainActor in
                        for url in openPanel.urls {
                            self.addTrustedFolder(url.path)
                            // 为授权的文件夹创建持久书签
                            self.createPersistentBookmark(for: url)
                        }
                    }
                    
                    let alert = NSAlert()
                    alert.messageText = "✅ 文件夹授权成功"
                    alert.informativeText = """
                    已授权 \(openPanel.urls.count) 个文件夹的访问权限。
                    
                    从这些文件夹拖拽文件将不再需要重复授权：
                    \(openPanel.urls.map { "• \($0.lastPathComponent)" }.joined(separator: "\n"))
                    """
                    alert.alertStyle = .informational
                    alert.addButton(withTitle: "好的")
                    alert.runModal()
                    
                    completion(true)
                } else {
                    completion(false)
                }
            }
        }
    }
    
    /// 创建持久书签
    private func createPersistentBookmark(for url: URL) {
        do {
            let bookmarkData = try url.bookmarkData(
                options: [.withSecurityScope, .securityScopeAllowOnlyReadAccess],
                includingResourceValuesForKeys: nil,
                relativeTo: nil
            )
            
            let bookmarkKey = "Bookmark_\(url.path.replacingOccurrences(of: "/", with: "_"))"
            userDefaults.set(bookmarkData, forKey: bookmarkKey)
            print("📖 创建持久书签: \(url.path)")
        } catch {
            print("❌ 创建持久书签失败: \(url.path) - \(error)")
        }
    }
    
    /// 从持久书签恢复URL
    func restoreFromPersistentBookmark(for path: String) -> URL? {
        let bookmarkKey = "Bookmark_\(path.replacingOccurrences(of: "/", with: "_"))"
        guard let bookmarkData = userDefaults.data(forKey: bookmarkKey) else {
            return nil
        }
        
        do {
            var isStale = false
            let url = try URL(
                resolvingBookmarkData: bookmarkData,
                options: [.withSecurityScope],
                relativeTo: nil,
                bookmarkDataIsStale: &isStale
            )
            
            if isStale {
                print("⚠️ 持久书签已过期: \(path)")
                userDefaults.removeObject(forKey: bookmarkKey)
                return nil
            }
            
            return url
        } catch {
            print("❌ 恢复持久书签失败: \(path) - \(error)")
            userDefaults.removeObject(forKey: bookmarkKey)
            return nil
        }
    }
    
    /// 打开系统设置
    private func openSystemPreferences() {
        let urls = [
            "x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles",
            "x-apple.systempreferences:com.apple.preference.security?Privacy"
        ]
        
        for urlString in urls {
            if let url = URL(string: urlString) {
                if NSWorkspace.shared.open(url) {
                    print("✅ 打开系统设置成功")
                    return
                }
            }
        }
        
        // 备用方案
        if #available(macOS 13.0, *) {
            NSWorkspace.shared.open(URL(fileURLWithPath: "/System/Applications/System Settings.app"))
        } else {
            NSWorkspace.shared.open(URL(fileURLWithPath: "/System/Applications/System Preferences.app"))
        }
    }
    
    // MARK: - 权限状态
    
    /// 获取权限状态描述
    func getPermissionStatus() -> (hasFullAccess: Bool, trustedFolders: [String], description: String) {
        let hasFullAccess = checkFullDiskAccess()
        let trustedFolders = getTrustedFolders()
        
        let description: String
        if hasFullAccess {
            description = "✅ 已获得完整磁盘访问权限，可以访问所有文件"
        } else if !trustedFolders.isEmpty {
            description = "✅ 已授权 \(trustedFolders.count) 个文件夹的访问权限"
        } else {
            description = "⚠️ 建议设置系统权限以避免重复授权"
        }
        
        return (hasFullAccess, trustedFolders, description)
    }
}