import Foundation
import SwiftUI
import AppKit
import Combine

/// 多选管理器 - 处理类似Finder的多选功能
@MainActor
class SelectionManager: ObservableObject {
    @Published var selectedItems: Set<String> = []
    @Published var isMultiSelectMode: Bool = false
    @Published var lastSelectedIndex: Int? = nil
    
    private var allItems: [ContentItem] = []
    
    // MARK: - 选择操作
    
    /// 设置所有项目（用于范围选择）
    func setAllItems(_ items: [ContentItem]) {
        allItems = items
    }
    
    /// 单击选择（清除其他选择）
    func selectSingle(_ itemId: String) {
        selectedItems = [itemId]
        isMultiSelectMode = false
        updateLastSelectedIndex(for: itemId)
    }
    
    /// Cmd+点击切换选择
    func toggleSelection(_ itemId: String, with event: NSEvent? = nil) {
        if event?.modifierFlags.contains(.command) == true || isMultiSelectMode {
            if selectedItems.contains(itemId) {
                selectedItems.remove(itemId)
            } else {
                selectedItems.insert(itemId)
            }
            isMultiSelectMode = !selectedItems.isEmpty
            updateLastSelectedIndex(for: itemId)
        } else {
            selectSingle(itemId)
        }
    }
    
    /// 简单的切换选择（不带事件）
    func toggleSelection(_ itemId: String) {
        if selectedItems.contains(itemId) {
            selectedItems.remove(itemId)
        } else {
            selectedItems.insert(itemId)
        }
        isMultiSelectMode = !selectedItems.isEmpty
        updateLastSelectedIndex(for: itemId)
    }
    
    /// Shift+点击范围选择
    func selectRange(_ itemId: String, with event: NSEvent? = nil) {
        guard event?.modifierFlags.contains(.shift) == true,
              let lastIndex = lastSelectedIndex,
              let currentIndex = allItems.firstIndex(where: { $0.id?.uuidString == itemId }) else {
            selectSingle(itemId)
            return
        }
        
        let startIndex = min(lastIndex, currentIndex)
        let endIndex = max(lastIndex, currentIndex)
        
        // 选择范围内的所有项目
        for index in startIndex...endIndex {
            if index < allItems.count {
                if let itemId = allItems[index].id?.uuidString {
                    selectedItems.insert(itemId)
                }
            }
        }
        
        isMultiSelectMode = true
    }
    
    /// 处理点击事件（自动检测修饰键）
    func handleClick(_ itemId: String, with event: NSEvent? = nil) {
        if let event = event {
            if event.modifierFlags.contains(.shift) {
                selectRange(itemId, with: event)
            } else if event.modifierFlags.contains(.command) {
                toggleSelection(itemId, with: event)
            } else {
                selectSingle(itemId)
            }
        } else {
            // 如果没有事件信息，根据当前状态决定
            if isMultiSelectMode {
                toggleSelection(itemId)
            } else {
                selectSingle(itemId)
            }
        }
    }
    
    // MARK: - 全选/取消全选
    
    /// 全选
    func selectAll() {
        selectedItems = Set(allItems.compactMap { $0.id?.uuidString })
        isMultiSelectMode = true
    }
    
    /// 取消全选
    func deselectAll() {
        selectedItems.removeAll()
        isMultiSelectMode = false
        lastSelectedIndex = nil
    }
    
    /// 反选
    func invertSelection() {
        let allIds = Set(allItems.compactMap { $0.id?.uuidString })
        selectedItems = allIds.subtracting(selectedItems)
        isMultiSelectMode = !selectedItems.isEmpty
    }
    
    // MARK: - 查询方法
    
    /// 检查项目是否被选中
    func isSelected(_ itemId: String) -> Bool {
        return selectedItems.contains(itemId)
    }
    
    /// 获取选中的项目
    func getSelectedItems() -> [ContentItem] {
        return allItems.filter { selectedItems.contains($0.id?.uuidString ?? "") }
    }
    
    /// 获取选中项目数量
    var selectedCount: Int {
        return selectedItems.count
    }
    
    /// 是否有选中项目
    var hasSelection: Bool {
        return !selectedItems.isEmpty
    }
    
    // MARK: - 批量操作
    
    /// 删除选中的项目（由视图层处理具体逻辑）
    func deleteSelectedItems() {
        // 这个方法由视图层调用具体的删除逻辑
        print("请求删除 \(selectedCount) 个项目")
    }
    
    /// 导出选中的项目（由视图层处理具体逻辑）
    func exportSelectedItems() {
        // 这个方法由视图层调用具体的导出逻辑
        print("请求导出 \(selectedCount) 个项目")
    }
    
    /// 复制选中项目的文件路径
    func copySelectedFilePaths() {
        let selectedItemsList = getSelectedItems()
        let filePaths = selectedItemsList.compactMap { $0.filePath }
        
        if !filePaths.isEmpty {
            let pathsString = filePaths.joined(separator: "\n")
            NSPasteboard.general.clearContents()
            NSPasteboard.general.setString(pathsString, forType: .string)
            print("已复制 \(filePaths.count) 个文件路径")
        }
    }
    
    // MARK: - 私有方法
    
    private func updateLastSelectedIndex(for itemId: String) {
        lastSelectedIndex = allItems.firstIndex { $0.id?.uuidString == itemId }
    }
}

// MARK: - 多选工具栏
struct MultiSelectionToolbar: View {
    @ObservedObject var selectionManager: SelectionManager
    let onDelete: () -> Void
    let onExport: () -> Void
    let onCopyPaths: () -> Void
    
    var body: some View {
        if selectionManager.hasSelection {
            HStack {
                // 选中数量显示
                Text("已选中 \(selectionManager.selectedCount) 项")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // 批量操作按钮
                HStack(spacing: 8) {
                    Button("复制路径") {
                        selectionManager.copySelectedFilePaths()
                        onCopyPaths()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("导出") {
                        selectionManager.exportSelectedItems()
                        onExport()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("删除") {
                        selectionManager.deleteSelectedItems()
                        onDelete()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                    
                    Button("取消选择") {
                        selectionManager.deselectAll()
                    }
                    .buttonStyle(.borderless)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.accentColor.opacity(0.1))
            )
            .transition(.move(edge: .bottom).combined(with: .opacity))
        }
    }
}

// MARK: - 键盘快捷键处理
struct SelectionKeyboardHandler: NSViewRepresentable {
    let selectionManager: SelectionManager
    
    func makeNSView(context: Context) -> NSView {
        let view = KeyboardHandlerView()
        view.selectionManager = selectionManager
        return view
    }
    
    func updateNSView(_ nsView: NSView, context: Context) {
        if let view = nsView as? KeyboardHandlerView {
            view.selectionManager = selectionManager
        }
    }
}

class KeyboardHandlerView: NSView {
    var selectionManager: SelectionManager?
    
    override var acceptsFirstResponder: Bool { true }
    
    override func keyDown(with event: NSEvent) {
        guard let selectionManager = selectionManager else {
            super.keyDown(with: event)
            return
        }
        
        switch event.keyCode {
        case 0: // A key
            if event.modifierFlags.contains(.command) {
                selectionManager.selectAll()
                return
            }
        case 51: // Delete key
            if selectionManager.hasSelection {
                selectionManager.deleteSelectedItems()
                return
            }
        case 53: // Escape key
            if selectionManager.hasSelection {
                selectionManager.deselectAll()
                return
            }
        default:
            break
        }
        
        super.keyDown(with: event)
    }
    
    override func mouseDown(with event: NSEvent) {
        // 点击空白区域取消选择
        if !(selectionManager?.hasSelection ?? false) {
            selectionManager?.deselectAll()
        }
        super.mouseDown(with: event)
    }
}

// MARK: - 右键菜单增强
struct EnhancedContextMenu: View {
    let selectedItems: [ContentItem]
    let selectionManager: SelectionManager
    let onQuickLook: () -> Void
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        Group {
            if selectedItems.count == 1 {
                // 单选菜单
                singleSelectionMenu
            } else if selectedItems.count > 1 {
                // 多选菜单
                multiSelectionMenu
            }
        }
    }
    
    @ViewBuilder
    private var singleSelectionMenu: some View {
        let item = selectedItems.first!
        
        Button("快速预览") {
            onQuickLook()
        }
        
        Button("编辑") {
            onEdit()
        }
        
        Divider()
        
        Menu("打开方式") {
            if let filePath = item.filePath {
                let url = URL(fileURLWithPath: filePath)
                let apps = FileTypeManager.shared.getRecommendedApps(for: url)
                
                ForEach(apps, id: \.bundleIdentifier) { app in
                    Button(app.name) {
                        _ = FileTypeManager.shared.openFile(url, with: app)
                    }
                }
            }
        }
        
        Divider()
        
        Button("在Finder中显示") {
            if let filePath = item.filePath {
                NSWorkspace.shared.selectFile(filePath, inFileViewerRootedAtPath: "")
            }
        }
        
        Button("复制文件路径") {
            if let filePath = item.filePath {
                NSPasteboard.general.clearContents()
                NSPasteboard.general.setString(filePath, forType: .string)
            }
        }
        
        Divider()
        
        Button("删除", role: .destructive) {
            onDelete()
        }
    }
    
    @ViewBuilder
    private var multiSelectionMenu: some View {
        Text("已选中 \(selectedItems.count) 项")
            .font(.headline)
        
        Divider()
        
        Button("复制所有文件路径") {
            selectionManager.copySelectedFilePaths()
        }
        
        Button("导出选中项目") {
            selectionManager.exportSelectedItems()
        }
        
        Divider()
        
        Button("取消选择") {
            selectionManager.deselectAll()
        }
        
        Button("删除所有选中项", role: .destructive) {
            onDelete()
        }
    }
}