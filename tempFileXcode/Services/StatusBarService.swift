import SwiftUI
import AppKit
import Combine
import os.log
import UserNotifications

// MARK: - Status Bar Manager
@MainActor
class StatusBarManager: ObservableObject {
    @Published var isVisible: Bool = true

    private var statusItem: NSStatusItem?
    private let logger = Logger(subsystem: "com.contentmanager.app", category: "StatusBarManager")
    
    // 依赖注入 - 保持弱引用避免循环引用
    private weak var contentService: ContentService?
    private weak var batchService: BatchService?
    private weak var pasteboardMonitor: PasteboardMonitor?
    private weak var quickPasteManager: QuickPasteManager?
    private weak var dragDetectionService: DragDetectionService?

    init() {
        setupStatusBar()
    }
    
    // 设置依赖
    func setDependencies(
        contentService: ContentService,
        batchService: BatchService,
        pasteboardMonitor: PasteboardMonitor,
        quickPasteManager: QuickPasteManager,
        dragDetectionService: DragDetectionService? = nil
    ) {
        self.contentService = contentService
        self.batchService = batchService
        self.pasteboardMonitor = pasteboardMonitor
        self.quickPasteManager = quickPasteManager
        self.dragDetectionService = dragDetectionService
        
        // 重新创建菜单以包含新功能
        updateMenu()
    }

    deinit {
        // 在deinit中不能调用MainActor方法，直接清理
        if let statusItem = statusItem {
            NSStatusBar.system.removeStatusItem(statusItem)
        }
    }

    private func setupStatusBar() {
        // 创建状态栏项目
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.squareLength)

        guard let statusItem = statusItem else {
            logger.error("Failed to create status item")
            return
        }

        // 设置图标 - 使用更现代的图标
        if let button = statusItem.button {
            let image = NSImage(systemSymbolName: "archivebox.fill", accessibilityDescription: "TempBox")
            image?.isTemplate = true
            button.image = image
            button.toolTip = "TempBox - 临时文件管理器"
            
            // 设置按钮动作 - 左键点击
            button.action = #selector(statusBarButtonClicked(_:))
            button.target = self
            
            // 启用右键菜单
            button.sendAction(on: [.leftMouseUp, .rightMouseUp])
        }

        // 创建菜单
        let menu = createMenu()
        statusItem.menu = menu

        logger.info("Status bar initialized")
    }
    
    // 更新菜单
    private func updateMenu() {
        guard let statusItem = statusItem else { return }
        statusItem.menu = createMenu()
    }
    
    // 状态栏按钮点击处理
    @objc private func statusBarButtonClicked(_ sender: NSStatusBarButton) {
        guard let event = NSApp.currentEvent else { return }
        
        if event.type == .rightMouseUp {
            // 右键点击 - 显示传统菜单
            statusItem?.menu = createMenu()
        } else {
            // 左键点击 - 显示现代化弹窗
            showModernPopover(from: sender)
            logger.info("Status bar left-clicked")
        }
    }
    
    // 显示现代化弹窗
    private func showModernPopover(from button: NSStatusBarButton) {
        let popover = NSPopover()
        popover.contentSize = NSSize(width: 360, height: 520)
        popover.behavior = .transient
        popover.animates = true
        popover.appearance = NSAppearance(named: .aqua)
        
        let hostingController = NSHostingController(rootView: 
            ModernStatusBarPopover(
                contentService: contentService,
                batchService: batchService,
                pasteboardMonitor: pasteboardMonitor,
                quickPasteManager: quickPasteManager,
                dragDetectionService: dragDetectionService,
                statusBarManager: self,
                onClose: {
                    popover.performClose(nil)
                }
            )
        )
        
        popover.contentViewController = hostingController
        popover.show(relativeTo: button.bounds, of: button, preferredEdge: .minY)
    }

    private func removeStatusBar() {
        if let statusItem = statusItem {
            NSStatusBar.system.removeStatusItem(statusItem)
            self.statusItem = nil
        }
    }

    private func createMenu() -> NSMenu {
        let menu = NSMenu()

        // 显示主窗口
        let showMainItem = NSMenuItem(
            title: "📋 显示主窗口",
            action: #selector(showMainWindow),
            keyEquivalent: "m"
        )
        showMainItem.target = self
        menu.addItem(showMainItem)

        menu.addItem(NSMenuItem.separator())

        // 快速功能区
        let quickSubmenu = NSMenu()
        
        // 新建批次
        let newBatchItem = NSMenuItem(
            title: "📦 新建批次",
            action: #selector(createNewBatch),
            keyEquivalent: "n"
        )
        newBatchItem.target = self
        quickSubmenu.addItem(newBatchItem)
        
        // 快速粘贴
        let quickPasteItem = NSMenuItem(
            title: "⚡ 快速粘贴窗口",
            action: #selector(showQuickPaste),
            keyEquivalent: "v"
        )
        quickPasteItem.target = self
        quickSubmenu.addItem(quickPasteItem)
        
        // 拖拽文件窗口
        let dragDropItem = NSMenuItem(
            title: "📁 拖拽文件窗口",
            action: #selector(showDragDropWindow),
            keyEquivalent: "d"
        )
        dragDropItem.target = self
        quickSubmenu.addItem(dragDropItem)
        
        quickSubmenu.addItem(NSMenuItem.separator())
        
        // 监听剪贴板
        let monitoringItem = NSMenuItem(
            title: pasteboardMonitor?.isMonitoring == true ? "⏸ 停止监听剪贴板" : "▶️ 开始监听剪贴板",
            action: #selector(toggleClipboardMonitoring),
            keyEquivalent: ""
        )
        monitoringItem.target = self
        quickSubmenu.addItem(monitoringItem)
        
        let quickMenuItem = NSMenuItem(title: "快速操作", action: nil, keyEquivalent: "")
        quickMenuItem.submenu = quickSubmenu
        menu.addItem(quickMenuItem)

        menu.addItem(NSMenuItem.separator())
        
        // 最近批次
        if let batchService = batchService, !batchService.batches.isEmpty {
            let recentBatchesSubmenu = NSMenu()
            
            // 显示最近的5个批次
            let recentBatches = Array(batchService.batches.prefix(5))
            for (index, batch) in recentBatches.enumerated() {
                let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
                let batchItem = NSMenuItem(
                    title: "📁 \(batchName)",
                    action: #selector(openRecentBatch(_:)),
                    keyEquivalent: "\(index + 1)"
                )
                batchItem.target = self
                batchItem.representedObject = batch
                recentBatchesSubmenu.addItem(batchItem)
            }
            
            if !recentBatches.isEmpty {
                let recentMenuItem = NSMenuItem(title: "最近批次", action: nil, keyEquivalent: "")
                recentMenuItem.submenu = recentBatchesSubmenu
                menu.addItem(recentMenuItem)
                menu.addItem(NSMenuItem.separator())
            }
        }

        // 退出
        let quitItem = NSMenuItem(
            title: "❌ 退出",
            action: #selector(quitApp),
            keyEquivalent: "q"
        )
        quitItem.target = self
        menu.addItem(quitItem)

        return menu
    }

    @objc private func showMainWindow() {
        NSApp.activate(ignoringOtherApps: true)
        for window in NSApp.windows {
            if window.contentViewController != nil {
                window.makeKeyAndOrderFront(nil)
                break
            }
        }
    }

    @objc private func quitApp() {
        NSApp.terminate(nil)
    }
    
    // MARK: - Menu Actions
    @objc private func createNewBatch() {
        Task {
            do {
                let newBatch = try batchService?.createNewBatch()
                batchService?.setCurrentBatch(newBatch!)
                showNotification(title: "批次创建成功", message: "已创建新的内容批次")
                updateMenu() // 更新菜单以显示新批次
            } catch {
                logger.error("Failed to create new batch: \(error)")
                showNotification(title: "创建失败", message: "无法创建新批次")
            }
        }
    }
    
    @objc private func showQuickPaste() {
        quickPasteManager?.showQuickPasteWindow()
    }
    
    @objc private func showDragDropWindow() {
        dragDetectionService?.showDragDropWindowManually()
    }
    
    @objc private func toggleClipboardMonitoring() {
        guard let monitor = pasteboardMonitor else { return }
        
        if monitor.isMonitoring {
            monitor.stopMonitoring()
            showNotification(title: "监听已停止", message: "剪贴板监听已关闭")
        } else {
            monitor.startMonitoring()
            showNotification(title: "监听已开始", message: "正在监听剪贴板变化")
        }
        updateMenu() // 更新菜单文本
    }
    
    @objc private func openRecentBatch(_ sender: NSMenuItem) {
        guard let batch = sender.representedObject as? NSManagedObject else { return }
        
        batchService?.setCurrentBatch(batch)
        showMainWindow()
        
        let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
        showNotification(title: "批次已切换", message: "已切换到批次：\(batchName)")
    }

    func showNotification(title: String, message: String) {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = message
        content.sound = UNNotificationSound.default
        
        let request = UNNotificationRequest(
            identifier: UUID().uuidString,
            content: content,
            trigger: nil
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                self.logger.error("Failed to send notification: \(error.localizedDescription)")
            } else {
                self.logger.info("Notification sent: \(title)")
            }
        }
    }
}

// MARK: - Modern Status Bar Popover
struct ModernStatusBarPopover: View {
    let contentService: ContentService?
    let batchService: BatchService?
    let pasteboardMonitor: PasteboardMonitor?
    let quickPasteManager: QuickPasteManager?
    let dragDetectionService: DragDetectionService?
    let statusBarManager: StatusBarManager
    let onClose: () -> Void
    
    @State private var hoveredItem: String?
    
    var body: some View {
        VStack(spacing: 0) {
            // 头部区域
            headerView
            
            Divider()
            
            // 主要功能区域
            ScrollView {
                VStack(spacing: 12) {
                    quickActionsSection
                    
                    Divider()
                        .padding(.horizontal, 16)
                    
                    batchesSection
                    
                    Divider()
                        .padding(.horizontal, 16)
                    
                    systemStatusSection
                }
                .padding(.vertical, 16)
            }
            
            Divider()
            
            // 底部操作区域
            footerView
        }
        .background(.regularMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack(spacing: 12) {
            // 应用图标 - 使用渐变背景的现代化设计
            Image(systemName: "archivebox.fill")
                .foregroundColor(.white)
                .font(.system(size: 18, weight: .semibold))
                .frame(width: 36, height: 36)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(
                            LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                )
            
            VStack(alignment: .leading, spacing: 2) {
                Text("TempBox")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                if let batchService = batchService,
                   let currentBatch = batchService.currentBatch {
                    let batchName = currentBatch.value(forKey: "name") as? String ?? "未命名批次"
                    Text("当前批次: \(batchName)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                } else {
                    Text("未选择批次")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // 关闭按钮
            Button(action: onClose) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.secondary)
                    .font(.system(size: 16))
            }
            .buttonStyle(.plain)
            .help("关闭")
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(.regularMaterial)
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("快速操作")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            .padding(.horizontal, 16)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                QuickActionCard(
                    title: "快速粘贴",
                    icon: "plus.app.fill",
                    color: .blue,
                    shortcut: "⌘⇧V",
                    isHovered: hoveredItem == "quickPaste"
                ) {
                    quickPasteManager?.showQuickPasteWindow()
                    onClose()
                }
                .onHover { hovering in
                    hoveredItem = hovering ? "quickPaste" : nil
                }
                
                QuickActionCard(
                    title: "拖拽文件",
                    icon: "arrow.down.doc.fill",
                    color: .green,
                    shortcut: "⌘⇧D",
                    isHovered: hoveredItem == "dragDrop"
                ) {
                    dragDetectionService?.showDragDropWindowManually()
                    onClose()
                }
                .onHover { hovering in
                    hoveredItem = hovering ? "dragDrop" : nil
                }
                
                QuickActionCard(
                    title: "新建批次",
                    icon: "folder.badge.plus",
                    color: .orange,
                    shortcut: "⌘N",
                    isHovered: hoveredItem == "newBatch"
                ) {
                    Task {
                        do {
                            let newBatch = try batchService?.createNewBatch()
                            batchService?.setCurrentBatch(newBatch!)
                            statusBarManager.showNotification(title: "批次创建成功", message: "已创建新的内容批次")
                        } catch {
                            statusBarManager.showNotification(title: "创建失败", message: "无法创建新批次")
                        }
                    }
                    onClose()
                }
                .onHover { hovering in
                    hoveredItem = hovering ? "newBatch" : nil
                }
                
                QuickActionCard(
                    title: "打开主窗口",
                    icon: "macwindow",
                    color: .purple,
                    shortcut: "⌘M",
                    isHovered: hoveredItem == "mainWindow"
                ) {
                    NSApp.activate(ignoringOtherApps: true)
                    for window in NSApp.windows {
                        if window.contentViewController != nil {
                            window.makeKeyAndOrderFront(nil)
                            break
                        }
                    }
                    onClose()
                }
                .onHover { hovering in
                    hoveredItem = hovering ? "mainWindow" : nil
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    // MARK: - Batches Section
    private var batchesSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("最近批次")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                if let batchService = batchService {
                    Text("\(batchService.batches.count) 个批次")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 16)
            
            if let batchService = batchService, !batchService.batches.isEmpty {
                let recentBatches = Array(batchService.batches.prefix(3))
                ForEach(recentBatches.indices, id: \.self) { index in
                    let batch = recentBatches[index]
                    BatchRowCard(
                        batch: batch,
                        isActive: batch == batchService.currentBatch,
                        onTap: {
                            batchService.setCurrentBatch(batch)
                            let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
                            statusBarManager.showNotification(title: "批次已切换", message: "已切换到批次：\(batchName)")
                            onClose()
                        }
                    )
                }
                .padding(.horizontal, 16)
            } else {
                HStack {
                    Image(systemName: "folder")
                        .foregroundColor(.secondary)
                    Text("暂无批次")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
            }
        }
    }
    
    // MARK: - System Status Section
    private var systemStatusSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("系统状态")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            .padding(.horizontal, 16)
            
            VStack(spacing: 8) {
                StatusRow(
                    title: "剪贴板监听",
                    status: pasteboardMonitor?.isMonitoring == true ? "运行中" : "已停止",
                    statusColor: pasteboardMonitor?.isMonitoring == true ? .green : .gray,
                    action: {
                        guard let monitor = pasteboardMonitor else { return }
                        
                        if monitor.isMonitoring {
                            monitor.stopMonitoring()
                            statusBarManager.showNotification(title: "监听已停止", message: "剪贴板监听已关闭")
                        } else {
                            monitor.startMonitoring()
                            statusBarManager.showNotification(title: "监听已开始", message: "正在监听剪贴板变化")
                        }
                    }
                )
                
                StatusRow(
                    title: "拖拽检测",
                    status: "就绪",
                    statusColor: .blue,
                    action: nil
                )
            }
            .padding(.horizontal, 16)
        }
    }
    
    // MARK: - Footer View
    private var footerView: some View {
        HStack(spacing: 12) {
            Button(action: {
                // TODO: 打开设置窗口
                onClose()
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "gear")
                        .font(.system(size: 12, weight: .medium))
                    Text("设置")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .strokeBorder(Color.gray.opacity(0.2), lineWidth: 0.5)
                        )
                )
            }
            .buttonStyle(.plain)
            .foregroundColor(.primary)
            
            Spacer()
            
            Button(action: {
                NSApp.terminate(nil)
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "power")
                        .font(.system(size: 12, weight: .medium))
                    Text("退出")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.red.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .strokeBorder(Color.red.opacity(0.3), lineWidth: 0.5)
                        )
                )
            }
            .buttonStyle(.plain)
            .foregroundColor(.red)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 10)
        .background(.regularMaterial)
    }
}

// MARK: - Quick Action Card
struct QuickActionCard: View {
    let title: String
    let icon: String
    let color: Color
    let shortcut: String
    let isHovered: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.system(size: 18, weight: .medium))
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text(shortcut)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(height: 80)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        isHovered ? 
                        LinearGradient(
                            colors: [color.opacity(0.1), color.opacity(0.05)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ) :
                        LinearGradient(
                            colors: [Color.white.opacity(0.8), Color.gray.opacity(0.05)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .strokeBorder(
                                isHovered ? color.opacity(0.4) : Color.gray.opacity(0.2), 
                                lineWidth: isHovered ? 1.5 : 1
                            )
                    )
                    .shadow(
                        color: isHovered ? color.opacity(0.15) : Color.black.opacity(0.05),
                        radius: isHovered ? 4 : 2,
                        x: 0,
                        y: isHovered ? 2 : 1
                    )
            )
        }
        .buttonStyle(.plain)
        .scaleEffect(isHovered ? 1.02 : 1.0)
        .animation(.easeInOut(duration: 0.15), value: isHovered)
    }
}

// MARK: - Batch Row Card
struct BatchRowCard: View {
    let batch: NSManagedObject
    let isActive: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                Image(systemName: isActive ? "folder.fill" : "folder")
                    .foregroundColor(isActive ? .accentColor : .secondary)
                    .font(.system(size: 16))
                
                VStack(alignment: .leading, spacing: 2) {
                    let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
                    let itemCount = (batch.value(forKey: "contentItems") as? NSSet)?.count ?? 0
                    
                    Text(batchName)
                        .font(.caption)
                        .fontWeight(isActive ? .semibold : .regular)
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    
                    Text("\(itemCount) 个项目")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if isActive {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.accentColor)
                        .font(.system(size: 12))
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(
                        isActive ? 
                        LinearGradient(
                            colors: [Color.accentColor.opacity(0.15), Color.accentColor.opacity(0.08)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ) :
                        LinearGradient(
                            colors: [Color.white.opacity(0.6), Color.gray.opacity(0.03)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .strokeBorder(
                                isActive ? Color.accentColor.opacity(0.3) : Color.clear,
                                lineWidth: 1
                            )
                    )
                    .shadow(
                        color: isActive ? Color.accentColor.opacity(0.1) : Color.black.opacity(0.03),
                        radius: isActive ? 3 : 1,
                        x: 0,
                        y: 1
                    )
            )
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Status Row
struct StatusRow: View {
    let title: String
    let status: String
    let statusColor: Color
    let action: (() -> Void)?
    
    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.primary)
            
            Spacer()
            
            HStack(spacing: 6) {
                Circle()
                    .fill(statusColor)
                    .frame(width: 6, height: 6)
                
                Text(status)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                if let action = action {
                    Button("切换") {
                        action()
                    }
                    .font(.caption2)
                    .buttonStyle(.borderless)
                    .foregroundColor(.accentColor)
                }
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
    }
}