import Foundation
import SwiftUI
import UniformTypeIdentifiers

/// 文件类型管理器 - 处理文件类型识别、图标和打开方式
class FileTypeManager {
    static let shared = FileTypeManager()
    
    private init() {}
    
    // MARK: - 文件类型定义
    
    enum FileCategory: String, CaseIterable {
        case text = "text"
        case code = "code"
        case document = "document"
        case image = "image"
        case video = "video"
        case audio = "audio"
        case archive = "archive"
        case data = "data"
        case unknown = "unknown"
        
        var displayName: String {
            switch self {
            case .text: return "文本文件"
            case .code: return "代码文件"
            case .document: return "文档文件"
            case .image: return "图片文件"
            case .video: return "视频文件"
            case .audio: return "音频文件"
            case .archive: return "压缩文件"
            case .data: return "数据文件"
            case .unknown: return "未知文件"
            }
        }
        
        var color: Color {
            switch self {
            case .text: return .blue
            case .code: return .green
            case .document: return .orange
            case .image: return .purple
            case .video: return .red
            case .audio: return .pink
            case .archive: return .brown
            case .data: return .gray
            case .unknown: return .secondary
            }
        }
        
        var systemImage: String {
            switch self {
            case .text: return "doc.text"
            case .code: return "chevron.left.forwardslash.chevron.right"
            case .document: return "doc.richtext"
            case .image: return "photo"
            case .video: return "video"
            case .audio: return "music.note"
            case .archive: return "archivebox"
            case .data: return "tablecells"
            case .unknown: return "doc"
            }
        }
    }
    
    // MARK: - 文件类型映射
    
    private let fileTypeMapping: [String: FileCategory] = [
        // 文本文件
        "txt": .text,
        "rtf": .text,
        "log": .text,
        "readme": .text,
        
        // 代码文件
        "swift": .code,
        "py": .code,
        "js": .code,
        "ts": .code,
        "html": .code,
        "css": .code,
        "scss": .code,
        "less": .code,
        "json": .code,
        "xml": .code,
        "yaml": .code,
        "yml": .code,
        "toml": .code,
        "ini": .code,
        "cfg": .code,
        "conf": .code,
        "sh": .code,
        "bash": .code,
        "zsh": .code,
        "fish": .code,
        "ps1": .code,
        "bat": .code,
        "cmd": .code,
        "java": .code,
        "kt": .code,
        "scala": .code,
        "go": .code,
        "rs": .code,
        "c": .code,
        "cpp": .code,
        "cc": .code,
        "cxx": .code,
        "h": .code,
        "hpp": .code,
        "cs": .code,
        "php": .code,
        "rb": .code,
        "pl": .code,
        "r": .code,
        "m": .code,
        "mm": .code,
        "sql": .code,
        "dockerfile": .code,
        "makefile": .code,
        "cmake": .code,
        "gradle": .code,
        "pom": .code,
        
        // 文档文件
        "md": .document,
        "markdown": .document,
        "mdown": .document,
        "mkd": .document,
        "doc": .document,
        "docx": .document,
        "pdf": .document,
        "pages": .document,
        "odt": .document,
        "tex": .document,
        "latex": .document,
        "epub": .document,
        "mobi": .document,
        
        // 图片文件
        "jpg": .image,
        "jpeg": .image,
        "png": .image,
        "gif": .image,
        "bmp": .image,
        "tiff": .image,
        "tif": .image,
        "webp": .image,
        "svg": .image,
        "ico": .image,
        "icns": .image,
        "heic": .image,
        "heif": .image,
        "raw": .image,
        "cr2": .image,
        "nef": .image,
        "arw": .image,
        "dng": .image,
        
        // 视频文件
        "mp4": .video,
        "mov": .video,
        "avi": .video,
        "mkv": .video,
        "wmv": .video,
        "flv": .video,
        "webm": .video,
        "m4v": .video,
        "3gp": .video,
        "ogv": .video,
        
        // 音频文件
        "mp3": .audio,
        "wav": .audio,
        "flac": .audio,
        "aac": .audio,
        "ogg": .audio,
        "wma": .audio,
        "m4a": .audio,
        "opus": .audio,
        "aiff": .audio,
        "au": .audio,
        
        // 压缩文件
        "zip": .archive,
        "rar": .archive,
        "7z": .archive,
        "tar": .archive,
        "gz": .archive,
        "bz2": .archive,
        "xz": .archive,
        "dmg": .archive,
        "pkg": .archive,
        "deb": .archive,
        "rpm": .archive,
        
        // 数据文件
        "csv": .data,
        "tsv": .data,
        "xlsx": .data,
        "xls": .data,
        "numbers": .data,
        "ods": .data,
        "db": .data,
        "sqlite": .data,
        "sqlite3": .data,
        "plist": .data,
        "binary": .data,
        "dat": .data,
        "bin": .data
    ]
    
    // MARK: - 文件类型识别
    
    /// 根据文件URL识别文件类型
    func identifyFileType(for url: URL) -> FileCategory {
        let pathExtension = url.pathExtension.lowercased()
        
        // 首先检查扩展名映射
        if let category = fileTypeMapping[pathExtension] {
            return category
        }
        
        // 使用UTType进行更精确的识别
        if let utType = UTType(filenameExtension: pathExtension) {
            return identifyFileTypeFromUTType(utType)
        }
        
        // 特殊文件名处理
        let fileName = url.lastPathComponent.lowercased()
        if fileName.contains("readme") || fileName.contains("license") || fileName.contains("changelog") {
            return .document
        }
        
        if fileName.hasPrefix("makefile") || fileName.hasPrefix("dockerfile") {
            return .code
        }
        
        return .unknown
    }
    
    /// 根据UTType识别文件类型
    private func identifyFileTypeFromUTType(_ utType: UTType) -> FileCategory {
        if utType.conforms(to: .sourceCode) || utType.conforms(to: .script) {
            return .code
        } else if utType.conforms(to: .text) || utType.conforms(to: .plainText) {
            return .text
        } else if utType.conforms(to: .image) {
            return .image
        } else if utType.conforms(to: .movie) || utType.conforms(to: .video) {
            return .video
        } else if utType.conforms(to: .audio) {
            return .audio
        } else if utType.conforms(to: .archive) {
            return .archive
        } else if utType.conforms(to: .spreadsheet) || utType.conforms(to: .database) {
            return .data
        } else if utType.conforms(to: .pdf) {
            return .document
        } else if utType.conforms(to: .presentation) {
            return .document
        }
        
        return .unknown
    }
    
    // MARK: - 文件打开方式
    
    /// 获取文件的推荐打开方式
    func getRecommendedApps(for url: URL) -> [AppInfo] {
        let fileType = identifyFileType(for: url)
        let pathExtension = url.pathExtension.lowercased()
        
        var apps: [AppInfo] = []
        
        // 根据文件类型推荐应用
        switch fileType {
        case .text, .document:
            apps.append(contentsOf: getTextEditors())
            if pathExtension == "md" || pathExtension == "markdown" {
                apps.append(contentsOf: getMarkdownEditors())
            }
            
        case .code:
            apps.append(contentsOf: getCodeEditors())
            
        case .image:
            apps.append(contentsOf: getImageViewers())
            
        case .video:
            apps.append(contentsOf: getVideoPlayers())
            
        case .audio:
            apps.append(contentsOf: getAudioPlayers())
            
        case .archive:
            apps.append(contentsOf: getArchiveUtilities())
            
        case .data:
            apps.append(contentsOf: getDataViewers())
            
        case .unknown:
            apps.append(contentsOf: getGenericApps())
        }
        
        // 添加系统默认应用
        if let defaultApp = getSystemDefaultApp(for: url) {
            apps.insert(defaultApp, at: 0)
        }
        
        return Array(apps.prefix(5)) // 限制为前5个应用
    }
    
    // MARK: - 应用信息
    
    struct AppInfo {
        let name: String
        let bundleIdentifier: String
        let iconName: String
        let isDefault: Bool
        
        init(name: String, bundleIdentifier: String, iconName: String = "app", isDefault: Bool = false) {
            self.name = name
            self.bundleIdentifier = bundleIdentifier
            self.iconName = iconName
            self.isDefault = isDefault
        }
    }
    
    // MARK: - 应用列表
    
    private func getTextEditors() -> [AppInfo] {
        return [
            AppInfo(name: "TextEdit", bundleIdentifier: "com.apple.TextEdit", iconName: "doc.text"),
            AppInfo(name: "Sublime Text", bundleIdentifier: "com.sublimetext.4", iconName: "doc.text.fill"),
            AppInfo(name: "Visual Studio Code", bundleIdentifier: "com.microsoft.VSCode", iconName: "chevron.left.forwardslash.chevron.right"),
            AppInfo(name: "Atom", bundleIdentifier: "com.github.atom", iconName: "atom")
        ]
    }
    
    private func getMarkdownEditors() -> [AppInfo] {
        return [
            AppInfo(name: "Typora", bundleIdentifier: "abnerworks.Typora", iconName: "doc.richtext"),
            AppInfo(name: "MacDown", bundleIdentifier: "com.uranusjr.macdown", iconName: "doc.richtext"),
            AppInfo(name: "Mark Text", bundleIdentifier: "com.github.marktext", iconName: "doc.richtext.fill"),
            AppInfo(name: "Obsidian", bundleIdentifier: "md.obsidian", iconName: "doc.richtext.fill"),
            AppInfo(name: "Notion", bundleIdentifier: "notion.id", iconName: "doc.richtext"),
            AppInfo(name: "Logseq", bundleIdentifier: "com.logseq.logseq", iconName: "doc.richtext"),
            AppInfo(name: "Bear", bundleIdentifier: "net.shinyfrog.bear", iconName: "doc.richtext.fill"),
            AppInfo(name: "iA Writer", bundleIdentifier: "com.ia.writer", iconName: "doc.text.fill"),
            AppInfo(name: "Ulysses", bundleIdentifier: "com.ulyssesapp.mac", iconName: "doc.richtext"),
            AppInfo(name: "MindNode", bundleIdentifier: "com.ideasoncanvas.mindnode.macos", iconName: "doc.richtext")
        ]
    }
    
    private func getCodeEditors() -> [AppInfo] {
        return [
            AppInfo(name: "Xcode", bundleIdentifier: "com.apple.dt.Xcode", iconName: "hammer"),
            AppInfo(name: "Visual Studio Code", bundleIdentifier: "com.microsoft.VSCode", iconName: "chevron.left.forwardslash.chevron.right"),
            AppInfo(name: "Sublime Text", bundleIdentifier: "com.sublimetext.4", iconName: "doc.text.fill"),
            AppInfo(name: "IntelliJ IDEA", bundleIdentifier: "com.jetbrains.intellij", iconName: "lightbulb")
        ]
    }
    
    private func getImageViewers() -> [AppInfo] {
        return [
            AppInfo(name: "Preview", bundleIdentifier: "com.apple.Preview", iconName: "photo"),
            AppInfo(name: "Photoshop", bundleIdentifier: "com.adobe.Photoshop", iconName: "photo.fill"),
            AppInfo(name: "GIMP", bundleIdentifier: "org.gimp.gimp-2.10", iconName: "photo.on.rectangle")
        ]
    }
    
    private func getVideoPlayers() -> [AppInfo] {
        return [
            AppInfo(name: "QuickTime Player", bundleIdentifier: "com.apple.QuickTimePlayerX", iconName: "video"),
            AppInfo(name: "VLC", bundleIdentifier: "org.videolan.vlc", iconName: "video.fill"),
            AppInfo(name: "IINA", bundleIdentifier: "com.colliderli.iina", iconName: "video.circle")
        ]
    }
    
    private func getAudioPlayers() -> [AppInfo] {
        return [
            AppInfo(name: "Music", bundleIdentifier: "com.apple.Music", iconName: "music.note"),
            AppInfo(name: "Spotify", bundleIdentifier: "com.spotify.client", iconName: "music.note.list"),
            AppInfo(name: "Audacity", bundleIdentifier: "org.audacityteam.audacity", iconName: "waveform")
        ]
    }
    
    private func getArchiveUtilities() -> [AppInfo] {
        return [
            AppInfo(name: "Archive Utility", bundleIdentifier: "com.apple.archiveutility", iconName: "archivebox"),
            AppInfo(name: "The Unarchiver", bundleIdentifier: "cx.c3.theunarchiver", iconName: "archivebox.fill"),
            AppInfo(name: "Keka", bundleIdentifier: "com.aone.keka", iconName: "archivebox.circle")
        ]
    }
    
    private func getDataViewers() -> [AppInfo] {
        return [
            AppInfo(name: "Numbers", bundleIdentifier: "com.apple.iWork.Numbers", iconName: "tablecells"),
            AppInfo(name: "Excel", bundleIdentifier: "com.microsoft.Excel", iconName: "tablecells.fill"),
            AppInfo(name: "DB Browser for SQLite", bundleIdentifier: "net.sourceforge.sqlitebrowser", iconName: "cylinder")
        ]
    }
    
    private func getGenericApps() -> [AppInfo] {
        return [
            AppInfo(name: "TextEdit", bundleIdentifier: "com.apple.TextEdit", iconName: "doc.text"),
            AppInfo(name: "Preview", bundleIdentifier: "com.apple.Preview", iconName: "doc.magnifyingglass")
        ]
    }
    
    /// 获取系统默认应用
    private func getSystemDefaultApp(for url: URL) -> AppInfo? {
        if let appURL = NSWorkspace.shared.urlForApplication(toOpen: url),
           let bundle = Bundle(url: appURL) {
            let appName = bundle.object(forInfoDictionaryKey: "CFBundleDisplayName") as? String ??
                         bundle.object(forInfoDictionaryKey: "CFBundleName") as? String ??
                         appURL.deletingPathExtension().lastPathComponent
            
            let bundleIdentifier = bundle.bundleIdentifier ?? ""
            
            return AppInfo(name: appName, bundleIdentifier: bundleIdentifier, iconName: "app.badge", isDefault: true)
        }
        
        return nil
    }
    
    // MARK: - 文件操作
    
    /// 使用指定应用打开文件
    func openFile(_ url: URL, with app: AppInfo) -> Bool {
        if app.isDefault {
            return NSWorkspace.shared.open(url)
        } else {
            if let appURL = NSWorkspace.shared.urlForApplication(withBundleIdentifier: app.bundleIdentifier) {
                return NSWorkspace.shared.open([url], withApplicationAt: appURL, configuration: NSWorkspace.OpenConfiguration()) == nil
            }
        }
        return false
    }
    
    /// 使用系统默认应用打开文件
    func openFileWithDefault(_ url: URL) -> Bool {
        return NSWorkspace.shared.open(url)
    }
}