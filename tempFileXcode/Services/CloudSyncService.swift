import Foundation
import CloudKit
import Combine
import CoreData
import os.log

// MARK: - Cloud Sync Service Protocol
protocol CloudSyncServiceProtocol: ObservableObject {
    var isEnabled: Bool { get }
    var syncStatus: SyncStatus { get }
    var lastSyncDate: Date? { get }
    
    func enableSync() async throws
    func disableSync() async
    func syncBatch(_ batch: NSManagedObject) async throws
    func syncContentItem(_ item: ContentItem) async throws
    func downloadBatch(with recordID: CKRecord.ID) async throws -> BatchSyncData
    func shareBatch(_ batch: NSManagedObject) async throws -> CKShare
    func acceptSharedBatch(from url: URL) async throws
}

// MARK: - Sync Status
enum SyncStatus: String, CaseIterable {
    case disabled = "disabled"
    case idle = "idle"
    case syncing = "syncing"
    case error = "error"
    
    var displayName: String {
        switch self {
        case .disabled:
            return "已禁用"
        case .idle:
            return "空闲"
        case .syncing:
            return "同步中"
        case .error:
            return "错误"
        }
    }
}

// MARK: - Batch Sync Data
struct BatchSyncData {
    let batchInfo: BatchInfo
    let contentItems: [ContentSyncData]
}

struct BatchInfo {
    let id: UUID
    let name: String
    let createdAt: Date
    let updatedAt: Date
    let tags: [String]
    let description: String?
}

struct ContentSyncData {
    let id: UUID
    let title: String?
    let content: String?
    let contentType: String
    let fileName: String?
    let fileData: Data?
    let tags: [String]
    let createdAt: Date
    let updatedAt: Date
    let notes: String?
}

// MARK: - Cloud Sync Service Implementation
@MainActor
class CloudSyncService: CloudSyncServiceProtocol {
    
    // MARK: - Published Properties
    @Published private(set) var isEnabled: Bool = false
    @Published private(set) var syncStatus: SyncStatus = .disabled
    @Published private(set) var lastSyncDate: Date?
    
    // MARK: - Private Properties
    private let container: CKContainer
    private let privateDatabase: CKDatabase
    private let sharedDatabase: CKDatabase
    private let contentService: ContentService
    private let batchService: BatchService
    private let logger = Logger(subsystem: "com.contentmanager.app", category: "CloudSync")
    
    private var cancellables = Set<AnyCancellable>()
    
    // CloudKit Record Types
    private let batchRecordType = "Batch"
    private let contentRecordType = "ContentItem"
    private let fileAssetRecordType = "FileAsset"
    
    init(contentService: ContentService, batchService: BatchService) {
        self.contentService = contentService
        self.batchService = batchService
        self.container = CKContainer(identifier: "iCloud.com.contentmanager.app") // 需要配置实际的iCloud容器ID
        self.privateDatabase = container.privateCloudDatabase
        self.sharedDatabase = container.sharedCloudDatabase
        
        loadSyncSettings()
        setupNotifications()
    }
    
    // MARK: - Setup
    private func loadSyncSettings() {
        isEnabled = UserDefaults.standard.bool(forKey: "CloudSyncEnabled")
        lastSyncDate = UserDefaults.standard.object(forKey: "LastSyncDate") as? Date
        
        if isEnabled {
            syncStatus = .idle
        }
    }
    
    private func setupNotifications() {
        // 监听iCloud账户状态变化
        NotificationCenter.default.publisher(for: .CKAccountChanged)
            .sink { [weak self] _ in
                Task {
                    await self?.checkAccountStatus()
                }
            }
            .store(in: &cancellables)
    }
    
    private func checkAccountStatus() async {
        do {
            let status = try await container.accountStatus()
            switch status {
            case .available:
                logger.info("iCloud account is available")
            case .noAccount:
                logger.warning("No iCloud account configured")
                await disableSync()
            case .restricted:
                logger.warning("iCloud account is restricted")
                await disableSync()
            case .couldNotDetermine:
                logger.error("Could not determine iCloud account status")
            case .temporarilyUnavailable:
                logger.warning("iCloud account temporarily unavailable")
            @unknown default:
                logger.error("Unknown iCloud account status")
            }
        } catch {
            logger.error("Failed to check account status: \(error)")
        }
    }
    
    // MARK: - Sync Control
    func enableSync() async throws {
        logger.info("Enabling cloud sync")
        syncStatus = .syncing
        
        // 检查iCloud账户状态
        let accountStatus = try await container.accountStatus()
        guard accountStatus == .available else {
            syncStatus = .error
            throw CloudSyncError.accountNotAvailable
        }
        
        // 注：从macOS 14.0开始，用户发现性权限已被废弃
        // 不再需要请求权限，直接继续
        
        // 设置订阅以接收远程更改
        try await setupSubscriptions()
        
        // 启用同步
        isEnabled = true
        syncStatus = .idle
        UserDefaults.standard.set(true, forKey: "CloudSyncEnabled")
        
        logger.info("Cloud sync enabled successfully")
    }
    
    func disableSync() async {
        logger.info("Disabling cloud sync")
        
        isEnabled = false
        syncStatus = .disabled
        UserDefaults.standard.set(false, forKey: "CloudSyncEnabled")
        
        // 移除订阅
        await removeSubscriptions()
        
        logger.info("Cloud sync disabled")
    }
    
    private func setupSubscriptions() async throws {
        // 创建批次记录订阅
        let batchSubscription = CKQuerySubscription(
            recordType: batchRecordType,
            predicate: NSPredicate(value: true),
            subscriptionID: "batch-changes"
        )
        
        let batchNotification = CKSubscription.NotificationInfo()
        batchNotification.shouldSendContentAvailable = true
        batchSubscription.notificationInfo = batchNotification
        
        // 创建内容项记录订阅
        let contentSubscription = CKQuerySubscription(
            recordType: contentRecordType,
            predicate: NSPredicate(value: true),
            subscriptionID: "content-changes"
        )
        
        let contentNotification = CKSubscription.NotificationInfo()
        contentNotification.shouldSendContentAvailable = true
        contentSubscription.notificationInfo = contentNotification
        
        // 保存订阅
        try await privateDatabase.save(batchSubscription)
        try await privateDatabase.save(contentSubscription)
        
        logger.info("CloudKit subscriptions created")
    }
    
    private func removeSubscriptions() async {
        do {
            let subscriptionIDs = ["batch-changes", "content-changes"]
            for subscriptionID in subscriptionIDs {
                try await privateDatabase.deleteSubscription(withID: subscriptionID)
            }
            logger.info("CloudKit subscriptions removed")
        } catch {
            logger.error("Failed to remove subscriptions: \(error)")
        }
    }
    
    // MARK: - Batch Sync
    func syncBatch(_ batch: NSManagedObject) async throws {
        guard isEnabled else { throw CloudSyncError.syncDisabled }
        
        logger.info("Syncing batch to iCloud")
        syncStatus = .syncing
        
        do {
            // 创建批次记录
            let batchRecord = try await createBatchRecord(from: batch)
            
            // 获取批次中的内容项
            let contentItems = try await getContentItemsForBatch(batch)
            
            // 同步内容项
            var contentRecords: [CKRecord] = []
            for item in contentItems {
                let contentRecord = try await createContentRecord(from: item, batchRecord: batchRecord)
                contentRecords.append(contentRecord)
            }
            
            // 批量保存记录
            let operation = CKModifyRecordsOperation(recordsToSave: [batchRecord] + contentRecords)
            operation.savePolicy = .changedKeys
            operation.qualityOfService = .userInitiated
            
            try await withCheckedThrowingContinuation { continuation in
                operation.modifyRecordsResultBlock = { result in
                    switch result {
                    case .success:
                        continuation.resume()
                    case .failure(let error):
                        continuation.resume(throwing: error)
                    }
                }
                privateDatabase.add(operation)
            }
            
            syncStatus = .idle
            lastSyncDate = Date()
            UserDefaults.standard.set(lastSyncDate, forKey: "LastSyncDate")
            
            logger.info("Batch synced successfully")
            
        } catch {
            syncStatus = .error
            logger.error("Failed to sync batch: \(error)")
            throw CloudSyncError.syncFailed(error)
        }
    }
    
    func syncContentItem(_ item: ContentItem) async throws {
        guard isEnabled else { throw CloudSyncError.syncDisabled }
        
        logger.info("Syncing content item to iCloud")
        
        do {
            let record = try await createContentRecord(from: item, batchRecord: nil)
            try await privateDatabase.save(record)
            
            logger.info("Content item synced successfully")
            
        } catch {
            logger.error("Failed to sync content item: \(error)")
            throw CloudSyncError.syncFailed(error)
        }
    }
    
    // MARK: - Helper Methods
    private func getContentItemsForBatch(_ batch: NSManagedObject) async throws -> [ContentItem] {
        // 使用ContentService获取批次中的内容项
        return try await contentService.getContentForBatch(batch)
    }
    
    // MARK: - Record Creation
    private func createBatchRecord(from batch: NSManagedObject) async throws -> CKRecord {
        guard let batchId = batch.value(forKey: "id") as? UUID else {
            throw CloudSyncError.invalidData("Batch ID missing")
        }
        
        let recordID = CKRecord.ID(recordName: batchId.uuidString)
        let record = CKRecord(recordType: batchRecordType, recordID: recordID)
        
        record["name"] = batch.value(forKey: "name") as? String ?? "Untitled Batch"
        record["createdAt"] = batch.value(forKey: "createdAt") as? Date ?? Date()
        record["updatedAt"] = batch.value(forKey: "updatedAt") as? Date ?? Date()
        record["itemCount"] = try await getContentItemsForBatch(batch).count
        
        // 添加描述和标签（如果有的话）
        record["description"] = batch.value(forKey: "batchDescription") as? String
        
        return record
    }
    
    private func createContentRecord(from item: ContentItem, batchRecord: CKRecord?) async throws -> CKRecord {
        guard let itemId = item.id else {
            throw CloudSyncError.invalidData("Content item ID missing")
        }
        
        let recordID = CKRecord.ID(recordName: itemId.uuidString)
        let record = CKRecord(recordType: contentRecordType, recordID: recordID)
        
        // 基本信息
        record["title"] = item.title
        record["content"] = item.content
        record["contentType"] = item.contentType
        record["fileName"] = item.fileName
        record["fileSize"] = item.fileSize
        record["mimeType"] = item.mimeType
        record["notes"] = item.notes
        record["createdAt"] = item.createdAt ?? Date()
        record["updatedAt"] = item.updatedAt ?? Date()
        record["isPermanent"] = item.isPermanent
        record["expiresAt"] = item.expiresAt
        
        // 标签
        record["tags"] = item.tagNames
        
        // 关联批次
        if let batchRecord = batchRecord {
            record["batch"] = CKRecord.Reference(record: batchRecord, action: .deleteSelf)
        }
        
        // 处理文件数据
        if item.contentTypeEnum == .file || item.contentTypeEnum == .image {
            if let fileData = try await contentService.loadContentFile(for: item) {
                let asset = try await createFileAsset(from: fileData, fileName: item.fileName)
                record["fileAsset"] = asset
            }
        }
        
        return record
    }
    
    private func createFileAsset(from data: Data, fileName: String?) async throws -> CKAsset {
        let tempURL = FileManager.default.temporaryDirectory
            .appendingPathComponent(fileName ?? UUID().uuidString)
        
        try data.write(to: tempURL)
        return CKAsset(fileURL: tempURL)
    }
    
    // MARK: - Download and Sharing
    func downloadBatch(with recordID: CKRecord.ID) async throws -> BatchSyncData {
        logger.info("Downloading batch from iCloud")
        
        do {
            // 下载批次记录
            let batchRecord = try await privateDatabase.record(for: recordID)
            
            // 查询相关的内容项
            let predicate = NSPredicate(format: "batch == %@", CKRecord.Reference(recordID: recordID, action: .none))
            let query = CKQuery(recordType: contentRecordType, predicate: predicate)
            
            let (matchResults, _) = try await privateDatabase.records(matching: query)
            
            // 转换为本地数据结构
            let batchInfo = BatchInfo(
                id: UUID(uuidString: recordID.recordName) ?? UUID(),
                name: batchRecord["name"] as? String ?? "Downloaded Batch",
                createdAt: batchRecord["createdAt"] as? Date ?? Date(),
                updatedAt: batchRecord["updatedAt"] as? Date ?? Date(),
                tags: [],
                description: batchRecord["description"] as? String
            )
            
            var contentItems: [ContentSyncData] = []
            for (_, result) in matchResults {
                switch result {
                case .success(let record):
                    let contentData = try await createContentSyncData(from: record)
                    contentItems.append(contentData)
                case .failure(let error):
                    logger.error("Failed to download content record: \(error)")
                }
            }
            
            logger.info("Downloaded batch with \(contentItems.count) items")
            return BatchSyncData(batchInfo: batchInfo, contentItems: contentItems)
            
        } catch {
            logger.error("Failed to download batch: \(error)")
            throw CloudSyncError.downloadFailed(error)
        }
    }
    
    private func createContentSyncData(from record: CKRecord) async throws -> ContentSyncData {
        var fileData: Data?
        
        // 下载文件数据（如果有）
        if let asset = record["fileAsset"] as? CKAsset,
           let fileURL = asset.fileURL {
            fileData = try Data(contentsOf: fileURL)
        }
        
        return ContentSyncData(
            id: UUID(uuidString: record.recordID.recordName) ?? UUID(),
            title: record["title"] as? String,
            content: record["content"] as? String,
            contentType: record["contentType"] as? String ?? "text",
            fileName: record["fileName"] as? String,
            fileData: fileData,
            tags: record["tags"] as? [String] ?? [],
            createdAt: record["createdAt"] as? Date ?? Date(),
            updatedAt: record["updatedAt"] as? Date ?? Date(),
            notes: record["notes"] as? String
        )
    }
    
    // MARK: - Sharing
    func shareBatch(_ batch: NSManagedObject) async throws -> CKShare {
        guard let batchId = batch.value(forKey: "id") as? UUID else {
            throw CloudSyncError.invalidData("Batch ID missing")
        }
        
        logger.info("Creating share for batch")
        
        let recordID = CKRecord.ID(recordName: batchId.uuidString)
        
        do {
            // 获取或创建批次记录
            let batchRecord: CKRecord
            do {
                batchRecord = try await privateDatabase.record(for: recordID)
            } catch {
                // 如果记录不存在，先同步批次
                try await syncBatch(batch)
                batchRecord = try await privateDatabase.record(for: recordID)
            }
            
            // 创建分享
            let share = CKShare(rootRecord: batchRecord)
            share[CKShare.SystemFieldKey.title] = batch.value(forKey: "name") as? String ?? "Shared Batch"
            share.publicPermission = .readOnly
            
            // 保存分享
            let operation = CKModifyRecordsOperation(recordsToSave: [batchRecord, share])
            operation.savePolicy = .changedKeys
            
            try await withCheckedThrowingContinuation { continuation in
                operation.modifyRecordsResultBlock = { result in
                    switch result {
                    case .success:
                        continuation.resume()
                    case .failure(let error):
                        continuation.resume(throwing: error)
                    }
                }
                privateDatabase.add(operation)
            }
            
            logger.info("Batch shared successfully")
            return share
            
        } catch {
            logger.error("Failed to share batch: \(error)")
            throw CloudSyncError.sharingFailed(error)
        }
    }
    
    func acceptSharedBatch(from url: URL) async throws {
        logger.info("Accepting shared batch from URL")
        
        do {
            let shareMetadata = try await container.shareMetadata(for: url)
            let acceptShareOperation = CKAcceptSharesOperation(shareMetadatas: [shareMetadata])
            
            try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
                acceptShareOperation.acceptSharesResultBlock = { result in
                    switch result {
                    case .success:
                        continuation.resume()
                    case .failure(let error):
                        continuation.resume(throwing: error)
                    }
                }
                container.add(acceptShareOperation)
            }
            
            // 下载共享的批次数据
            let rootRecordID = shareMetadata.rootRecord?.recordID ?? CKRecord.ID(recordName: "unknown")
            let batchData = try await downloadBatch(with: rootRecordID)
            
            // 创建本地批次
            try await createLocalBatch(from: batchData)
            
            logger.info("Shared batch accepted and imported successfully")
            
        } catch {
            logger.error("Failed to accept shared batch: \(error)")
            throw CloudSyncError.sharingFailed(error)
        }
    }
    
    private func createLocalBatch(from batchData: BatchSyncData) async throws {
        // 创建新批次
        let newBatch = try batchService.createNewBatch(name: batchData.batchInfo.name)
        
        // 设置批次信息
        newBatch.setValue(batchData.batchInfo.name + " (Shared)", forKey: "name")
        newBatch.setValue(Date(), forKey: "createdAt")
        newBatch.setValue(Date(), forKey: "updatedAt")
        
        // 添加内容项
        for contentData in batchData.contentItems {
            let contentDataStruct = ContentData(
                id: UUID(),
                title: contentData.title,
                content: contentData.content,
                contentType: ContentType(rawValue: contentData.contentType) ?? .text,
                data: contentData.fileData,
                filePath: nil,
                fileName: contentData.fileName,
                fileSize: Int64(contentData.fileData?.count ?? 0),
                mimeType: nil,
                notes: contentData.notes,
                tags: contentData.tags,
                createdAt: contentData.createdAt,
                updatedAt: contentData.updatedAt,
                expiresAt: nil,
                isPermanent: false
            )
            
            _ = try await contentService.addContent(contentDataStruct, toBatch: newBatch)
        }
        
        logger.info("Local batch created from shared data")
    }
}

// MARK: - Cloud Sync Error
enum CloudSyncError: LocalizedError {
    case syncDisabled
    case accountNotAvailable
    case invalidData(String)
    case syncFailed(Error)
    case downloadFailed(Error)
    case sharingFailed(Error)
    
    var errorDescription: String? {
        switch self {
        case .syncDisabled:
            return "云同步已禁用"
        case .accountNotAvailable:
            return "iCloud账户不可用"
        case .invalidData(let message):
            return "数据无效: \(message)"
        case .syncFailed(let error):
            return "同步失败: \(error.localizedDescription)"
        case .downloadFailed(let error):
            return "下载失败: \(error.localizedDescription)"
        case .sharingFailed(let error):
            return "分享失败: \(error.localizedDescription)"
        }
    }
}