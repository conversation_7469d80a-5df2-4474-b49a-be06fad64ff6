import Foundation
import AppKit
import UniformTypeIdentifiers
import Combine
import os.log

// MARK: - Export Service Protocol
protocol ExportServiceProtocol: ObservableObject {
    func exportAsZip(_ items: [ContentItem], to url: URL) async throws
    func exportAsMarkdown(_ items: [ContentItem], to url: URL) async throws
    func exportIndividualFiles(_ items: [ContentItem], to url: URL) async throws
    func exportSingleItem(_ item: ContentItem, to url: URL) async throws
    func getExportProgress() -> ExportProgress
}

// MARK: - Export Progress
struct ExportProgress {
    let currentItem: Int
    let totalItems: Int
    let currentOperation: String
    let isCompleted: Bool
    let error: Error?
    
    var percentage: Double {
        guard totalItems > 0 else { return 0 }
        return Double(currentItem) / Double(totalItems)
    }
}

// MARK: - Export Service Implementation
@MainActor
class ExportService: ExportServiceProtocol {
    
    // MARK: - Properties
    @Published private(set) var exportProgress = ExportProgress(
        currentItem: 0,
        totalItems: 0,
        currentOperation: "",
        isCompleted: true,
        error: nil
    )
    
    private let contentService: ContentService
    private let fileManager = FileManager.default
    
    // MARK: - Initialization
    init(contentService: ContentService) {
        self.contentService = contentService
    }
    
    // MARK: - Export Progress
    func getExportProgress() -> ExportProgress {
        return exportProgress
    }
    
    private func updateProgress(current: Int, total: Int, operation: String, error: Error? = nil) {
        exportProgress = ExportProgress(
            currentItem: current,
            totalItems: total,
            currentOperation: operation,
            isCompleted: current >= total && error == nil,
            error: error
        )
    }
    
    // MARK: - ZIP Export
    func exportAsZip(_ items: [ContentItem], to url: URL) async throws {
        Logger.fileOperations.info("Starting ZIP export of \(items.count) items to \(url.path)")
        
        updateProgress(current: 0, total: items.count, operation: "准备导出...")
        
        // Create temporary directory for export
        let tempDir = fileManager.temporaryDirectory.appendingPathComponent(UUID().uuidString)
        try fileManager.createDirectory(at: tempDir, withIntermediateDirectories: true)
        
        defer {
            // Cleanup temporary directory
            try? fileManager.removeItem(at: tempDir)
        }
        
        do {
            // Export items to temporary directory
            for (index, item) in items.enumerated() {
                updateProgress(current: index, total: items.count, operation: "导出 \(item.displayTitle)")
                
                try await exportItemToDirectory(item, directory: tempDir)
            }
            
            updateProgress(current: items.count, total: items.count, operation: "创建 ZIP 文件...")
            
            // Create ZIP file
            try await createZipFile(from: tempDir, to: url)
            
            updateProgress(current: items.count, total: items.count, operation: "导出完成")
            Logger.fileOperations.info("ZIP export completed successfully")
            
        } catch {
            updateProgress(current: 0, total: items.count, operation: "导出失败", error: error)
            Logger.fileOperations.error("ZIP export failed: \(error)")
            throw error
        }
    }
    
    private func exportItemToDirectory(_ item: ContentItem, directory: URL) async throws {
        let itemDir = directory.appendingPathComponent(sanitizeFileName(item.displayTitle))
        try fileManager.createDirectory(at: itemDir, withIntermediateDirectories: true)
        
        // Export metadata
        let metadata = createItemMetadata(item)
        let metadataURL = itemDir.appendingPathComponent("metadata.json")
        try metadata.write(to: metadataURL)
        
        // Export content based on type
        switch item.contentTypeEnum {
        case .text:
            if let content = item.content {
                let contentURL = itemDir.appendingPathComponent("content.txt")
                try content.write(to: contentURL, atomically: true, encoding: .utf8)
            }
            
        case .image, .file:
            if let fileData = try await contentService.loadContentFile(for: item) {
                let fileName = item.fileName ?? "content"
                let fileURL = itemDir.appendingPathComponent(fileName)
                try fileData.write(to: fileURL)
            }
        }
        
        // Export notes if available
        if let notes = item.notes, !notes.isEmpty {
            let notesURL = itemDir.appendingPathComponent("notes.txt")
            try notes.write(to: notesURL, atomically: true, encoding: .utf8)
        }
    }
    
    private func createItemMetadata(_ item: ContentItem) -> Data {
        let metadata: [String: Any] = [
            "id": item.id?.uuidString ?? UUID().uuidString,
            "title": item.title ?? "",
            "contentType": item.contentType ?? "",
            "fileName": item.fileName ?? "",
            "fileSize": item.fileSize,
            "mimeType": item.mimeType ?? "",
            "tags": item.tagNames,
            "createdAt": ISO8601DateFormatter().string(from: item.createdAt ?? Date()),
            "updatedAt": ISO8601DateFormatter().string(from: item.updatedAt ?? Date()),
            "isPermanent": item.isPermanent,
            "expiresAt": item.expiresAt.map { ISO8601DateFormatter().string(from: $0) } ?? NSNull()
        ]
        
        return try! JSONSerialization.data(withJSONObject: metadata, options: .prettyPrinted)
    }
    
    private func createZipFile(from sourceDir: URL, to destinationURL: URL) async throws {
        // Use NSTask to run the zip command
        let process = Process()
        process.executableURL = URL(fileURLWithPath: "/usr/bin/zip")
        process.arguments = ["-r", destinationURL.path, "."]
        process.currentDirectoryURL = sourceDir
        
        let pipe = Pipe()
        process.standardOutput = pipe
        process.standardError = pipe
        
        try process.run()
        process.waitUntilExit()
        
        if process.terminationStatus != 0 {
            let data = pipe.fileHandleForReading.readDataToEndOfFile()
            let output = String(data: data, encoding: .utf8) ?? "Unknown error"
            throw ContentManagerError.exportError("ZIP creation failed: \(output)")
        }
    }
    
    // MARK: - Markdown Export
    func exportAsMarkdown(_ items: [ContentItem], to url: URL) async throws {
        Logger.fileOperations.info("Starting Markdown export of \(items.count) items to \(url.path)")
        
        updateProgress(current: 0, total: items.count, operation: "生成 Markdown...")
        
        do {
            var markdown = generateMarkdownHeader(itemCount: items.count)
            
            // Group items by type for better organization
            let groupedItems = Dictionary(grouping: items) { $0.contentTypeEnum }
            
            for contentType in ContentType.allCases {
                guard let typeItems = groupedItems[contentType], !typeItems.isEmpty else { continue }
                
                markdown += "\n## \(contentType.displayName) (\(typeItems.count))\n\n"
                
                for (index, item) in typeItems.enumerated() {
                    updateProgress(
                        current: index,
                        total: items.count,
                        operation: "处理 \(item.displayTitle)"
                    )
                    
                    markdown += generateMarkdownForItem(item)
                    markdown += "\n---\n\n"
                }
            }
            
            // Write markdown to file
            try markdown.write(to: url, atomically: true, encoding: .utf8)
            
            updateProgress(current: items.count, total: items.count, operation: "导出完成")
            Logger.fileOperations.info("Markdown export completed successfully")
            
        } catch {
            updateProgress(current: 0, total: items.count, operation: "导出失败", error: error)
            Logger.fileOperations.error("Markdown export failed: \(error)")
            throw error
        }
    }
    
    private func generateMarkdownHeader(itemCount: Int) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .short
        
        return """
        # TempBox 导出
        
        **导出时间:** \(formatter.string(from: Date()))
        **项目数量:** \(itemCount)
        
        """
    }
    
    private func generateMarkdownForItem(_ item: ContentItem) -> String {
        var markdown = "### \(item.displayTitle)\n\n"
        
        // Metadata table
        markdown += "| 属性 | 值 |\n"
        markdown += "|------|----|\n"
        markdown += "| 类型 | \(item.contentTypeEnum.displayName) |\n"
        markdown += "| 创建时间 | \(formatDate(item.createdAt ?? Date())) |\n"
        markdown += "| 更新时间 | \(formatDate(item.updatedAt ?? Date())) |\n"
        markdown += "| 文件大小 | \(item.formattedFileSize) |\n"
        
        if !item.tagNames.isEmpty {
            markdown += "| 标签 | \(item.tagNames.joined(separator: ", ")) |\n"
        }
        
        if let expiresAt = item.expiresAt {
            markdown += "| 过期时间 | \(formatDate(expiresAt)) |\n"
        }
        
        markdown += "\n"
        
        // Content based on type
        switch item.contentTypeEnum {
        case .text:
            if let content = item.content {
                markdown += "**内容:**\n\n```\n\(content)\n```\n\n"
            }
            
        case .image:
            if let fileName = item.fileName {
                markdown += "**图片文件:** `\(fileName)`\n\n"
                markdown += "*注意: 图片文件需要单独导出*\n\n"
            }
            
        case .file:
            if let fileName = item.fileName {
                markdown += "**文件:** `\(fileName)`\n\n"
                if let mimeType = item.mimeType {
                    markdown += "**MIME 类型:** `\(mimeType)`\n\n"
                }
                markdown += "*注意: 文件需要单独导出*\n\n"
            }
        }
        
        // Notes
        if let notes = item.notes, !notes.isEmpty {
            markdown += "**备注:**\n\n\(notes)\n\n"
        }
        
        return markdown
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
    
    // MARK: - Individual Files Export
    func exportIndividualFiles(_ items: [ContentItem], to url: URL) async throws {
        Logger.fileOperations.info("Starting individual files export of \(items.count) items to \(url.path)")
        
        updateProgress(current: 0, total: items.count, operation: "准备导出...")
        
        // Ensure the target directory exists
        try fileManager.createDirectory(at: url, withIntermediateDirectories: true, attributes: nil)
        
        do {
            for (index, item) in items.enumerated() {
                updateProgress(current: index, total: items.count, operation: "导出 \(item.displayTitle)")
                
                let fileName = generateUniqueFileName(for: item, in: url)
                let fileURL = url.appendingPathComponent(fileName)
                
                switch item.contentTypeEnum {
                case .text:
                    if let content = item.content {
                        try content.write(to: fileURL, atomically: true, encoding: .utf8)
                    }
                    
                case .image, .file:
                    if let fileData = try await contentService.loadContentFile(for: item) {
                        try fileData.write(to: fileURL)
                    }
                }
                
                // Also export metadata for each file
                if items.count > 1 {
                    let metadataFileName = fileName.replacingOccurrences(of: ".", with: "_metadata.")
                        .replacingOccurrences(of: "_metadata.", with: "_metadata.json")
                    let metadataURL = url.appendingPathComponent(metadataFileName)
                    let metadata = createItemMetadata(item)
                    try metadata.write(to: metadataURL)
                }
            }
            
            updateProgress(current: items.count, total: items.count, operation: "导出完成")
            Logger.fileOperations.info("Individual files export completed successfully")
            
        } catch {
            updateProgress(current: 0, total: items.count, operation: "导出失败", error: error)
            Logger.fileOperations.error("Individual files export failed: \(error)")
            throw error
        }
    }
    
    private func generateUniqueFileName(for item: ContentItem, in directory: URL) -> String {
        var baseName: String
        var fileExtension: String
        
        switch item.contentTypeEnum {
        case .text:
            baseName = item.title ?? "content"
            fileExtension = "txt"
            
        case .image, .file:
            if let fileName = item.fileName {
                let url = URL(fileURLWithPath: fileName)
                baseName = url.deletingPathExtension().lastPathComponent
                fileExtension = url.pathExtension.isEmpty ? "dat" : url.pathExtension
            } else {
                baseName = item.displayTitle
                fileExtension = item.contentTypeEnum == .image ? "png" : "dat"
            }
        }
        
        baseName = sanitizeFileName(baseName)
        var fileName = "\(baseName).\(fileExtension)"
        
        // Ensure uniqueness
        var counter = 1
        while fileManager.fileExists(atPath: directory.appendingPathComponent(fileName).path) {
            fileName = "\(baseName)_\(counter).\(fileExtension)"
            counter += 1
        }
        
        return fileName
    }
    
    // MARK: - Single Item Export
    func exportSingleItem(_ item: ContentItem, to url: URL) async throws {
        Logger.fileOperations.info("Exporting single item \(item.displayTitle) to \(url.path)")
        
        updateProgress(current: 0, total: 1, operation: "导出 \(item.displayTitle)")
        
        do {
            switch item.contentTypeEnum {
            case .text:
                if let content = item.content {
                    try content.write(to: url, atomically: true, encoding: .utf8)
                } else {
                    throw ContentManagerError.exportError("Text content is empty")
                }
                
            case .image, .file:
                if let fileData = try await contentService.loadContentFile(for: item) {
                    try fileData.write(to: url)
                } else {
                    throw ContentManagerError.exportError("File data not found")
                }
            }
            
            updateProgress(current: 1, total: 1, operation: "导出完成")
            Logger.fileOperations.info("Single item export completed successfully")
            
        } catch {
            updateProgress(current: 0, total: 1, operation: "导出失败", error: error)
            Logger.fileOperations.error("Single item export failed: \(error)")
            throw error
        }
    }
    
    // MARK: - Utility Methods
    private func sanitizeFileName(_ fileName: String) -> String {
        let invalidCharacters = CharacterSet(charactersIn: ":/\\?%*|\"<>")
        return fileName.components(separatedBy: invalidCharacters).joined(separator: "_")
    }
    
    // MARK: - Export Options
    func showExportDialog(for items: [ContentItem]) -> URL? {
        let savePanel = NSSavePanel()
        savePanel.title = "导出内容"
        savePanel.message = "选择导出位置和格式"
        savePanel.canCreateDirectories = true
        savePanel.allowedContentTypes = [.zip, .plainText]
        savePanel.nameFieldStringValue = "TempBox_Export_\(Date().timeIntervalSince1970)"
        
        if savePanel.runModal() == .OK {
            return savePanel.url
        }
        
        return nil
    }
    
    func showSingleItemExportDialog(for item: ContentItem) -> URL? {
        let savePanel = NSSavePanel()
        savePanel.title = "导出 \(item.displayTitle)"
        savePanel.canCreateDirectories = true
        
        // Set appropriate file extension based on content type
        switch item.contentTypeEnum {
        case .text:
            savePanel.allowedContentTypes = [.plainText]
            savePanel.nameFieldStringValue = (item.title ?? "content") + ".txt"
            
        case .image:
            savePanel.allowedContentTypes = [.png, .jpeg, .tiff]
            savePanel.nameFieldStringValue = item.fileName ?? "image.png"
            
        case .file:
            if let fileName = item.fileName {
                savePanel.nameFieldStringValue = fileName
            }
        }
        
        if savePanel.runModal() == .OK {
            return savePanel.url
        }
        
        return nil
    }
}

// MARK: - Export Format Enum
enum ExportFormat: String, CaseIterable {
    case zip = "zip"
    case markdown = "markdown"
    case individual = "individual"
    
    var displayName: String {
        switch self {
        case .zip:
            return "ZIP 压缩包"
        case .markdown:
            return "Markdown 文档"
        case .individual:
            return "单独文件"
        }
    }
    
    var fileExtension: String {
        switch self {
        case .zip:
            return "zip"
        case .markdown:
            return "md"
        case .individual:
            return ""
        }
    }
    
    var utType: UTType {
        switch self {
        case .zip:
            return .zip
        case .markdown:
            return .plainText
        case .individual:
            return .data
        }
    }
}

// MARK: - Export Statistics
struct ExportStatistics {
    let totalItems: Int
    let exportedItems: Int
    let failedItems: Int
    let totalSize: Int64
    let exportTime: TimeInterval
    let format: ExportFormat
    
    var successRate: Double {
        guard totalItems > 0 else { return 0 }
        return Double(exportedItems) / Double(totalItems)
    }
    
    var formattedTotalSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useBytes, .useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: totalSize)
    }
    
    var formattedExportTime: String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.minute, .second]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: exportTime) ?? "0s"
    }
}