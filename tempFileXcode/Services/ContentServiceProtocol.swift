import Foundation
import CoreData

// MARK: - Content Service Protocol
protocol ContentServiceProtocol: ObservableObject {
    
    // MARK: - Basic CRUD Operations
    func addContent(_ content: ContentData) async throws -> ContentItem
    func updateContent(_ item: ContentItem, with data: ContentData) async throws
    func deleteContent(_ item: ContentItem) async throws
    func getAllContent() async throws -> [ContentItem]
    func getContentByType(_ type: ContentType) async throws -> [ContentItem]
    
    // MARK: - File Management
    func saveContentFile(_ data: Data, for item: ContentItem) async throws -> String
    func loadContentFile(for item: ContentItem) async throws -> Data?
    func deleteContentFile(for item: ContentItem) async throws
    
    // MARK: - Content Type Detection
    func detectContentType(from data: Data) -> ContentType
    func detectContentType(from url: URL) -> ContentType
    func detectContentType(from string: String) -> ContentType
    
    // MARK: - Cleanup Operations
    func cleanupExpiredContent() async throws
    func cleanupOrphanedFiles() async throws
    func getStorageStatistics() async throws -> StorageStatistics
    
    // MARK: - Validation
    func validateContent(_ content: ContentData) throws
    func isContentValid(_ content: ContentData) -> Bool
}

// MARK: - Storage Statistics
struct StorageStatistics {
    let totalItems: Int
    let totalSize: Int64
    let textItems: Int
    let imageItems: Int
    let fileItems: Int
    let expiredItems: Int
    let orphanedFiles: Int
    
    var formattedTotalSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useBytes, .useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: totalSize)
    }
}