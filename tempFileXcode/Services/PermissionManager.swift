import Foundation
import AppKit

// MARK: - 权限管理器
class PermissionManager {
    static let shared = PermissionManager()
    
    // 存储安全作用域书签的字典
    private var securityScopedBookmarks: [String: Data] = [:]
    private let bookmarksKey = "SecurityScopedBookmarks"
    
    private init() {
        loadBookmarks()
    }
    
    // MARK: - 书签管理
    
    /// 加载保存的安全作用域书签
    private func loadBookmarks() {
        if let data = UserDefaults.standard.data(forKey: bookmarksKey),
           let bookmarks = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(data) as? [String: Data] {
            securityScopedBookmarks = bookmarks
            print("📚 加载了 \(bookmarks.count) 个安全作用域书签")
        }
    }
    
    /// 保存安全作用域书签
    private func saveBookmarks() {
        do {
            let data = try NSKeyedArchiver.archivedData(withRootObject: securityScopedBookmarks, requiringSecureCoding: false)
            UserDefaults.standard.set(data, forKey: bookmarksKey)
            print("💾 保存了 \(securityScopedBookmarks.count) 个安全作用域书签")
        } catch {
            print("❌ 保存书签失败: \(error)")
        }
    }
    
    /// 为URL创建安全作用域书签
    private func createBookmark(for url: URL) {
        // 确保在安全作用域访问状态下创建书签
        let needsSecurityScope = url.startAccessingSecurityScopedResource()
        defer {
            if needsSecurityScope {
                url.stopAccessingSecurityScopedResource()
            }
        }
        
        do {
            let bookmarkData = try url.bookmarkData(options: [.withSecurityScope], includingResourceValuesForKeys: nil, relativeTo: nil)
            securityScopedBookmarks[url.path] = bookmarkData
            saveBookmarks()
            print("📖 为文件创建书签: \(url.lastPathComponent)")
        } catch {
            print("❌ 创建书签失败: \(url.lastPathComponent) - \(error)")
            // 如果书签创建失败，尝试使用备用方案
            print("⚠️ 书签创建失败，使用原始URL: \(url.lastPathComponent)")
        }
    }
    
    /// 从书签恢复URL访问权限
    func restoreFromBookmark(for path: String) -> URL? {
        guard let bookmarkData = securityScopedBookmarks[path] else {
            return nil
        }
        
        do {
            var isStale = false
            let url = try URL(resolvingBookmarkData: bookmarkData, options: [.withSecurityScope], relativeTo: nil, bookmarkDataIsStale: &isStale)
            
            if isStale {
                print("⚠️ 书签已过期，需要重新创建: \(path)")
                securityScopedBookmarks.removeValue(forKey: path)
                saveBookmarks()
                return nil
            }
            
            print("📖 从书签恢复访问权限: \(url.lastPathComponent)")
            return url
        } catch {
            print("❌ 从书签恢复失败: \(path) - \(error)")
            securityScopedBookmarks.removeValue(forKey: path)
            saveBookmarks()
            return nil
        }
    }
    
    // MARK: - 权限检测
    
    /// 检查是否需要用户授权访问文件
    func checkIfNeedsUserAuthorization(for urls: [URL]) -> Bool {
        var needsAuthorization = false
        
        for url in urls {
            // 首先尝试从书签恢复权限
            if let bookmarkURL = restoreFromBookmark(for: url.path) {
                let needsSecurityScope = bookmarkURL.startAccessingSecurityScopedResource()
                print("🔐 从书签检查权限: \(url.lastPathComponent) -> \(needsSecurityScope)")
                
                if needsSecurityScope {
                    // 测试文件访问
                    do {
                        _ = try bookmarkURL.resourceValues(forKeys: [.fileSizeKey])
                        bookmarkURL.stopAccessingSecurityScopedResource()
                        print("✅ 书签权限有效: \(url.lastPathComponent)")
                        continue // 这个文件有权限
                    } catch {
                        bookmarkURL.stopAccessingSecurityScopedResource()
                        print("❌ 书签权限无效: \(url.lastPathComponent) - \(error)")
                    }
                }
            }
            
            // 尝试直接访问
            let needsSecurityScope = url.startAccessingSecurityScopedResource()
            print("🔐 直接检查权限: \(url.lastPathComponent) -> \(needsSecurityScope)")
            
            if needsSecurityScope {
                url.stopAccessingSecurityScopedResource()
                continue // 已经有权限
            }
            
            // 尝试实际的文件内容访问测试（与ContentData.fromFile保持一致）
            do {
                // 首先确保安全作用域访问
                let needsSecurityScope = url.startAccessingSecurityScopedResource()
                defer {
                    if needsSecurityScope {
                        url.stopAccessingSecurityScopedResource()
                    }
                }

                // 尝试读取少量数据来测试实际访问权限
                let testData = try Data(contentsOf: url, options: [.mappedIfSafe])
                print("✅ 直接访问成功: \(url.lastPathComponent) (大小: \(testData.count) bytes)")
                continue // 可以访问
            } catch {
                print("🔐 文件访问测试失败: \(url.lastPathComponent) - \(error)")
                needsAuthorization = true // 需要授权
            }
        }
        
        return needsAuthorization
    }
    
    /// 处理授权后的文件，创建书签
    func processAuthorizedFiles(_ urls: [URL]) -> [URL] {
        var processedURLs: [URL] = []
        
        for url in urls {
            // 验证文件确实可以访问
            let needsScope = url.startAccessingSecurityScopedResource()
            defer {
                if needsScope {
                    url.stopAccessingSecurityScopedResource()
                }
            }
            
            do {
                // 测试访问
                _ = try url.resourceValues(forKeys: [.fileSizeKey])
                
                // 为可访问的文件创建书签
                createBookmark(for: url)
                
                // 尝试从书签恢复（验证书签是否有效）
                if let bookmarkURL = restoreFromBookmark(for: url.path) {
                    processedURLs.append(bookmarkURL)
                    print("✅ 成功处理文件并创建书签: \(url.lastPathComponent)")
                } else {
                    // 如果书签无效，使用原始URL
                    processedURLs.append(url)
                    print("⚠️ 书签创建失败，使用原始URL: \(url.lastPathComponent)")
                }
            } catch {
                print("❌ 文件验证失败，跳过: \(url.lastPathComponent) - \(error)")
                // 跳过无法访问的文件
            }
        }
        
        return processedURLs
    }
    
    /// 检查系统权限设置
    func checkSystemPermissions() -> Bool {
        // 检查应用是否有完整磁盘访问权限
        let testPath = "/System/Library/CoreServices/Finder.app"
        let testURL = URL(fileURLWithPath: testPath)
        
        do {
            _ = try testURL.resourceValues(forKeys: [.fileSizeKey])
            return true // 有完整磁盘访问权限
        } catch {
            return false // 没有完整磁盘访问权限
        }
    }
    
    // MARK: - 权限引导
    
    /// 显示权限引导对话框 - 增强版本
    func showPermissionGuide(for urls: [URL], completion: @escaping ([URL]?) -> Void) {
        print("🔐 开始权限引导流程，文件数量: \(urls.count)")

        // 首先尝试智能权限检查
        let accessResult = performIntelligentAccessCheck(for: urls)

        if !accessResult.needsAuthorization.isEmpty {
            print("🔐 需要授权的文件: \(accessResult.needsAuthorization.count) 个")
            showSmartAuthorizationGuide(for: accessResult.needsAuthorization) { authorizedURLs in
                if let authorizedURLs = authorizedURLs {
                    // 合并已有权限的文件和新授权的文件
                    let allAuthorizedURLs = accessResult.alreadyAccessible + authorizedURLs
                    completion(allAuthorizedURLs)
                } else if !accessResult.alreadyAccessible.isEmpty {
                    // 如果用户取消授权但有部分文件已可访问，询问是否继续
                    self.showPartialAuthorizationDialog(
                        accessible: accessResult.alreadyAccessible,
                        total: urls,
                        completion: completion
                    )
                } else {
                    completion(nil)
                }
            }
        } else {
            print("🎉 所有文件都已可访问")
            completion(accessResult.alreadyAccessible)
        }
    }

    /// 智能权限检查结果
    private struct AccessCheckResult {
        let alreadyAccessible: [URL]
        let needsAuthorization: [URL]
    }

    /// 执行智能权限检查
    private func performIntelligentAccessCheck(for urls: [URL]) -> AccessCheckResult {
        var alreadyAccessible: [URL] = []
        var needsAuthorization: [URL] = []

        for url in urls {
            print("🔍 检查文件权限: \(url.lastPathComponent)")

            // 策略1: 检查是否有现有书签
            if let bookmarkURL = restoreFromBookmark(for: url.path) {
                let needsScope = bookmarkURL.startAccessingSecurityScopedResource()
                defer {
                    if needsScope {
                        bookmarkURL.stopAccessingSecurityScopedResource()
                    }
                }

                do {
                    _ = try bookmarkURL.resourceValues(forKeys: [.fileSizeKey])
                    alreadyAccessible.append(bookmarkURL)
                    print("✅ 书签权限有效: \(url.lastPathComponent)")
                    continue
                } catch {
                    print("❌ 书签权限失效: \(url.lastPathComponent)")
                }
            }

            // 策略2: 尝试直接访问
            let needsScope = url.startAccessingSecurityScopedResource()
            defer {
                if needsScope {
                    url.stopAccessingSecurityScopedResource()
                }
            }

            do {
                // 首先确保安全作用域访问
                let needsSecurityScope = url.startAccessingSecurityScopedResource()
                defer {
                    if needsSecurityScope {
                        url.stopAccessingSecurityScopedResource()
                    }
                }

                // 尝试读取少量数据来测试实际访问权限
                let testData = try Data(contentsOf: url, options: [.mappedIfSafe])
                alreadyAccessible.append(url)
                print("✅ 直接访问成功: \(url.lastPathComponent) (大小: \(testData.count) bytes)")
            } catch {
                print("❌ 需要授权: \(url.lastPathComponent) - \(error)")
                needsAuthorization.append(url)
            }
        }

        return AccessCheckResult(
            alreadyAccessible: alreadyAccessible,
            needsAuthorization: needsAuthorization
        )
    }
    
    /// 直接对指定文件进行权限授权（主要用于拖拽场景）
    func requestDirectAuthorization(for urls: [URL], completion: @escaping ([URL]?) -> Void) {
        print("🔐 请求直接授权 \(urls.count) 个文件")
        
        // 首先检查系统级权限
        let systemManager = SystemPermissionManager.shared
        let permissionStatus = systemManager.getPermissionStatus()
        
        if permissionStatus.hasFullAccess {
            print("✅ 已有完整磁盘访问权限，直接处理文件")
            completion(urls)
            return
        }
        
        // 检查是否有信任的文件夹
        var accessibleURLs: [URL] = []
        var needsAuthorizationURLs: [URL] = []
        
        for url in urls {
            print("🔍 检查拖拽文件权限: \(url.lastPathComponent)")
            
            // 检查文件夹是否已被信任
            let folderPath = url.deletingLastPathComponent().path
            if systemManager.isFolderTrusted(folderPath) {
                // 尝试从持久书签恢复
                if let bookmarkURL = systemManager.restoreFromPersistentBookmark(for: folderPath) {
                    let needsScope = bookmarkURL.startAccessingSecurityScopedResource()
                    defer {
                        if needsScope {
                            bookmarkURL.stopAccessingSecurityScopedResource()
                        }
                    }
                    
                    do {
                        _ = try Data(contentsOf: url, options: [.mappedIfSafe])
                        accessibleURLs.append(url)
                        print("✅ 通过信任文件夹访问: \(url.lastPathComponent)")
                        continue
                    } catch {
                        print("❌ 信任文件夹访问失败: \(url.lastPathComponent)")
                    }
                }
            }
            
            // 尝试访问拖拽的文件（它们应该有临时权限）
            let needsSecurityScope = url.startAccessingSecurityScopedResource()
            defer {
                if needsSecurityScope {
                    url.stopAccessingSecurityScopedResource()
                }
            }
            
            do {
                // 测试文件访问
                _ = try Data(contentsOf: url, options: [.mappedIfSafe])
                accessibleURLs.append(url)
                print("✅ 拖拽文件可访问: \(url.lastPathComponent)")
                
                // 立即为可访问的文件创建书签
                createBookmark(for: url)
            } catch {
                print("❌ 拖拽文件无法访问: \(url.lastPathComponent) - \(error)")
                needsAuthorizationURLs.append(url)
            }
        }
        
        if needsAuthorizationURLs.isEmpty {
            print("✅ 所有拖拽文件都可以访问")
            completion(accessibleURLs)
        } else {
            print("⚠️ 有 \(needsAuthorizationURLs.count) 个文件需要额外授权")
            
            // 如果是第一次遇到权限问题，显示系统权限引导
            if accessibleURLs.isEmpty && permissionStatus.trustedFolders.isEmpty {
                systemManager.showSystemPermissionGuide { success in
                    if success {
                        // 用户设置了系统权限，重新尝试
                        self.requestDirectAuthorization(for: urls, completion: completion)
                    } else {
                        // 用户选择了其他方式，继续显示文件授权对话框
                        self.showSmartAuthorizationGuide(for: needsAuthorizationURLs) { authorizedURLs in
                            if let authorizedURLs = authorizedURLs {
                                completion(accessibleURLs + authorizedURLs)
                            } else {
                                completion(accessibleURLs.isEmpty ? nil : accessibleURLs)
                            }
                        }
                    }
                }
            } else {
                // 对于无法访问的文件，显示授权对话框
                showSmartAuthorizationGuide(for: needsAuthorizationURLs) { authorizedURLs in
                    if let authorizedURLs = authorizedURLs {
                        completion(accessibleURLs + authorizedURLs)
                    } else {
                        // 即使部分授权失败，也返回可访问的文件
                        completion(accessibleURLs.isEmpty ? nil : accessibleURLs)
                    }
                }
            }
        }
    }

    /// 显示部分授权对话框
    private func showPartialAuthorizationDialog(
        accessible: [URL],
        total: [URL],
        completion: @escaping ([URL]?) -> Void
    ) {
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "部分文件可以访问"
            alert.informativeText = """
            在 \(total.count) 个文件中，有 \(accessible.count) 个文件已可以访问。

            可访问的文件：
            \(accessible.map { "• \($0.lastPathComponent)" }.joined(separator: "\n"))

            您可以选择：
            • 继续处理可访问的文件
            • 取消本次操作
            """
            alert.alertStyle = .informational

            alert.addButton(withTitle: "继续处理")
            alert.addButton(withTitle: "取消")

            let response = alert.runModal()
            if response == .alertFirstButtonReturn {
                completion(accessible)
            } else {
                completion(nil)
            }
        }
    }
    
    /// 检查特定文件的访问权限
    private func checkSpecificFileAccess(for urls: [URL]) -> [URL] {
        var accessibleURLs: [URL] = []
        
        for url in urls {
            // 首先尝试从书签恢复
            if let bookmarkURL = restoreFromBookmark(for: url.path) {
                let needsScope = bookmarkURL.startAccessingSecurityScopedResource()
                defer {
                    if needsScope {
                        bookmarkURL.stopAccessingSecurityScopedResource()
                    }
                }
                
                do {
                    _ = try bookmarkURL.resourceValues(forKeys: [.fileSizeKey])
                    accessibleURLs.append(bookmarkURL)
                    print("✅ 通过书签访问成功: \(url.lastPathComponent)")
                    continue
                } catch {
                    print("❌ 书签访问失败: \(url.lastPathComponent) - \(error)")
                }
            }
            
            // 尝试直接访问
            let needsScope = url.startAccessingSecurityScopedResource()
            defer {
                if needsScope {
                    url.stopAccessingSecurityScopedResource()
                }
            }
            
            do {
                // 首先确保安全作用域访问
                let needsSecurityScope = url.startAccessingSecurityScopedResource()
                defer {
                    if needsSecurityScope {
                        url.stopAccessingSecurityScopedResource()
                    }
                }

                // 尝试读取少量数据来测试实际访问权限
                let testData = try Data(contentsOf: url, options: [.mappedIfSafe])
                accessibleURLs.append(url)
                print("✅ 直接访问成功: \(url.lastPathComponent) (大小: \(testData.count) bytes)")
            } catch {
                print("❌ 直接访问失败: \(url.lastPathComponent) - \(error)")
            }
        }
        
        return accessibleURLs
    }
    
    /// 显示系统权限设置引导
    private func showSystemPermissionGuide(for urls: [URL] = [], completion: @escaping ([URL]?) -> Void) {
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "需要系统权限设置"
            alert.informativeText = """
            为了更好地支持文件拖拽功能，建议您在系统设置中授予应用权限：
            
            📱 macOS Ventura 及更新版本：
            1. 打开"系统设置" > "隐私与安全性"
            2. 选择"完整磁盘访问权限"或"文件和文件夹"
            3. 添加此应用并启用权限
            
            🖥️ macOS Monterey 及更早版本：
            1. 打开"系统偏好设置" > "安全性与隐私"
            2. 选择"隐私"标签页
            3. 选择"完整磁盘访问权限"
            4. 点击锁图标解锁，添加此应用
            
            或者，您可以继续使用手动文件选择方式。
            """
            alert.alertStyle = .informational
            alert.addButton(withTitle: "打开系统设置")
            alert.addButton(withTitle: "手动选择文件")
            alert.addButton(withTitle: "取消")
            
            let response = alert.runModal()
            
            switch response {
            case .alertFirstButtonReturn:
                // 打开系统设置
                self.openSystemPreferences()
                self.showPostSettingsGuide()
                completion(nil)
            case .alertSecondButtonReturn:
                // 继续手动选择
                self.showFileSelectionDialog(completion: completion)
            default:
                // 取消
                completion(nil)
            }
        }
    }
    
    /// 显示文件选择引导
    private func showFileSelectionGuide(for urls: [URL], completion: @escaping ([URL]?) -> Void) {
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "需要文件访问权限"
            alert.informativeText = """
            由于macOS安全限制，需要您手动选择文件以授权访问。
            
            请在接下来的对话框中选择您刚才拖拽的文件。
            
            💡 提示：您可以同时选择多个文件。
            """
            alert.alertStyle = .informational
            alert.addButton(withTitle: "继续选择文件")
            alert.addButton(withTitle: "取消")
            
            let response = alert.runModal()
            
            if response == .alertFirstButtonReturn {
                self.showFileSelectionDialog(defaultDirectory: urls.first?.deletingLastPathComponent(), completion: completion)
            } else {
                completion(nil)
            }
        }
    }
    
    /// 显示文件选择对话框
    private func showFileSelectionDialog(defaultDirectory: URL? = nil, completion: @escaping ([URL]?) -> Void) {
        DispatchQueue.main.async {
            let openPanel = NSOpenPanel()
            openPanel.title = "选择文件以授权访问"
            openPanel.message = "请选择您刚才拖拽的文件以授权应用访问："
            openPanel.prompt = "授权访问"
            openPanel.allowsMultipleSelection = true
            openPanel.canChooseDirectories = false
            openPanel.canChooseFiles = true
            
            // 设置默认目录
            if let directory = defaultDirectory {
                openPanel.directoryURL = directory
            }
            
            openPanel.begin { response in
                if response == .OK {
                    print("✅ 用户授权了 \(openPanel.urls.count) 个文件")
                    // 处理授权的文件并创建书签
                    let processedURLs = self.processAuthorizedFiles(openPanel.urls)
                    completion(processedURLs)
                } else {
                    print("❌ 用户取消了文件选择")
                    completion(nil)
                }
            }
        }
    }
    
    // MARK: - 系统设置操作
    
    /// 打开系统设置
    private func openSystemPreferences() {
        // 检测macOS版本并打开相应的设置
        if #available(macOS 13.0, *) {
            // macOS Ventura 及更新版本 - 系统设置
            if let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy") {
                NSWorkspace.shared.open(url)
            }
        } else {
            // macOS Monterey 及更早版本 - 系统偏好设置
            if let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles") {
                NSWorkspace.shared.open(url)
            }
        }
        
        // 备用方案：直接打开设置应用
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if #available(macOS 13.0, *) {
                NSWorkspace.shared.open(URL(fileURLWithPath: "/System/Applications/System Settings.app"))
            } else {
                NSWorkspace.shared.open(URL(fileURLWithPath: "/System/Applications/System Preferences.app"))
            }
        }
    }
    
    /// 显示设置后的指导
    private func showPostSettingsGuide() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let alert = NSAlert()
            alert.messageText = "权限设置完成后"
            alert.informativeText = """
            在系统设置中授权完成后：
            
            1. 重新启动此应用
            2. 再次尝试拖拽文件
            
            如果仍有问题，请使用手动文件选择功能。
            
            📝 注意：某些权限更改可能需要重启应用才能生效。
            """
            alert.alertStyle = .informational
            alert.addButton(withTitle: "知道了")
            alert.runModal()
        }
    }
    
    /// 智能授权引导 - 专为拖拽场景设计（增强版本）
    private func showSmartAuthorizationGuide(for urls: [URL], completion: @escaping ([URL]?) -> Void) {
        DispatchQueue.main.async {
            let fileNames = urls.map { $0.lastPathComponent }.joined(separator: "\n• ")
            let folderPaths = Set(urls.map { $0.deletingLastPathComponent().path })
            let folderList = folderPaths.map { "📁 \($0)" }.joined(separator: "\n")

            let alert = NSAlert()
            alert.messageText = "🔐 需要文件访问权限"
            alert.informativeText = """
            应用需要访问以下 \(urls.count) 个拖拽的文件：

            • \(fileNames)

            文件位置：
            \(folderList)

            💡 为什么需要授权？
            • 这是macOS沙盒安全机制的要求
            • 保护您的文件不被恶意应用访问

            🚀 推荐操作：
            • 点击"立即授权"，在文件选择对话框中选择这些文件
            • 授权后会自动记住，下次拖拽同一位置的文件无需重新授权

            ⚙️ 或者您可以在系统设置中一次性授予完整文件夹访问权限
            """
            alert.alertStyle = .informational
            alert.addButton(withTitle: "立即授权")
            alert.addButton(withTitle: "系统设置")
            alert.addButton(withTitle: "取消")

            let response = alert.runModal()

            switch response {
            case .alertFirstButtonReturn:
                // 直接授权这些文件
                self.requestSpecificFileAccess(for: urls, completion: completion)
            case .alertSecondButtonReturn:
                // 打开系统设置
                self.openSystemPreferences()
                self.showPostSettingsGuide()
                completion(nil)
            default:
                // 取消
                completion(nil)
            }
        }
    }
    
    /// 请求特定文件的访问权限 - 增强版本
    private func requestSpecificFileAccess(for urls: [URL], completion: @escaping ([URL]?) -> Void) {
        print("🔐 请求特定文件访问权限，文件数量: \(urls.count)")

        // 创建一个智能的NSOpenPanel来获取这些文件的访问权限
        DispatchQueue.main.async {
            let openPanel = NSOpenPanel()
            openPanel.title = "🔐 文件访问授权"

            let fileNames = urls.map { $0.lastPathComponent }.joined(separator: "\n• ")
            openPanel.message = """
            请选择您刚才拖拽的文件以授权访问：

            • \(fileNames)

            💡 提示：您可以一次性选择多个文件，或选择整个文件夹来授权所有文件。
            """
            openPanel.prompt = "授权访问"
            openPanel.allowsMultipleSelection = true
            openPanel.canChooseDirectories = true  // 允许选择文件夹
            openPanel.canChooseFiles = true

            // 智能设置默认目录
            if let firstURL = urls.first {
                let parentDirectory = firstURL.deletingLastPathComponent()
                openPanel.directoryURL = parentDirectory
                print("🔍 设置默认目录: \(parentDirectory.path)")
            }

            // 设置文件类型过滤器（如果所有文件都是同一类型）
            let fileExtensions = Set(urls.compactMap { $0.pathExtension.isEmpty ? nil : $0.pathExtension })
            if fileExtensions.count == 1, let ext = fileExtensions.first {
                openPanel.allowedFileTypes = [ext]
                print("🔍 设置文件类型过滤器: \(ext)")
            } else {
                openPanel.allowedContentTypes = []
            }

            openPanel.begin { response in
                if response == .OK {
                    print("✅ 用户授权了 \(openPanel.urls.count) 个文件/文件夹")

                    // 处理授权的文件并创建书签
                    let processedURLs = self.processAuthorizedFiles(openPanel.urls)

                    // 智能检查授权结果
                    self.validateAuthorizationResult(
                        requested: urls,
                        authorized: processedURLs,
                        completion: completion
                    )
                } else {
                    print("❌ 用户取消了文件授权")
                    completion(nil)
                }
            }
        }
    }

    /// 验证授权结果并处理部分授权情况
    private func validateAuthorizationResult(
        requested: [URL],
        authorized: [URL],
        completion: @escaping ([URL]?) -> Void
    ) {
        let authorizedPaths = Set(authorized.map { $0.path })
        let requestedPaths = Set(requested.map { $0.path })

        // 检查是否有直接匹配的文件
        let directMatches = requested.filter { authorizedPaths.contains($0.path) }

        // 检查是否有通过父目录授权的文件
        var indirectMatches: [URL] = []
        for requestedURL in requested {
            if !authorizedPaths.contains(requestedURL.path) {
                // 检查是否有父目录被授权
                for authorizedURL in authorized {
                    if requestedURL.path.hasPrefix(authorizedURL.path + "/") {
                        indirectMatches.append(requestedURL)
                        break
                    }
                }
            }
        }

        let allMatches = directMatches + indirectMatches
        let missingFiles = requestedPaths.subtracting(Set(allMatches.map { $0.path }))

        print("🔍 授权结果分析:")
        print("  - 直接匹配: \(directMatches.count)")
        print("  - 间接匹配: \(indirectMatches.count)")
        print("  - 缺失文件: \(missingFiles.count)")

        if missingFiles.isEmpty {
            print("🎉 所有文件都已获得授权")
            completion(allMatches.isEmpty ? authorized : allMatches)
        } else {
            print("⚠️ 部分文件未被授权: \(missingFiles)")
            self.showPartialAuthorizationWarning(
                missing: missingFiles,
                authorized: allMatches.isEmpty ? authorized : allMatches,
                completion: completion
            )
        }
    }
    
    /// 显示部分授权警告
    private func showPartialAuthorizationWarning(missing: Set<String>, authorized: [URL], completion: @escaping ([URL]?) -> Void) {
        DispatchQueue.main.async {
            let missingNames = missing.map { URL(fileURLWithPath: $0).lastPathComponent }.joined(separator: ", ")
            let alert = NSAlert()
            alert.messageText = "部分文件未授权"
            alert.informativeText = """
            以下文件未被授权访问：
            \(missingNames)
            
            您可以：
            • 继续处理已授权的文件
            • 重新尝试授权所有文件
            • 取消操作
            """
            alert.alertStyle = .warning
            alert.addButton(withTitle: "继续处理已授权文件")
            alert.addButton(withTitle: "重新授权")
            alert.addButton(withTitle: "取消")
            
            let response = alert.runModal()
            
            switch response {
            case .alertFirstButtonReturn:
                completion(authorized)
            case .alertSecondButtonReturn:
                // 重新尝试授权
                let allURLs = authorized + missing.map { URL(fileURLWithPath: $0) }
                self.requestSpecificFileAccess(for: allURLs, completion: completion)
            default:
                completion(nil)
            }
        }
    }
    
    // MARK: - 权限状态检查
    
    /// 获取权限状态描述
    func getPermissionStatusDescription() -> String {
        let hasSystemPermissions = checkSystemPermissions()
        
        if hasSystemPermissions {
            return "✅ 应用已获得系统文件访问权限"
        } else {
            return "⚠️ 应用需要系统文件访问权限才能更好地支持拖拽功能"
        }
    }
}