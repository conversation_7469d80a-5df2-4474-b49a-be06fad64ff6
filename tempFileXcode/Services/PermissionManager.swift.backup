import Foundation
import AppKit

// MARK: - 权限管理器
class PermissionManager {
    static let shared = PermissionManager()
    
    // 存储安全作用域书签的字典
    private var securityScopedBookmarks: [String: Data] = [:]
    private let bookmarksKey = "SecurityScopedBookmarks"
    
    private init() {
        loadBookmarks()
    }
    
    // MARK: - 书签管理
    
    /// 加载保存的安全作用域书签
    private func loadBookmarks() {
        if let data = UserDefaults.standard.data(forKey: bookmarksKey),
           let bookmarks = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(data) as? [String: Data] {
            securityScopedBookmarks = bookmarks
            print("📚 加载了 \(bookmarks.count) 个安全作用域书签")
        }
    }
    
    /// 保存安全作用域书签
    private func saveBookmarks() {
        do {
            let data = try NSKeyedArchiver.archivedData(withRootObject: securityScopedBookmarks, requiringSecureCoding: false)
            UserDefaults.standard.set(data, forKey: bookmarksKey)
            print("💾 保存了 \(securityScopedBookmarks.count) 个安全作用域书签")
        } catch {
            print("❌ 保存书签失败: \(error)")
        }
    }
    
    /// 为URL创建安全作用域书签
    private func createBookmark(for url: URL) {
        do {
            let bookmarkData = try url.bookmarkData(options: [.withSecurityScope], includingResourceValuesForKeys: nil, relativeTo: nil)
            securityScopedBookmarks[url.path] = bookmarkData
            saveBookmarks()
            print("📖 为文件创建书签: \(url.lastPathComponent)")
        } catch {
            print("❌ 创建书签失败: \(url.lastPathComponent) - \(error)")
        }
    }
    
    /// 从书签恢复URL访问权限
    func restoreFromBookmark(for path: String) -> URL? {
        guard let bookmarkData = securityScopedBookmarks[path] else {
            return nil
        }
        
        do {
            var isStale = false
            let url = try URL(resolvingBookmarkData: bookmarkData, options: [.withSecurityScope], relativeTo: nil, bookmarkDataIsStale: &isStale)
            
            if isStale {
                print("⚠️ 书签已过期，需要重新创建: \(path)")
                securityScopedBookmarks.removeValue(forKey: path)
                saveBookmarks()
                return nil
            }
            
            print("📖 从书签恢复访问权限: \(url.lastPathComponent)")
            return url
        } catch {
            print("❌ 从书签恢复失败: \(path) - \(error)")
            securityScopedBookmarks.removeValue(forKey: path)
            saveBookmarks()
            return nil
        }
    }
    
    // MARK: - 权限检测
    
    /// 检查是否需要用户授权访问文件
    func checkIfNeedsUserAuthorization(for urls: [URL]) -> Bool {
        var needsAuthorization = false
        
        for url in urls {
            // 首先尝试从书签恢复权限
            if let bookmarkURL = restoreFromBookmark(for: url.path) {
                let needsSecurityScope = bookmarkURL.startAccessingSecurityScopedResource()
                print("🔐 从书签检查权限: \(url.lastPathComponent) -> \(needsSecurityScope)")
                
                if needsSecurityScope {
                    // 测试文件访问
                    do {
                        _ = try bookmarkURL.resourceValues(forKeys: [.fileSizeKey])
                        bookmarkURL.stopAccessingSecurityScopedResource()
                        print("✅ 书签权限有效: \(url.lastPathComponent)")
                        continue // 这个文件有权限
                    } catch {
                        bookmarkURL.stopAccessingSecurityScopedResource()
                        print("❌ 书签权限无效: \(url.lastPathComponent) - \(error)")
                    }
                }
            }
            
            // 尝试直接访问
            let needsSecurityScope = url.startAccessingSecurityScopedResource()
            print("🔐 直接检查权限: \(url.lastPathComponent) -> \(needsSecurityScope)")
            
            if needsSecurityScope {
                url.stopAccessingSecurityScopedResource()
                continue // 已经有权限
            }
            
            // 尝试简单的文件访问测试
            do {
                _ = try url.resourceValues(forKeys: [.fileSizeKey])
                print("✅ 直接访问成功: \(url.lastPathComponent)")
                continue // 可以访问
            } catch {
                print("🔐 文件访问测试失败: \(url.lastPathComponent) - \(error)")
                needsAuthorization = true // 需要授权
            }
        }
        
        return needsAuthorization
    }
    
    /// 处理授权后的文件，创建书签
    func processAuthorizedFiles(_ urls: [URL]) -> [URL] {
        print("🔐 processAuthorizedFiles 开始处理 \(urls.count) 个文件")
        var processedURLs: [URL] = []
        
        for url in urls {
            print("🔍 验证文件访问: \(url.lastPathComponent)")
            
            // 验证文件确实可以访问
            let needsScope = url.startAccessingSecurityScopedResource()
            defer {
                if needsScope {
                    url.stopAccessingSecurityScopedResource()
                }
            }
            
            do {
                // 测试访问 - 使用更全面的验证
                let resourceValues = try url.resourceValues(forKeys: [.fileSizeKey, .isReadableKey])
                let fileSize = resourceValues.fileSize ?? 0
                let isReadable = resourceValues.isReadable ?? false
                
                print("🔍 文件验证结果: \(url.lastPathComponent) - 大小: \(fileSize), 可读: \(isReadable)")
                
                guard isReadable else {
                    print("❌ 文件不可读，跳过: \(url.lastPathComponent)")
                    continue
                }
                
                // 为可访问的文件创建书签
                createBookmark(for: url)
                
                // 尝试从书签恢复（验证书签是否有效）
                if let bookmarkURL = restoreFromBookmark(for: url.path) {
                    // 再次验证书签URL
                    let bookmarkAccess = bookmarkURL.startAccessingSecurityScopedResource()
                    defer {
                        if bookmarkAccess {
                            bookmarkURL.stopAccessingSecurityScopedResource()
                        }
                    }
                    
                    do {
                        _ = try bookmarkURL.resourceValues(forKeys: [.fileSizeKey])
                        processedURLs.append(bookmarkURL)
                        print("✅ 成功处理文件并创建有效书签: \(url.lastPathComponent)")
                    } catch {
                        print("❌ 书签URL验证失败，使用原始URL: \(url.lastPathComponent) - \(error)")
                        processedURLs.append(url)
                    }
                } else {
                    // 如果书签无效，使用原始URL
                    processedURLs.append(url)
                    print("⚠️ 书签创建失败，使用原始URL: \(url.lastPathComponent)")
                }
            } catch {
                print("❌ 文件验证失败，跳过: \(url.lastPathComponent) - \(error)")
                // 跳过无法访问的文件，不添加到结果中
            }
        }
        
        print("🔐 processAuthorizedFiles 完成，返回 \(processedURLs.count) 个有效文件")
        return processedURLs
    }
    
    /// 检查系统权限设置
    func checkSystemPermissions() -> Bool {
        // 检查应用是否有完整磁盘访问权限
        let testPath = "/System/Library/CoreServices/Finder.app"
        let testURL = URL(fileURLWithPath: testPath)
        
        do {
            _ = try testURL.resourceValues(forKeys: [.fileSizeKey])
            return true // 有完整磁盘访问权限
        } catch {
            return false // 没有完整磁盘访问权限
        }
    }
    
    // MARK: - 权限引导
    
    /// 显示权限引导对话框
    func showPermissionGuide(for urls: [URL], completion: @escaping ([URL]?) -> Void) {
        let hasSystemPermissions = checkSystemPermissions()
        
        if !hasSystemPermissions {
            showSystemPermissionGuide(for: urls, completion: completion)
        } else {
            // 如果已经有系统权限，直接尝试创建书签
            let processedURLs = processAuthorizedFiles(urls)
            completion(processedURLs)
        }
    }
    
    /// 直接对指定文件进行权限授权（主要用于拖拽场景）
    func requestDirectAuthorization(for urls: [URL], completion: @escaping ([URL]?) -> Void) {
        print("🔐 请求直接授权 \(urls.count) 个文件")
        
        // 首先检查这些特定文件是否可以访问
        let accessibleURLs = checkSpecificFileAccess(for: urls)
        
        if accessibleURLs.count == urls.count {
            print("✅ 所有文件都可以直接访问，创建书签")
            let processedURLs = processAuthorizedFiles(accessibleURLs)
            completion(processedURLs)
            return
        }
        
        // 如果有文件无法访问，显示智能授权对话框
        print("⚠️ 有 \(urls.count - accessibleURLs.count) 个文件需要授权")
        showSmartAuthorizationGuide(for: urls, completion: completion)
    }
    
    /// 检查特定文件的访问权限
    private func checkSpecificFileAccess(for urls: [URL]) -> [URL] {
        var accessibleURLs: [URL] = []
        
        for url in urls {
            // 首先尝试从书签恢复
            if let bookmarkURL = restoreFromBookmark(for: url.path) {
                let needsScope = bookmarkURL.startAccessingSecurityScopedResource()
                defer {
                    if needsScope {
                        bookmarkURL.stopAccessingSecurityScopedResource()
                    }
                }
                
                do {
                    _ = try bookmarkURL.resourceValues(forKeys: [.fileSizeKey])
                    accessibleURLs.append(bookmarkURL)
                    print("✅ 通过书签访问成功: \(url.lastPathComponent)")
                    continue
                } catch {
                    print("❌ 书签访问失败: \(url.lastPathComponent) - \(error)")
                }
            }
            
            // 尝试直接访问
            let needsScope = url.startAccessingSecurityScopedResource()
            defer {
                if needsScope {
                    url.stopAccessingSecurityScopedResource()
                }
            }
            
            do {
                _ = try url.resourceValues(forKeys: [.fileSizeKey])
                accessibleURLs.append(url)
                print("✅ 直接访问成功: \(url.lastPathComponent)")
            } catch {
                print("❌ 直接访问失败: \(url.lastPathComponent) - \(error)")
            }
        }
        
        return accessibleURLs
    }
    
    /// 显示系统权限设置引导
    private func showSystemPermissionGuide(for urls: [URL] = [], completion: @escaping ([URL]?) -> Void) {
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "需要系统权限设置"
            alert.informativeText = """
            为了更好地支持文件拖拽功能，建议您在系统设置中授予应用权限：
            
            📱 macOS Ventura 及更新版本：
            1. 打开"系统设置" > "隐私与安全性"
            2. 选择"完整磁盘访问权限"或"文件和文件夹"
            3. 添加此应用并启用权限
            
            🖥️ macOS Monterey 及更早版本：
            1. 打开"系统偏好设置" > "安全性与隐私"
            2. 选择"隐私"标签页
            3. 选择"完整磁盘访问权限"
            4. 点击锁图标解锁，添加此应用
            
            或者，您可以继续使用手动文件选择方式。
            """
            alert.alertStyle = .informational
            alert.addButton(withTitle: "打开系统设置")
            alert.addButton(withTitle: "手动选择文件")
            alert.addButton(withTitle: "取消")
            
            let response = alert.runModal()
            
            switch response {
            case .alertFirstButtonReturn:
                // 打开系统设置
                self.openSystemPreferences()
                self.showPostSettingsGuide()
                completion(nil)
            case .alertSecondButtonReturn:
                // 继续手动选择
                self.showFileSelectionDialog(completion: completion)
            default:
                // 取消
                completion(nil)
            }
        }
    }
    
    /// 显示文件选择引导
    private func showFileSelectionGuide(for urls: [URL], completion: @escaping ([URL]?) -> Void) {
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "需要文件访问权限"
            alert.informativeText = """
            由于macOS安全限制，需要您手动选择文件以授权访问。
            
            请在接下来的对话框中选择您刚才拖拽的文件。
            
            💡 提示：您可以同时选择多个文件。
            """
            alert.alertStyle = .informational
            alert.addButton(withTitle: "继续选择文件")
            alert.addButton(withTitle: "取消")
            
            let response = alert.runModal()
            
            if response == .alertFirstButtonReturn {
                self.showFileSelectionDialog(defaultDirectory: urls.first?.deletingLastPathComponent(), completion: completion)
            } else {
                completion(nil)
            }
        }
    }
    
    /// 显示文件选择对话框
    private func showFileSelectionDialog(defaultDirectory: URL? = nil, completion: @escaping ([URL]?) -> Void) {
        DispatchQueue.main.async {
            let openPanel = NSOpenPanel()
            openPanel.title = "选择文件以授权访问"
            openPanel.message = "请选择您刚才拖拽的文件以授权应用访问："
            openPanel.prompt = "授权访问"
            openPanel.allowsMultipleSelection = true
            openPanel.canChooseDirectories = false
            openPanel.canChooseFiles = true
            
            // 设置默认目录
            if let directory = defaultDirectory {
                openPanel.directoryURL = directory
            }
            
            openPanel.begin { response in
                if response == .OK {
                    print("✅ 用户授权了 \(openPanel.urls.count) 个文件")
                    // 处理授权的文件并创建书签
                    let processedURLs = self.processAuthorizedFiles(openPanel.urls)
                    completion(processedURLs)
                } else {
                    print("❌ 用户取消了文件选择")
                    completion(nil)
                }
            }
        }
    }
    
    // MARK: - 系统设置操作
    
    /// 打开系统设置
    private func openSystemPreferences() {
        // 检测macOS版本并打开相应的设置
        if #available(macOS 13.0, *) {
            // macOS Ventura 及更新版本 - 系统设置
            if let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy") {
                NSWorkspace.shared.open(url)
            }
        } else {
            // macOS Monterey 及更早版本 - 系统偏好设置
            if let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles") {
                NSWorkspace.shared.open(url)
            }
        }
        
        // 备用方案：直接打开设置应用
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if #available(macOS 13.0, *) {
                NSWorkspace.shared.open(URL(fileURLWithPath: "/System/Applications/System Settings.app"))
            } else {
                NSWorkspace.shared.open(URL(fileURLWithPath: "/System/Applications/System Preferences.app"))
            }
        }
    }
    
    /// 显示设置后的指导
    private func showPostSettingsGuide() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let alert = NSAlert()
            alert.messageText = "权限设置完成后"
            alert.informativeText = """
            在系统设置中授权完成后：
            
            1. 重新启动此应用
            2. 再次尝试拖拽文件
            
            如果仍有问题，请使用手动文件选择功能。
            
            📝 注意：某些权限更改可能需要重启应用才能生效。
            """
            alert.alertStyle = .informational
            alert.addButton(withTitle: "知道了")
            alert.runModal()
        }
    }
    
    /// 智能授权引导 - 专为拖拽场景设计
    private func showSmartAuthorizationGuide(for urls: [URL], completion: @escaping ([URL]?) -> Void) {
        DispatchQueue.main.async {
            let fileNames = urls.map { $0.lastPathComponent }.joined(separator: ", ")
            let alert = NSAlert()
            alert.messageText = "文件访问权限"
            alert.informativeText = """
            需要访问以下拖拽的文件：
            \(fileNames)
            
            macOS 安全机制需要您授权访问这些文件。
            
            您可以选择：
            • 一次性授权这些文件（推荐）
            • 在系统设置中授予完整权限
            """
            alert.alertStyle = .informational
            alert.addButton(withTitle: "授权这些文件")
            alert.addButton(withTitle: "打开系统设置")
            alert.addButton(withTitle: "取消")
            
            let response = alert.runModal()
            
            switch response {
            case .alertFirstButtonReturn:
                // 直接授权这些文件
                self.requestSpecificFileAccess(for: urls, completion: completion)
            case .alertSecondButtonReturn:
                // 打开系统设置
                self.openSystemPreferences()
                self.showPostSettingsGuide()
                completion(nil)
            default:
                // 取消
                completion(nil)
            }
        }
    }
    
    /// 请求特定文件的访问权限
    private func requestSpecificFileAccess(for urls: [URL], completion: @escaping ([URL]?) -> Void) {
        print("🔐 请求特定文件访问权限，文件数量: \(urls.count)")
        
        // 创建一个临时的NSOpenPanel来获取这些文件的访问权限
        DispatchQueue.main.async {
            let openPanel = NSOpenPanel()
            openPanel.title = "确认文件访问"
            openPanel.message = "请在对话框中选择您刚才拖拽的文件以授权访问："
            openPanel.prompt = "授权访问"
            openPanel.allowsMultipleSelection = true
            openPanel.canChooseDirectories = false
            openPanel.canChooseFiles = true
            
            // 设置默认目录为第一个文件的目录
            if let firstURL = urls.first {
                openPanel.directoryURL = firstURL.deletingLastPathComponent()
            }
            
            // 尝试预选择文件（如果可能）
            openPanel.allowedContentTypes = []
            
            openPanel.begin { response in
                if response == .OK {
                    print("✅ 用户授权了 \(openPanel.urls.count) 个文件")
                    
                    // 处理授权的文件并创建书签
                    let processedURLs = self.processAuthorizedFiles(openPanel.urls)
                    
                    // 检查是否包含原始拖拽的文件
                    let authorizedPaths = Set(processedURLs.map { $0.path })
                    let requestedPaths = Set(urls.map { $0.path })
                    let missingFiles = requestedPaths.subtracting(authorizedPaths)
                    
                    if !missingFiles.isEmpty {
                        print("⚠️ 部分文件未被授权: \(missingFiles)")
                        self.showPartialAuthorizationWarning(missing: missingFiles, authorized: processedURLs, completion: completion)
                    } else {
                        completion(processedURLs)
                    }
                } else {
                    print("❌ 用户取消了文件授权")
                    completion(nil)
                }
            }
        }
    }
    
    /// 显示部分授权警告
    private func showPartialAuthorizationWarning(missing: Set<String>, authorized: [URL], completion: @escaping ([URL]?) -> Void) {
        DispatchQueue.main.async {
            let missingNames = missing.map { URL(fileURLWithPath: $0).lastPathComponent }.joined(separator: ", ")
            let alert = NSAlert()
            alert.messageText = "部分文件未授权"
            alert.informativeText = """
            以下文件未被授权访问：
            \(missingNames)
            
            您可以：
            • 继续处理已授权的文件
            • 重新尝试授权所有文件
            • 取消操作
            """
            alert.alertStyle = .warning
            alert.addButton(withTitle: "继续处理已授权文件")
            alert.addButton(withTitle: "重新授权")
            alert.addButton(withTitle: "取消")
            
            let response = alert.runModal()
            
            switch response {
            case .alertFirstButtonReturn:
                completion(authorized)
            case .alertSecondButtonReturn:
                // 重新尝试授权
                let allURLs = authorized + missing.map { URL(fileURLWithPath: $0) }
                self.requestSpecificFileAccess(for: allURLs, completion: completion)
            default:
                completion(nil)
            }
        }
    }
    
    // MARK: - 权限状态检查
    
    /// 获取权限状态描述
    func getPermissionStatusDescription() -> String {
        let hasSystemPermissions = checkSystemPermissions()
        
        if hasSystemPermissions {
            return "✅ 应用已获得系统文件访问权限"
        } else {
            return "⚠️ 应用需要系统文件访问权限才能更好地支持拖拽功能"
        }
    }
}