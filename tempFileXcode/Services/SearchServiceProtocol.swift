import Foundation
import CoreData

// MARK: - Search Service Protocol
protocol SearchServiceProtocol: ObservableObject {
    
    // MARK: - Search Operations
    func searchContent(query: String) async throws -> [ContentItem]
    func searchContent(query: String, in items: [ContentItem]) -> [ContentItem]
    
    // MARK: - Filter Operations
    func filterByTags(_ tags: [Tag]) async throws -> [ContentItem]
    func filterByTags(_ tagNames: [String]) async throws -> [ContentItem]
    func filterByType(_ type: ContentType) async throws -> [ContentItem]
    func filterByDateRange(from startDate: Date, to endDate: Date) async throws -> [ContentItem]
    func filterBySize(min: Int64?, max: Int64?) async throws -> [ContentItem]
    func filterExpiredContent() async throws -> [ContentItem]
    func filterPermanentContent() async throws -> [ContentItem]
    
    // MARK: - Combined Search and Filter
    func searchAndFilter(
        query: String?,
        tags: [String]?,
        type: ContentType?,
        dateRange: DateRange?,
        sizeRange: SizeRange?,
        includeExpired: Bool
    ) async throws -> [ContentItem]
    
    // MARK: - Search Suggestions
    func getSearchSuggestions(for query: String) async throws -> [SearchSuggestion]
    func getPopularTags(limit: Int) async throws -> [Tag]
    func getRecentSearches() -> [String]
    
    // MARK: - Search History
    func addToSearchHistory(_ query: String)
    func clearSearchHistory()
    
    // MARK: - Search Statistics
    func getSearchStatistics() async throws -> SearchStatistics
}

// MARK: - Supporting Types
struct DateRange {
    let startDate: Date
    let endDate: Date
    
    init(startDate: Date, endDate: Date) {
        self.startDate = startDate
        self.endDate = endDate
    }
    
    static func today() -> DateRange {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: Date())
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
        return DateRange(startDate: startOfDay, endDate: endOfDay)
    }
    
    static func thisWeek() -> DateRange {
        let calendar = Calendar.current
        let now = Date()
        let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
        let endOfWeek = calendar.date(byAdding: .weekOfYear, value: 1, to: startOfWeek)!
        return DateRange(startDate: startOfWeek, endDate: endOfWeek)
    }
    
    static func thisMonth() -> DateRange {
        let calendar = Calendar.current
        let now = Date()
        let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
        let endOfMonth = calendar.date(byAdding: .month, value: 1, to: startOfMonth)!
        return DateRange(startDate: startOfMonth, endDate: endOfMonth)
    }
    
    static func last30Days() -> DateRange {
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -30, to: endDate)!
        return DateRange(startDate: startDate, endDate: endDate)
    }
}

struct SizeRange {
    let minSize: Int64?
    let maxSize: Int64?
    
    init(minSize: Int64? = nil, maxSize: Int64? = nil) {
        self.minSize = minSize
        self.maxSize = maxSize
    }
    
    static func small() -> SizeRange {
        return SizeRange(minSize: nil, maxSize: 1024 * 1024) // < 1MB
    }
    
    static func medium() -> SizeRange {
        return SizeRange(minSize: 1024 * 1024, maxSize: 10 * 1024 * 1024) // 1MB - 10MB
    }
    
    static func large() -> SizeRange {
        return SizeRange(minSize: 10 * 1024 * 1024, maxSize: nil) // > 10MB
    }
}

struct SearchSuggestion {
    let text: String
    let type: SuggestionType
    let count: Int?
    
    enum SuggestionType {
        case query
        case tag
        case fileName
        case contentType
    }
}

struct SearchStatistics {
    let totalSearches: Int
    let popularQueries: [String]
    let popularTags: [String]
    let searchesByType: [ContentType: Int]
    let averageResultsPerSearch: Double
    let lastSearchDate: Date?
}

// MARK: - Search Result Highlighting
struct SearchResult {
    let item: ContentItem
    let highlights: [SearchHighlight]
    let relevanceScore: Double
}

struct SearchHighlight {
    let field: HighlightField
    let ranges: [NSRange]
    let snippet: String?
    
    enum HighlightField {
        case title
        case content
        case notes
        case fileName
        case tags
    }
}

// MARK: - Search Options
struct SearchOptions {
    let caseSensitive: Bool
    let wholeWords: Bool
    let useRegex: Bool
    let includeContent: Bool
    let includeTags: Bool
    let includeFileName: Bool
    let includeNotes: Bool
    let maxResults: Int?
    let sortBy: SortOption
    
    enum SortOption {
        case relevance
        case dateCreated
        case dateModified
        case title
        case size
        case type
    }
    
    static let `default` = SearchOptions(
        caseSensitive: false,
        wholeWords: false,
        useRegex: false,
        includeContent: true,
        includeTags: true,
        includeFileName: true,
        includeNotes: true,
        maxResults: nil,
        sortBy: .relevance
    )
}