import Foundation
import CoreData
import CryptoKit
import UniformTypeIdentifiers
import os.log
import Combine

// MARK: - Smart Organization Service Protocol
protocol SmartOrganizationServiceProtocol: ObservableObject {
    func detectDuplicates(in items: [ContentItem]) async -> [DuplicateGroup]
    func suggestBatchOrganization(for items: [ContentItem]) async -> [BatchSuggestion]
    func suggestTags(for item: ContentItem) async -> [String]
    func analyzeFileContent(_ item: ContentItem) async throws -> FileAnalysis
}

// MARK: - Duplicate Group
struct DuplicateGroup: Identifiable {
    let id = UUID()
    let items: [ContentItem]
    let similarity: Double
    let reason: DuplicateReason
    
    var primaryItem: ContentItem? {
        // 选择最新的作为主要项目
        items.max { ($0.createdAt ?? Date.distantPast) < ($1.createdAt ?? Date.distantPast) }
    }
    
    var duplicateItems: [ContentItem] {
        guard let primary = primaryItem else { return items }
        return items.filter { $0.id != primary.id }
    }
}

// MARK: - Duplicate Reason
enum DuplicateReason: String, CaseIterable {
    case identicalContent = "identical_content"
    case identicalHash = "identical_hash"
    case similarFileName = "similar_filename"
    case similarContent = "similar_content"
    
    var displayName: String {
        switch self {
        case .identicalContent:
            return "内容完全相同"
        case .identicalHash:
            return "文件哈希相同"
        case .similarFileName:
            return "文件名相似"
        case .similarContent:
            return "内容相似"
        }
    }
    
    var confidence: Double {
        switch self {
        case .identicalContent, .identicalHash:
            return 1.0
        case .similarFileName:
            return 0.7
        case .similarContent:
            return 0.8
        }
    }
}

// MARK: - Batch Suggestion
struct BatchSuggestion: Identifiable {
    let id = UUID()
    let name: String
    let items: [ContentItem]
    let reason: OrganizationReason
    let confidence: Double
    
    var description: String {
        switch reason {
        case .contentType:
            return "基于文件类型：\(items.first?.contentTypeEnum.displayName ?? "")"
        case .creationDate:
            return "基于创建时间"
        case .fileExtension:
            return "基于文件扩展名"
        case .contentSimilarity:
            return "基于内容相似性"
        case .tags:
            return "基于标签"
        }
    }
}

// MARK: - Organization Reason
enum OrganizationReason: String, CaseIterable {
    case contentType = "content_type"
    case creationDate = "creation_date"
    case fileExtension = "file_extension"
    case contentSimilarity = "content_similarity"
    case tags = "tags"
}

// MARK: - File Analysis
struct FileAnalysis {
    let contentHash: String
    let textContent: String?
    let suggestedTags: [String]
    let contentType: String
    let metadata: [String: Any]
    let keywords: [String]
}

// MARK: - Smart Organization Service Implementation
@MainActor
class SmartOrganizationService: SmartOrganizationServiceProtocol, ObservableObject {
    
    private let logger = Logger(subsystem: "com.contentmanager.app", category: "SmartOrganization")
    private let contentService: ContentService
    
    init(contentService: ContentService) {
        self.contentService = contentService
    }
    
    // MARK: - Duplicate Detection
    func detectDuplicates(in items: [ContentItem]) async -> [DuplicateGroup] {
        logger.info("Starting duplicate detection for \(items.count) items")
        
        var duplicateGroups: [DuplicateGroup] = []
        var processedItems: Set<UUID> = []
        
        for item in items {
            guard let itemId = item.id, !processedItems.contains(itemId) else { continue }
            
            var duplicates: [ContentItem] = [item]
            
            // 检查与其他项目的重复
            for otherItem in items {
                guard let otherId = otherItem.id,
                      otherId != itemId,
                      !processedItems.contains(otherId) else { continue }
                
                if let duplicateReason = await checkDuplicate(item, otherItem) {
                    duplicates.append(otherItem)
                    processedItems.insert(otherId)
                    
                    if duplicates.count >= 2 {
                        let group = DuplicateGroup(
                            items: duplicates,
                            similarity: duplicateReason.confidence,
                            reason: duplicateReason
                        )
                        duplicateGroups.append(group)
                    }
                }
            }
            
            processedItems.insert(itemId)
        }
        
        logger.info("Found \(duplicateGroups.count) duplicate groups")
        return duplicateGroups
    }
    
    private func checkDuplicate(_ item1: ContentItem, _ item2: ContentItem) async -> DuplicateReason? {
        // 1. 检查文件哈希（对于文件类型）
        if item1.contentTypeEnum == .file && item2.contentTypeEnum == .file {
            if let hash1 = await calculateFileHash(item1),
               let hash2 = await calculateFileHash(item2),
               hash1 == hash2 {
                return .identicalHash
            }
        }
        
        // 2. 检查文本内容
        if item1.contentTypeEnum == .text && item2.contentTypeEnum == .text {
            if let content1 = item1.content?.trimmingCharacters(in: .whitespacesAndNewlines),
               let content2 = item2.content?.trimmingCharacters(in: .whitespacesAndNewlines),
               !content1.isEmpty && !content2.isEmpty {
                
                if content1 == content2 {
                    return .identicalContent
                }
                
                let similarity = calculateTextSimilarity(content1, content2)
                if similarity > 0.9 {
                    return .similarContent
                }
            }
        }
        
        // 3. 检查文件名相似性
        if let name1 = item1.fileName ?? item1.title,
           let name2 = item2.fileName ?? item2.title {
            let similarity = calculateFileNameSimilarity(name1, name2)
            if similarity > 0.8 {
                return .similarFileName
            }
        }
        
        return nil
    }
    
    private func calculateFileHash(_ item: ContentItem) async -> String? {
        do {
            guard let data = try await contentService.loadContentFile(for: item) else { return nil }
            let hash = SHA256.hash(data: data)
            return hash.compactMap { String(format: "%02x", $0) }.joined()
        } catch {
            logger.error("Failed to calculate file hash: \(error)")
            return nil
        }
    }
    
    private func calculateTextSimilarity(_ text1: String, _ text2: String) -> Double {
        let words1 = Set(text1.lowercased().components(separatedBy: .whitespacesAndNewlines))
        let words2 = Set(text2.lowercased().components(separatedBy: .whitespacesAndNewlines))
        
        let intersection = words1.intersection(words2).count
        let union = words1.union(words2).count
        
        return union > 0 ? Double(intersection) / Double(union) : 0.0
    }
    
    private func calculateFileNameSimilarity(_ name1: String, _ name2: String) -> Double {
        let cleanName1 = name1.lowercased()
        let cleanName2 = name2.lowercased()
        
        // 使用编辑距离计算相似度
        let editDistance = levenshteinDistance(cleanName1, cleanName2)
        let maxLength = max(cleanName1.count, cleanName2.count)
        
        return maxLength > 0 ? 1.0 - Double(editDistance) / Double(maxLength) : 0.0
    }
    
    private func levenshteinDistance(_ s1: String, _ s2: String) -> Int {
        let a = Array(s1)
        let b = Array(s2)
        let m = a.count
        let n = b.count
        
        var dp = Array(repeating: Array(repeating: 0, count: n + 1), count: m + 1)
        
        for i in 0...m {
            dp[i][0] = i
        }
        
        for j in 0...n {
            dp[0][j] = j
        }
        
        for i in 1...m {
            for j in 1...n {
                if a[i-1] == b[j-1] {
                    dp[i][j] = dp[i-1][j-1]
                } else {
                    dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
                }
            }
        }
        
        return dp[m][n]
    }
    
    // MARK: - Batch Organization Suggestions
    func suggestBatchOrganization(for items: [ContentItem]) async -> [BatchSuggestion] {
        logger.info("Generating batch organization suggestions for \(items.count) items")
        
        var suggestions: [BatchSuggestion] = []
        
        // 1. 按内容类型分组
        let typeGroups = Dictionary(grouping: items) { $0.contentTypeEnum }
        for (type, typeItems) in typeGroups where typeItems.count >= 2 {
            suggestions.append(BatchSuggestion(
                name: "\(type.displayName)批次",
                items: typeItems,
                reason: .contentType,
                confidence: 0.9
            ))
        }
        
        // 2. 按创建日期分组（同一天）
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        let dateGroups = Dictionary(grouping: items) { item in
            dateFormatter.string(from: item.createdAt ?? Date())
        }
        
        for (date, dateItems) in dateGroups where dateItems.count >= 3 {
            suggestions.append(BatchSuggestion(
                name: "\(date) 批次",
                items: dateItems,
                reason: .creationDate,
                confidence: 0.7
            ))
        }
        
        // 3. 按文件扩展名分组
        let extensionGroups = Dictionary(grouping: items.filter { $0.fileName != nil }) { item in
            URL(fileURLWithPath: item.fileName!).pathExtension.lowercased()
        }
        
        for (ext, extItems) in extensionGroups where !ext.isEmpty && extItems.count >= 2 {
            suggestions.append(BatchSuggestion(
                name: "\(ext.uppercased()) 文件批次",
                items: extItems,
                reason: .fileExtension,
                confidence: 0.8
            ))
        }
        
        // 4. 按标签分组
        let tagGroups = items.reduce(into: [String: [ContentItem]]()) { result, item in
            for tag in item.tagNames {
                result[tag, default: []].append(item)
            }
        }
        
        for (tag, tagItems) in tagGroups where tagItems.count >= 2 {
            suggestions.append(BatchSuggestion(
                name: "\(tag) 标签批次",
                items: tagItems,
                reason: .tags,
                confidence: 0.85
            ))
        }
        
        // 按置信度排序
        suggestions.sort { $0.confidence > $1.confidence }
        
        logger.info("Generated \(suggestions.count) batch suggestions")
        return suggestions
    }
    
    // MARK: - Tag Suggestions
    func suggestTags(for item: ContentItem) async -> [String] {
        var suggestedTags: [String] = []
        
        // 基于内容类型的默认标签
        switch item.contentTypeEnum {
        case .text:
            suggestedTags.append(contentsOf: ["文本", "笔记"])
        case .image:
            suggestedTags.append(contentsOf: ["图片", "媒体"])
        case .file:
            suggestedTags.append(contentsOf: ["文件", "文档"])
        }
        
        // 基于文件扩展名的标签
        if let fileName = item.fileName {
            let ext = URL(fileURLWithPath: fileName).pathExtension.lowercased()
            switch ext {
            case "pdf":
                suggestedTags.append("PDF")
            case "doc", "docx":
                suggestedTags.append("Word文档")
            case "xls", "xlsx":
                suggestedTags.append("电子表格")
            case "ppt", "pptx":
                suggestedTags.append("演示文稿")
            case "zip", "rar", "7z":
                suggestedTags.append("压缩文件")
            case "jpg", "jpeg", "png", "gif":
                suggestedTags.append("图片")
            case "mp4", "mov", "avi":
                suggestedTags.append("视频")
            case "mp3", "wav", "m4a":
                suggestedTags.append("音频")
            default:
                break
            }
        }
        
        // 基于内容关键词的标签
        if let content = item.content {
            let keywords = extractKeywords(from: content)
            suggestedTags.append(contentsOf: keywords.prefix(3))
        }
        
        // 基于创建时间的标签
        let calendar = Calendar.current
        let now = Date()
        if let createdAt = item.createdAt {
            if calendar.isDate(createdAt, inSameDayAs: now) {
                suggestedTags.append("今天")
            } else if calendar.isDate(createdAt, equalTo: now, toGranularity: .weekOfYear) {
                suggestedTags.append("本周")
            }
        }
        
        return Array(Set(suggestedTags)) // 去重
    }
    
    private func extractKeywords(from text: String) -> [String] {
        let words = text.lowercased()
            .components(separatedBy: .whitespacesAndNewlines)
            .filter { $0.count > 3 && $0.count < 15 }
        
        // 简单的关键词提取（可以后续使用自然语言处理改进）
        let stopWords = Set(["的", "和", "在", "是", "了", "有", "我", "你", "他", "她", "它", "我们", "你们", "他们"])
        return Array(Set(words.filter { !stopWords.contains($0) })).prefix(5).map { String($0) }
    }
    
    // MARK: - File Analysis
    func analyzeFileContent(_ item: ContentItem) async throws -> FileAnalysis {
        var textContent: String?
        var metadata: [String: Any] = [:]
        var keywords: [String] = []
        
        // 计算内容哈希
        let contentHash: String
        if let content = item.content {
            contentHash = SHA256.hash(data: Data(content.utf8))
                .compactMap { String(format: "%02x", $0) }.joined()
            textContent = content
            keywords = extractKeywords(from: content)
        } else if let fileData = try await contentService.loadContentFile(for: item) {
            contentHash = SHA256.hash(data: fileData)
                .compactMap { String(format: "%02x", $0) }.joined()
            
            // 尝试从文件中提取文本（简单实现）
            if item.contentTypeEnum == .file {
                textContent = extractTextFromFile(fileData, fileName: item.fileName)
                if let text = textContent {
                    keywords = extractKeywords(from: text)
                }
            }
        } else {
            contentHash = UUID().uuidString
        }
        
        // 收集元数据
        metadata["fileSize"] = item.fileSize
        metadata["createdAt"] = item.createdAt?.timeIntervalSince1970
        metadata["contentType"] = item.contentType
        if let fileName = item.fileName {
            metadata["fileName"] = fileName
            metadata["fileExtension"] = URL(fileURLWithPath: fileName).pathExtension
        }
        
        let suggestedTags = await suggestTags(for: item)
        
        return FileAnalysis(
            contentHash: contentHash,
            textContent: textContent,
            suggestedTags: suggestedTags,
            contentType: item.contentType ?? "unknown",
            metadata: metadata,
            keywords: keywords
        )
    }
    
    private func extractTextFromFile(_ data: Data, fileName: String?) -> String? {
        guard let fileName = fileName else { return nil }
        
        let ext = URL(fileURLWithPath: fileName).pathExtension.lowercased()
        
        switch ext {
        case "txt", "md", "swift", "js", "html", "css", "json", "xml":
            return String(data: data, encoding: .utf8)
        default:
            // 对于其他格式，暂时返回nil
            // 可以后续添加更多格式的支持
            return nil
        }
    }
}