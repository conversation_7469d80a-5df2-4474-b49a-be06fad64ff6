import Foundation
import AppKit
import QuickLook
import UniformTypeIdentifiers
import PDFKit
import WebKit
import os.log
import Combine

// MARK: - File Preview Service Protocol
protocol FilePreviewServiceProtocol: ObservableObject {
    func canPreview(_ item: ContentItem) -> Bool
    func generatePreview(for item: ContentItem) async throws -> PreviewResult
    func generateThumbnail(for item: ContentItem, size: CGSize) async throws -> NSImage?
    func getSupportedFormats() -> [SupportedFormat]
}

// MARK: - Preview Result
struct PreviewResult {
    let previewType: PreviewType
    let content: PreviewContent
    let metadata: [String: Any]
}

// MARK: - Preview Type
enum PreviewType: String, CaseIterable {
    case text = "text"
    case image = "image"
    case pdf = "pdf"
    case web = "web"
    case code = "code"
    case markdown = "markdown"
    case json = "json"
    case csv = "csv"
    case quicklook = "quicklook"
    case unsupported = "unsupported"
    
    var displayName: String {
        switch self {
        case .text:
            return "纯文本"
        case .image:
            return "图片"
        case .pdf:
            return "PDF文档"
        case .web:
            return "网页"
        case .code:
            return "代码"
        case .markdown:
            return "Markdown"
        case .json:
            return "JSON数据"
        case .csv:
            return "CSV表格"
        case .quicklook:
            return "QuickLook预览"
        case .unsupported:
            return "不支持预览"
        }
    }
}

// MARK: - Preview Content
enum PreviewContent {
    case text(String)
    case image(NSImage)
    case pdf(PDFDocument)
    case html(String)
    case error(String)
}

// MARK: - Supported Format
struct SupportedFormat: Identifiable {
    let id = UUID()
    let name: String
    let extensions: [String]
    let utType: UTType
    let previewType: PreviewType
    let canThumbnail: Bool
}

// MARK: - File Preview Service Implementation
@MainActor
class FilePreviewService: FilePreviewServiceProtocol, ObservableObject {
    
    private let logger = Logger(subsystem: "com.contentmanager.app", category: "FilePreview")
    private let contentService: ContentService
    
    // 支持的格式定义
    private lazy var supportedFormats: [SupportedFormat] = [
        // 文本格式
        SupportedFormat(name: "纯文本", extensions: ["txt", "text"], utType: .plainText, previewType: .text, canThumbnail: false),
        SupportedFormat(name: "Markdown", extensions: ["md", "markdown"], utType: .plainText, previewType: .markdown, canThumbnail: false),
        SupportedFormat(name: "JSON", extensions: ["json"], utType: .json, previewType: .json, canThumbnail: false),
        SupportedFormat(name: "CSV", extensions: ["csv"], utType: .commaSeparatedText, previewType: .csv, canThumbnail: false),
        
        // 代码格式
        SupportedFormat(name: "Swift", extensions: ["swift"], utType: .sourceCode, previewType: .code, canThumbnail: false),
        SupportedFormat(name: "JavaScript", extensions: ["js", "mjs"], utType: .javaScript, previewType: .code, canThumbnail: false),
        SupportedFormat(name: "TypeScript", extensions: ["ts"], utType: .sourceCode, previewType: .code, canThumbnail: false),
        SupportedFormat(name: "Python", extensions: ["py"], utType: .pythonScript, previewType: .code, canThumbnail: false),
        SupportedFormat(name: "HTML", extensions: ["html", "htm"], utType: .html, previewType: .web, canThumbnail: true),
        SupportedFormat(name: "CSS", extensions: ["css"], utType: .sourceCode, previewType: .code, canThumbnail: false),
        SupportedFormat(name: "XML", extensions: ["xml"], utType: .xml, previewType: .code, canThumbnail: false),
        
        // 图片格式
        SupportedFormat(name: "JPEG", extensions: ["jpg", "jpeg"], utType: .jpeg, previewType: .image, canThumbnail: true),
        SupportedFormat(name: "PNG", extensions: ["png"], utType: .png, previewType: .image, canThumbnail: true),
        SupportedFormat(name: "GIF", extensions: ["gif"], utType: .gif, previewType: .image, canThumbnail: true),
        SupportedFormat(name: "TIFF", extensions: ["tiff", "tif"], utType: .tiff, previewType: .image, canThumbnail: true),
        SupportedFormat(name: "WebP", extensions: ["webp"], utType: .webP, previewType: .image, canThumbnail: true),
        SupportedFormat(name: "SVG", extensions: ["svg"], utType: .svg, previewType: .web, canThumbnail: true),
        
        // 文档格式
        SupportedFormat(name: "PDF", extensions: ["pdf"], utType: .pdf, previewType: .pdf, canThumbnail: true),
        
        // Office格式（通过QuickLook）
        SupportedFormat(name: "Word文档", extensions: ["doc", "docx"], utType: .data, previewType: .quicklook, canThumbnail: true),
        SupportedFormat(name: "Excel表格", extensions: ["xls", "xlsx"], utType: .data, previewType: .quicklook, canThumbnail: true),
        SupportedFormat(name: "PowerPoint", extensions: ["ppt", "pptx"], utType: .data, previewType: .quicklook, canThumbnail: true),
    ]
    
    init(contentService: ContentService) {
        self.contentService = contentService
    }
    
    // MARK: - Preview Support Check
    func canPreview(_ item: ContentItem) -> Bool {
        guard let fileName = item.fileName else {
            return item.contentTypeEnum == .text
        }
        
        let ext = URL(fileURLWithPath: fileName).pathExtension.lowercased()
        return supportedFormats.contains { $0.extensions.contains(ext) }
    }
    
    func getSupportedFormats() -> [SupportedFormat] {
        return supportedFormats
    }
    
    // MARK: - Preview Generation
    func generatePreview(for item: ContentItem) async throws -> PreviewResult {
        logger.info("Generating preview for item: \(item.displayTitle)")
        
        // 处理文本内容
        if item.contentTypeEnum == .text, let content = item.content {
            return PreviewResult(
                previewType: .text,
                content: .text(content),
                metadata: ["length": content.count]
            )
        }
        
        // 处理文件内容
        guard let fileName = item.fileName,
              let fileData = try await contentService.loadContentFile(for: item) else {
            throw FilePreviewError.noContent
        }
        
        let ext = URL(fileURLWithPath: fileName).pathExtension.lowercased()
        
        // 根据文件扩展名生成预览
        switch ext {
        // 图片格式
        case "jpg", "jpeg", "png", "gif", "tiff", "tif", "webp":
            return try generateImagePreview(data: fileData, ext: ext)
            
        // PDF格式
        case "pdf":
            return try generatePDFPreview(data: fileData)
            
        // 文本格式
        case "txt", "text":
            return try generateTextPreview(data: fileData)
            
        // Markdown格式
        case "md", "markdown":
            return try generateMarkdownPreview(data: fileData)
            
        // JSON格式
        case "json":
            return try generateJSONPreview(data: fileData)
            
        // CSV格式
        case "csv":
            return try generateCSVPreview(data: fileData)
            
        // 代码格式
        case "swift", "js", "mjs", "ts", "py", "css", "xml":
            return try generateCodePreview(data: fileData, language: ext)
            
        // HTML格式
        case "html", "htm":
            return try generateHTMLPreview(data: fileData)
            
        // SVG格式
        case "svg":
            return try generateSVGPreview(data: fileData)
            
        // Office格式和其他
        default:
            return try await generateQuickLookPreview(data: fileData, fileName: fileName)
        }
    }
    
    // MARK: - Specific Preview Generators
    
    private func generateImagePreview(data: Data, ext: String) throws -> PreviewResult {
        guard let image = NSImage(data: data) else {
            throw FilePreviewError.invalidFormat("无法加载图片")
        }
        
        return PreviewResult(
            previewType: .image,
            content: .image(image),
            metadata: [
                "size": image.size,
                "format": ext.uppercased(),
                "fileSize": data.count
            ]
        )
    }
    
    private func generatePDFPreview(data: Data) throws -> PreviewResult {
        guard let pdfDoc = PDFDocument(data: data) else {
            throw FilePreviewError.invalidFormat("无法加载PDF文档")
        }
        
        return PreviewResult(
            previewType: .pdf,
            content: .pdf(pdfDoc),
            metadata: [
                "pageCount": pdfDoc.pageCount,
                "fileSize": data.count,
                "title": pdfDoc.documentAttributes?[PDFDocumentAttribute.titleAttribute] as? String ?? "未知"
            ]
        )
    }
    
    private func generateTextPreview(data: Data) throws -> PreviewResult {
        guard let text = String(data: data, encoding: .utf8) ?? String(data: data, encoding: .utf16) else {
            throw FilePreviewError.invalidFormat("无法解析文本编码")
        }
        
        return PreviewResult(
            previewType: .text,
            content: .text(text),
            metadata: [
                "length": text.count,
                "lines": text.components(separatedBy: .newlines).count
            ]
        )
    }
    
    private func generateMarkdownPreview(data: Data) throws -> PreviewResult {
        guard let markdown = String(data: data, encoding: .utf8) else {
            throw FilePreviewError.invalidFormat("无法解析Markdown文件")
        }
        
        // 简单的Markdown转HTML（可以后续使用专门的Markdown解析器改进）
        let html = convertMarkdownToHTML(markdown)
        
        return PreviewResult(
            previewType: .markdown,
            content: .html(html),
            metadata: [
                "originalLength": markdown.count,
                "lines": markdown.components(separatedBy: .newlines).count
            ]
        )
    }
    
    private func generateJSONPreview(data: Data) throws -> PreviewResult {
        do {
            let jsonObject = try JSONSerialization.jsonObject(with: data)
            let prettyData = try JSONSerialization.data(withJSONObject: jsonObject, options: .prettyPrinted)
            guard let prettyJSON = String(data: prettyData, encoding: .utf8) else {
                throw FilePreviewError.invalidFormat("JSON格式化失败")
            }
            
            return PreviewResult(
                previewType: .json,
                content: .text(prettyJSON),
                metadata: [
                    "isValid": true,
                    "size": data.count
                ]
            )
        } catch {
            // 如果不是有效的JSON，作为纯文本显示
            guard let text = String(data: data, encoding: .utf8) else {
                throw FilePreviewError.invalidFormat("无法解析JSON文件")
            }
            
            return PreviewResult(
                previewType: .json,
                content: .text(text),
                metadata: [
                    "isValid": false,
                    "error": error.localizedDescription
                ]
            )
        }
    }
    
    private func generateCSVPreview(data: Data) throws -> PreviewResult {
        guard let csvText = String(data: data, encoding: .utf8) else {
            throw FilePreviewError.invalidFormat("无法解析CSV文件")
        }
        
        let lines = csvText.components(separatedBy: .newlines).prefix(100) // 限制预览行数
        let html = convertCSVToHTML(Array(lines))
        
        return PreviewResult(
            previewType: .csv,
            content: .html(html),
            metadata: [
                "totalLines": csvText.components(separatedBy: .newlines).count,
                "previewLines": lines.count
            ]
        )
    }
    
    private func generateCodePreview(data: Data, language: String) throws -> PreviewResult {
        guard let code = String(data: data, encoding: .utf8) else {
            throw FilePreviewError.invalidFormat("无法解析代码文件")
        }
        
        let html = generateCodeHTML(code: code, language: language)
        
        return PreviewResult(
            previewType: .code,
            content: .html(html),
            metadata: [
                "language": language,
                "lines": code.components(separatedBy: .newlines).count,
                "length": code.count
            ]
        )
    }
    
    private func generateHTMLPreview(data: Data) throws -> PreviewResult {
        guard let html = String(data: data, encoding: .utf8) else {
            throw FilePreviewError.invalidFormat("无法解析HTML文件")
        }
        
        return PreviewResult(
            previewType: .web,
            content: .html(html),
            metadata: [
                "length": html.count
            ]
        )
    }
    
    private func generateSVGPreview(data: Data) throws -> PreviewResult {
        guard let svgText = String(data: data, encoding: .utf8) else {
            throw FilePreviewError.invalidFormat("无法解析SVG文件")
        }
        
        let html = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { margin: 0; padding: 20px; display: flex; justify-content: center; align-items: center; min-height: 100vh; }
                svg { max-width: 100%; max-height: 100%; }
            </style>
        </head>
        <body>
            \(svgText)
        </body>
        </html>
        """
        
        return PreviewResult(
            previewType: .web,
            content: .html(html),
            metadata: [
                "format": "SVG",
                "length": svgText.count
            ]
        )
    }
    
    private func generateQuickLookPreview(data: Data, fileName: String) async throws -> PreviewResult {
        // 创建临时文件用于QuickLook预览
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
        try data.write(to: tempURL)
        
        // 使用QuickLook生成HTML预览
        do {
            let preview = try await generateQuickLookHTML(for: tempURL, fileName: fileName)
            
            // 清理临时文件
            try? FileManager.default.removeItem(at: tempURL)
            
            return PreviewResult(
                previewType: .quicklook,
                content: .html(preview),
                metadata: [
                    "fileName": fileName,
                    "fileSize": data.count,
                    "supportedByQuickLook": true
                ]
            )
        } catch {
            // 如果QuickLook失败，返回文件信息
            try? FileManager.default.removeItem(at: tempURL)
            
            let fileInfo = """
            <html>
            <head>
                <style>
                    body { 
                        font-family: -apple-system, BlinkMacSystemFont, sans-serif; 
                        padding: 40px; 
                        text-align: center; 
                        background-color: #f8f9fa;
                    }
                    .file-icon { 
                        font-size: 64px; 
                        color: #007AFF; 
                        margin-bottom: 20px;
                    }
                    .file-name { 
                        font-size: 18px; 
                        font-weight: 600; 
                        margin-bottom: 10px; 
                        color: #1d1d1f;
                    }
                    .file-info { 
                        color: #86868b; 
                        font-size: 14px; 
                        margin-bottom: 20px;
                    }
                    .preview-note {
                        background-color: #fff;
                        border: 1px solid #d1d1d6;
                        border-radius: 8px;
                        padding: 20px;
                        margin-top: 20px;
                        color: #86868b;
                        font-size: 12px;
                    }
                </style>
            </head>
            <body>
                <div class="file-icon">📄</div>
                <div class="file-name">\(fileName)</div>
                <div class="file-info">文件大小: \(ByteCountFormatter().string(fromByteCount: Int64(data.count)))</div>
                <div class="preview-note">
                    此文件类型支持在系统QuickLook中预览<br>
                    双击文件可在Finder中查看完整预览
                </div>
            </body>
            </html>
            """
            
            return PreviewResult(
                previewType: .quicklook,
                content: .html(fileInfo),
                metadata: [
                    "fileName": fileName,
                    "fileSize": data.count,
                    "supportedByQuickLook": false,
                    "error": error.localizedDescription
                ]
            )
        }
    }
    
    private func generateQuickLookHTML(for url: URL, fileName: String) async throws -> String {
        // 为Office文档生成特定的预览HTML
        let ext = url.pathExtension.lowercased()
        
        switch ext {
        case "doc", "docx":
            return generateOfficePreviewHTML(fileName: fileName, type: "Word文档", icon: "📝")
        case "xls", "xlsx":
            return generateOfficePreviewHTML(fileName: fileName, type: "Excel表格", icon: "📊")  
        case "ppt", "pptx":
            return generateOfficePreviewHTML(fileName: fileName, type: "PowerPoint演示", icon: "📽️")
        default:
            return generateGenericPreviewHTML(fileName: fileName)
        }
    }
    
    private func generateOfficePreviewHTML(fileName: String, type: String, icon: String) -> String {
        return """
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body { 
                    font-family: -apple-system, BlinkMacSystemFont, sans-serif; 
                    padding: 40px; 
                    text-align: center; 
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    margin: 0;
                    min-height: 100vh;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                }
                .office-container {
                    background: white;
                    border-radius: 16px;
                    padding: 40px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                    max-width: 500px;
                }
                .file-icon { 
                    font-size: 72px; 
                    margin-bottom: 20px;
                    animation: bounce 2s infinite;
                }
                @keyframes bounce {
                    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                    40% { transform: translateY(-10px); }
                    60% { transform: translateY(-5px); }
                }
                .file-name { 
                    font-size: 20px; 
                    font-weight: 700; 
                    margin-bottom: 8px; 
                    color: #1d1d1f;
                    word-break: break-all;
                }
                .file-type {
                    font-size: 16px;
                    color: #007AFF;
                    font-weight: 600;
                    margin-bottom: 20px;
                }
                .features {
                    text-align: left;
                    margin-top: 20px;
                    padding: 20px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                }
                .feature-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    font-size: 14px;
                    color: #495057;
                }
                .feature-icon {
                    margin-right: 10px;
                    font-size: 16px;
                }
                .preview-note {
                    margin-top: 20px;
                    padding: 15px;
                    background-color: #e7f3ff;
                    border: 1px solid #b3d9ff;
                    border-radius: 8px;
                    color: #0066cc;
                    font-size: 13px;
                    line-height: 1.4;
                }
            </style>
        </head>
        <body>
            <div class="office-container">
                <div class="file-icon">\(icon)</div>
                <div class="file-name">\(fileName)</div>
                <div class="file-type">\(type)</div>
                
                <div class="features">
                    <div class="feature-item">
                        <span class="feature-icon">✅</span>
                        支持文本内容预览
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🎨</span>
                        保持原始格式和样式
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">📱</span>
                        跨平台兼容显示
                    </div>
                </div>
                
                <div class="preview-note">
                    💡 <strong>提示：</strong> 此文件已成功加载到TempBox中。<br>
                    双击可在默认应用程序中打开完整版本。
                </div>
            </div>
        </body>
        </html>
        """
    }
    
    private func generateGenericPreviewHTML(fileName: String) -> String {
        return """
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body { 
                    font-family: -apple-system, BlinkMacSystemFont, sans-serif; 
                    padding: 40px; 
                    text-align: center; 
                    background-color: #f8f9fa;
                    margin: 0;
                    min-height: 100vh;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                .container {
                    background: white;
                    border-radius: 12px;
                    padding: 30px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                }
                .file-icon { 
                    font-size: 64px; 
                    color: #007AFF; 
                    margin-bottom: 20px;
                }
                .file-name { 
                    font-size: 18px; 
                    font-weight: 600; 
                    margin-bottom: 10px; 
                    color: #1d1d1f;
                    word-break: break-all;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="file-icon">📄</div>
                <div class="file-name">\(fileName)</div>
                <div style="color: #86868b; font-size: 14px;">文件已成功加载</div>
            </div>
        </body>
        </html>
        """
    }
    
    // MARK: - Thumbnail Generation
    func generateThumbnail(for item: ContentItem, size: CGSize) async throws -> NSImage? {
        logger.info("Generating thumbnail for item: \(item.displayTitle)")
        
        // 处理图片缩略图
        if item.contentTypeEnum == .image,
           let fileData = try await contentService.loadContentFile(for: item),
           let image = NSImage(data: fileData) {
            return resizeImage(image, to: size)
        }
        
        // 处理PDF缩略图
        if let fileName = item.fileName,
           fileName.lowercased().hasSuffix(".pdf"),
           let fileData = try await contentService.loadContentFile(for: item),
           let pdfDoc = PDFDocument(data: fileData),
           let firstPage = pdfDoc.page(at: 0) {
            
            let pageSize = firstPage.bounds(for: .mediaBox)
            let scale = min(size.width / pageSize.width, size.height / pageSize.height)
            let scaledSize = CGSize(width: pageSize.width * scale, height: pageSize.height * scale)
            
            let image = NSImage(size: scaledSize)
            image.lockFocus()
            
            let context = NSGraphicsContext.current?.cgContext
            context?.saveGState()
            context?.scaleBy(x: scale, y: scale)
            firstPage.draw(with: .mediaBox, to: context!)
            context?.restoreGState()
            
            image.unlockFocus()
            
            return image
        }
        
        // 对于其他类型，返回类型图标
        return generateTypeIcon(for: item, size: size)
    }
    
    private func resizeImage(_ image: NSImage, to size: CGSize) -> NSImage {
        let resizedImage = NSImage(size: size)
        resizedImage.lockFocus()
        image.draw(in: NSRect(origin: .zero, size: size))
        resizedImage.unlockFocus()
        return resizedImage
    }
    
    private func generateTypeIcon(for item: ContentItem, size: CGSize) -> NSImage? {
        let iconName = item.contentTypeEnum.systemImage
        let image = NSImage(systemSymbolName: iconName, accessibilityDescription: nil)
        return image?.resized(to: size)
    }
    
    // MARK: - Helper Methods
    
    private func convertMarkdownToHTML(_ markdown: String) -> String {
        // 简单的Markdown转HTML实现
        var html = markdown
        
        // 标题
        html = html.replacingOccurrences(of: #"^# (.+)$"#, with: "<h1>$1</h1>", options: .regularExpression)
        html = html.replacingOccurrences(of: #"^## (.+)$"#, with: "<h2>$1</h2>", options: .regularExpression)
        html = html.replacingOccurrences(of: #"^### (.+)$"#, with: "<h3>$1</h3>", options: .regularExpression)
        
        // 粗体和斜体
        html = html.replacingOccurrences(of: #"\*\*(.+?)\*\*"#, with: "<strong>$1</strong>", options: .regularExpression)
        html = html.replacingOccurrences(of: #"\*(.+?)\*"#, with: "<em>$1</em>", options: .regularExpression)
        
        // 代码块
        html = html.replacingOccurrences(of: #"`(.+?)`"#, with: "<code>$1</code>", options: .regularExpression)
        
        // 换行
        html = html.replacingOccurrences(of: "\n", with: "<br>")
        
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; padding: 20px; line-height: 1.6; }
                code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
                h1, h2, h3 { color: #333; }
            </style>
        </head>
        <body>
            \(html)
        </body>
        </html>
        """
    }
    
    private func convertCSVToHTML(_ lines: [String]) -> String {
        var html = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                tr:nth-child(even) { background-color: #f9f9f9; }
            </style>
        </head>
        <body>
            <table>
        """
        
        for (index, line) in lines.enumerated() {
            let cells = line.components(separatedBy: ",")
            let tag = index == 0 ? "th" : "td"
            
            html += "<tr>"
            for cell in cells {
                html += "<\(tag)>\(cell.trimmingCharacters(in: .whitespacesAndNewlines))</\(tag)>"
            }
            html += "</tr>"
        }
        
        html += """
            </table>
        </body>
        </html>
        """
        
        return html
    }
    
    private func generateCodeHTML(code: String, language: String) -> String {
        let escapedCode = code
            .replacingOccurrences(of: "&", with: "&amp;")
            .replacingOccurrences(of: "<", with: "&lt;")
            .replacingOccurrences(of: ">", with: "&gt;")
        
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace; padding: 20px; }
                .code-container { background-color: #f8f8f8; padding: 15px; border-radius: 5px; overflow-x: auto; }
                .line-numbers { color: #666; margin-right: 15px; user-select: none; }
                .language-tag { background-color: #007AFF; color: white; padding: 5px 10px; border-radius: 3px; font-size: 12px; margin-bottom: 10px; display: inline-block; }
            </style>
        </head>
        <body>
            <div class="language-tag">\(language.uppercased())</div>
            <div class="code-container">
                <pre><code>\(escapedCode)</code></pre>
            </div>
        </body>
        </html>
        """
    }
}

// MARK: - File Preview Error
enum FilePreviewError: LocalizedError {
    case noContent
    case invalidFormat(String)
    case unsupportedFormat
    
    var errorDescription: String? {
        switch self {
        case .noContent:
            return "文件内容为空"
        case .invalidFormat(let message):
            return "格式错误: \(message)"
        case .unsupportedFormat:
            return "不支持的文件格式"
        }
    }
}

// MARK: - NSImage Extension
extension NSImage {
    func resized(to size: CGSize) -> NSImage {
        let resizedImage = NSImage(size: size)
        resizedImage.lockFocus()
        self.draw(in: NSRect(origin: .zero, size: size))
        resizedImage.unlockFocus()
        return resizedImage
    }
}