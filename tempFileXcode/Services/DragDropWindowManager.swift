import Foundation
import AppKit
import SwiftUI
import Combine

/// 拖拽窗口管理器（增强版） - 支持动态定位和视觉反馈
class DragDropWindowManager: ObservableObject {
    @Published var isWindowVisible: Bool = false
    @Published var dragProgress: Double = 0.0
    @Published var dragStatus: DragStatus = .waiting
    
    private var overlayWindow: DragDropOverlayWindow?
    private let contentService: ContentService
    private let batchService: BatchService
    private var hideTimer: Timer?
    
    // 智能窗口定位
    private var preferredPosition: WindowPosition = .leftEdge
    private var lastMouseLocation: NSPoint?
    
    enum DragStatus {
        case waiting
        case detecting
        case active
        case processing
        case completed
        case error
    }
    
    enum WindowPosition {
        case leftEdge
        case rightEdge
        case topEdge
        case bottomEdge
        case center
        case followMouse
    }
    
    init(contentService: ContentService, batchService: BatchService) {
        self.contentService = contentService
        self.batchService = batchService
        setupMouseTracking()
    }
    
    // MARK: - 鼠标跟踪设置
    private func setupMouseTracking() {
        // 设置全局鼠标跟踪，用于智能窗口定位
        NSEvent.addGlobalMonitorForEvents(matching: [.mouseMoved]) { [weak self] event in
            self?.lastMouseLocation = event.locationInWindow
        }
    }
    
    // MARK: - 智能窗口管理
    
    /// 显示拖拽窗口（带智能定位）
    func showWindow() {
        DispatchQueue.main.async {
            self.dragStatus = .active
            
            if self.overlayWindow == nil {
                self.overlayWindow = DragDropOverlayWindow(
                    contentService: self.contentService,
                    batchService: self.batchService,
                    dragDropManager: self
                )
            }
            
            // 智能定位窗口
            self.positionWindowIntelligently()
            
            // 显示窗口并添加视觉效果
            self.overlayWindow?.showWindow()
            self.isWindowVisible = true
            self.startAutoHideTimer()
            
            // 添加显示动画
            self.animateWindowAppearance()
        }
    }
    
    /// 智能定位窗口
    private func positionWindowIntelligently() {
        guard let window = overlayWindow,
              let screen = NSScreen.main else { return }
        
        let screenFrame = screen.visibleFrame
        let windowSize = CGSize(width: 400, height: 300)
        
        let position = calculateOptimalPosition(screenFrame: screenFrame, windowSize: windowSize)
        
        window.setFrame(NSRect(origin: position, size: windowSize), display: true, animate: true)
    }
    
    /// 计算最佳窗口位置
    private func calculateOptimalPosition(screenFrame: NSRect, windowSize: CGSize) -> NSPoint {
        // 如果有鼠标位置信息，根据鼠标位置智能选择
        if let mouseLocation = lastMouseLocation {
            return calculatePositionNearMouse(mouseLocation: mouseLocation, screenFrame: screenFrame, windowSize: windowSize)
        }
        
        // 否则使用默认的边缘位置
        switch preferredPosition {
        case .leftEdge:
            return NSPoint(x: screenFrame.minX + 20, y: screenFrame.midY - windowSize.height / 2)
        case .rightEdge:
            return NSPoint(x: screenFrame.maxX - windowSize.width - 20, y: screenFrame.midY - windowSize.height / 2)
        case .topEdge:
            return NSPoint(x: screenFrame.midX - windowSize.width / 2, y: screenFrame.maxY - windowSize.height - 20)
        case .bottomEdge:
            return NSPoint(x: screenFrame.midX - windowSize.width / 2, y: screenFrame.minY + 20)
        case .center:
            return NSPoint(x: screenFrame.midX - windowSize.width / 2, y: screenFrame.midY - windowSize.height / 2)
        case .followMouse:
            return lastMouseLocation ?? NSPoint(x: screenFrame.minX + 20, y: screenFrame.midY - windowSize.height / 2)
        }
    }
    
    /// 根据鼠标位置计算窗口位置
    private func calculatePositionNearMouse(mouseLocation: NSPoint, screenFrame: NSRect, windowSize: CGSize) -> NSPoint {
        let margin: CGFloat = 50
        
        // 计算鼠标在屏幕中的相对位置
        let relativeX = (mouseLocation.x - screenFrame.minX) / screenFrame.width
        let relativeY = (mouseLocation.y - screenFrame.minY) / screenFrame.height
        
        var x: CGFloat
        var y: CGFloat
        
        // 根据鼠标位置选择窗口位置，避免遮挡
        if relativeX < 0.5 {
            // 鼠标在左侧，窗口显示在右侧
            x = mouseLocation.x + margin
        } else {
            // 鼠标在右侧，窗口显示在左侧
            x = mouseLocation.x - windowSize.width - margin
        }
        
        if relativeY < 0.5 {
            // 鼠标在下方，窗口显示在上方
            y = mouseLocation.y + margin
        } else {
            // 鼠标在上方，窗口显示在下方
            y = mouseLocation.y - windowSize.height - margin
        }
        
        // 确保窗口在屏幕范围内
        x = max(screenFrame.minX + 10, min(x, screenFrame.maxX - windowSize.width - 10))
        y = max(screenFrame.minY + 10, min(y, screenFrame.maxY - windowSize.height - 10))
        
        return NSPoint(x: x, y: y)
    }
    
    /// 窗口出现动画
    private func animateWindowAppearance() {
        guard let window = overlayWindow else { return }
        
        // 初始状态：缩小和透明
        window.alphaValue = 0.0
        window.setFrame(NSRect(
            x: window.frame.midX - 50,
            y: window.frame.midY - 50,
            width: 100,
            height: 100
        ), display: false)
        
        // 动画到最终状态
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.3
            context.timingFunction = CAMediaTimingFunction(name: .easeOut)
            
            window.animator().alphaValue = 1.0
            window.animator().setFrame(NSRect(
                x: window.frame.midX - 200,
                y: window.frame.midY - 150,
                width: 400,
                height: 300
            ), display: true)
        }
    }
    
    /// 隐藏拖拽窗口（带动画）
    func hideWindow() {
        DispatchQueue.main.async {
            self.dragStatus = .waiting
            self.animateWindowDisappearance()
            self.stopAutoHideTimer()
        }
    }
    
    /// 窗口消失动画
    private func animateWindowDisappearance() {
        guard let window = overlayWindow else { return }
        
        NSAnimationContext.runAnimationGroup { context in
            context.duration = 0.2
            context.timingFunction = CAMediaTimingFunction(name: .easeIn)
            
            // 缩小和淡出
            window.animator().alphaValue = 0.0
            window.animator().setFrame(NSRect(
                x: window.frame.midX - 50,
                y: window.frame.midY - 50,
                width: 100,
                height: 100
            ), display: true)
        } completionHandler: {
            // 动画完成后真正隐藏窗口
            self.overlayWindow?.hideWindow()
            self.isWindowVisible = false
        }
    }
    
    /// 保持窗口可见（重置自动隐藏计时器）
    func keepWindowVisible() {
        DispatchQueue.main.async {
            self.startAutoHideTimer()
        }
    }
    
    /// 切换窗口显示状态
    func toggleWindow() {
        if windowVisible {
            hideWindow()
        } else {
            showWindow()
        }
    }
    
    // MARK: - 拖拽状态管理
    
    /// 更新拖拽进度
    func updateDragProgress(_ progress: Double) {
        DispatchQueue.main.async {
            self.dragProgress = max(0.0, min(1.0, progress))
        }
    }
    
    /// 设置拖拽状态
    func setDragStatus(_ status: DragStatus) {
        DispatchQueue.main.async {
            self.dragStatus = status
        }
    }
    
    /// 设置窗口首选位置
    func setPreferredPosition(_ position: WindowPosition) {
        preferredPosition = position
    }
    
    // MARK: - 自动隐藏计时器
    
    private func startAutoHideTimer() {
        stopAutoHideTimer()
        
        // 根据拖拽状态调整自动隐藏时间
        let hideInterval: TimeInterval
        switch dragStatus {
        case .processing:
            hideInterval = 30.0 // 处理中时延长显示时间
        case .active:
            hideInterval = 15.0 // 活跃状态中等时间
        default:
            hideInterval = 10.0 // 默认时间
        }
        
        hideTimer = Timer.scheduledTimer(withTimeInterval: hideInterval, repeats: false) { [weak self] _ in
            self?.hideWindow()
        }
    }
    
    private func stopAutoHideTimer() {
        hideTimer?.invalidate()
        hideTimer = nil
    }
    
    // MARK: - 窗口状态
    
    var windowVisible: Bool {
        return overlayWindow?.isVisible == true
    }
    
    // MARK: - 服务设置
    
    func setDragDetectionService(_ service: DragDetectionService) {
        // 这里可以设置拖拽检测服务的引用，如果需要的话
        print("设置拖拽检测服务")
    }
    
    // MARK: - 清理
    
    deinit {
        stopAutoHideTimer()
        overlayWindow = nil
    }
}