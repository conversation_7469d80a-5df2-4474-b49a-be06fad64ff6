import Foundation
import CoreData
import Combine
import os.log

// MARK: - Search Service Implementation
@MainActor
class SearchService: SearchServiceProtocol {
    
    // MARK: - Properties
    private let persistenceController: PersistenceController
    private let userDefaults = UserDefaults.standard
    
    // Search history
    private let searchHistoryKey = "SearchHistory"
    private let maxSearchHistoryItems = 50
    
    // Search statistics
    @Published var lastSearchQuery: String = ""
    @Published var lastSearchResults: [ContentItem] = []
    @Published var isSearching = false
    
    // MARK: - Initialization
    init(persistenceController: PersistenceController? = nil) {
        self.persistenceController = persistenceController ?? PersistenceController.shared
    }
    
    // MARK: - Search Operations
    func searchContent(query: String) async throws -> [ContentItem] {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return []
        }
        
        isSearching = true
        defer { isSearching = false }
        
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        addToSearchHistory(trimmedQuery)
        
        let results = try await persistenceController.performBackgroundTask { context in
            let request = self.buildSearchRequest(query: trimmedQuery, options: SearchOptions(
                caseSensitive: false,
                wholeWords: false,
                useRegex: false,
                includeContent: true,
                includeTags: true,
                includeFileName: true,
                includeNotes: true,
                maxResults: nil,
                sortBy: .relevance
            ))
            let items = try context.fetch(request)
            
            Logger.database.info("Search completed: '\(trimmedQuery)' returned \(items.count) results")
            return items
        }
        
        await MainActor.run {
            lastSearchQuery = trimmedQuery
            lastSearchResults = results
        }
        
        return results
    }
    
    func searchContent(query: String, in items: [ContentItem]) -> [ContentItem] {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return items
        }
        
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        
        return items.filter { item in
            // Search in title
            if let title = item.title?.lowercased(), title.contains(trimmedQuery) {
                return true
            }
            
            // Search in content
            if let content = item.content?.lowercased(), content.contains(trimmedQuery) {
                return true
            }
            
            // Search in notes
            if let notes = item.notes?.lowercased(), notes.contains(trimmedQuery) {
                return true
            }
            
            // Search in filename
            if let fileName = item.fileName?.lowercased(), fileName.contains(trimmedQuery) {
                return true
            }
            
            // Search in tags
            let tagNames = item.tagNames.map { $0.lowercased() }
            if tagNames.contains(where: { $0.contains(trimmedQuery) }) {
                return true
            }
            
            return false
        }
    }
    
    @MainActor
    private func buildSearchRequest(query: String, options: SearchOptions) -> NSFetchRequest<ContentItem> {
        let request: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
        
        var predicates: [NSPredicate] = []
        let searchQuery = options.caseSensitive ? query : query.lowercased()
        let comparison = options.caseSensitive ? "CONTAINS" : "CONTAINS[cd]"
        
        // Build search predicates for different fields
        if options.includeContent {
            predicates.append(NSPredicate(format: "title \(comparison) %@", searchQuery))
            predicates.append(NSPredicate(format: "content \(comparison) %@", searchQuery))
        }
        
        if options.includeNotes {
            predicates.append(NSPredicate(format: "notes \(comparison) %@", searchQuery))
        }
        
        if options.includeFileName {
            predicates.append(NSPredicate(format: "fileName \(comparison) %@", searchQuery))
        }
        
        if options.includeTags {
            predicates.append(NSPredicate(format: "ANY tags.name \(comparison) %@", searchQuery))
        }
        
        // Combine predicates with OR
        request.predicate = NSCompoundPredicate(orPredicateWithSubpredicates: predicates)
        
        // Apply sorting
        request.sortDescriptors = getSortDescriptors(for: options.sortBy)
        
        // Apply result limit
        if let maxResults = options.maxResults {
            request.fetchLimit = maxResults
        }
        
        return request
    }
    
    private func getSortDescriptors(for sortOption: SearchOptions.SortOption) -> [NSSortDescriptor] {
        switch sortOption {
        case .relevance:
            // For relevance, we'll sort by creation date for now
            // In a more advanced implementation, we could calculate relevance scores
            return [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)]
        case .dateCreated:
            return [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)]
        case .dateModified:
            return [NSSortDescriptor(keyPath: \ContentItem.updatedAt, ascending: false)]
        case .title:
            return [NSSortDescriptor(keyPath: \ContentItem.title, ascending: true)]
        case .size:
            return [NSSortDescriptor(keyPath: \ContentItem.fileSize, ascending: false)]
        case .type:
            return [
                NSSortDescriptor(keyPath: \ContentItem.contentType, ascending: true),
                NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)
            ]
        }
    }
    
    // MARK: - Filter Operations
    func filterByTags(_ tags: [Tag]) async throws -> [ContentItem] {
        let tagNames = tags.compactMap { $0.name }
        return try await filterByTags(tagNames)
    }
    
    func filterByTags(_ tagNames: [String]) async throws -> [ContentItem] {
        guard !tagNames.isEmpty else {
            return try await getAllContent()
        }
        
        return try await persistenceController.performBackgroundTask { context in
            let request: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            
            // Create predicates for each tag
            let tagPredicates = tagNames.map { tagName in
                NSPredicate(format: "ANY tags.name == %@", tagName)
            }
            
            // Combine with AND to find items that have ALL specified tags
            request.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: tagPredicates)
            request.sortDescriptors = [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)]
            
            return try context.fetch(request)
        }
    }
    
    func filterByType(_ type: ContentType) async throws -> [ContentItem] {
        return try await persistenceController.performBackgroundTask { context in
            let request = ContentItem.itemsByTypeFetchRequest(type)
            return try context.fetch(request)
        }
    }
    
    func filterByDateRange(from startDate: Date, to endDate: Date) async throws -> [ContentItem] {
        return try await persistenceController.performBackgroundTask { context in
            let request: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            request.predicate = NSPredicate(
                format: "createdAt >= %@ AND createdAt <= %@",
                startDate as NSDate,
                endDate as NSDate
            )
            request.sortDescriptors = [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)]
            
            return try context.fetch(request)
        }
    }
    
    func filterBySize(min: Int64?, max: Int64?) async throws -> [ContentItem] {
        return try await persistenceController.performBackgroundTask { context in
            let request: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            
            var predicates: [NSPredicate] = []
            
            if let minSize = min {
                predicates.append(NSPredicate(format: "fileSize >= %lld", minSize))
            }
            
            if let maxSize = max {
                predicates.append(NSPredicate(format: "fileSize <= %lld", maxSize))
            }
            
            if !predicates.isEmpty {
                request.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: predicates)
            }
            
            request.sortDescriptors = [NSSortDescriptor(keyPath: \ContentItem.fileSize, ascending: false)]
            
            return try context.fetch(request)
        }
    }
    
    func filterExpiredContent() async throws -> [ContentItem] {
        return try await persistenceController.performBackgroundTask { context in
            let request = ContentItem.expiredItemsFetchRequest()
            return try context.fetch(request)
        }
    }
    
    func filterPermanentContent() async throws -> [ContentItem] {
        return try await persistenceController.performBackgroundTask { context in
            let request: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            request.predicate = NSPredicate(format: "isPermanent == YES")
            request.sortDescriptors = [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)]
            
            return try context.fetch(request)
        }
    }
    
    // MARK: - Combined Search and Filter
    func searchAndFilter(
        query: String?,
        tags: [String]?,
        type: ContentType?,
        dateRange: DateRange?,
        sizeRange: SizeRange?,
        includeExpired: Bool = true
    ) async throws -> [ContentItem] {
        
        return try await persistenceController.performBackgroundTask { context in
            let request: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            var predicates: [NSPredicate] = []
            
            // Search query predicate
            if let query = query, !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                let searchQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
                let searchPredicates = [
                    NSPredicate(format: "title CONTAINS[cd] %@", searchQuery),
                    NSPredicate(format: "content CONTAINS[cd] %@", searchQuery),
                    NSPredicate(format: "notes CONTAINS[cd] %@", searchQuery),
                    NSPredicate(format: "fileName CONTAINS[cd] %@", searchQuery),
                    NSPredicate(format: "ANY tags.name CONTAINS[cd] %@", searchQuery)
                ]
                predicates.append(NSCompoundPredicate(orPredicateWithSubpredicates: searchPredicates))
            }
            
            // Tags filter
            if let tags = tags, !tags.isEmpty {
                let tagPredicates = tags.map { tagName in
                    NSPredicate(format: "ANY tags.name == %@", tagName)
                }
                predicates.append(NSCompoundPredicate(andPredicateWithSubpredicates: tagPredicates))
            }
            
            // Content type filter
            if let type = type {
                predicates.append(NSPredicate(format: "contentType == %@", type.rawValue))
            }
            
            // Date range filter
            if let dateRange = dateRange {
                predicates.append(NSPredicate(
                    format: "createdAt >= %@ AND createdAt <= %@",
                    dateRange.startDate as NSDate,
                    dateRange.endDate as NSDate
                ))
            }
            
            // Size range filter
            if let sizeRange = sizeRange {
                if let minSize = sizeRange.minSize {
                    predicates.append(NSPredicate(format: "fileSize >= %lld", minSize))
                }
                if let maxSize = sizeRange.maxSize {
                    predicates.append(NSPredicate(format: "fileSize <= %lld", maxSize))
                }
            }
            
            // Expired content filter
            if !includeExpired {
                predicates.append(NSPredicate(format: "expiresAt == nil OR expiresAt > %@", Date() as NSDate))
            }
            
            // Combine all predicates
            if !predicates.isEmpty {
                request.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: predicates)
            }
            
            request.sortDescriptors = [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)]
            
            return try context.fetch(request)
        }
    }
    
    // MARK: - Search Suggestions
    func getSearchSuggestions(for query: String) async throws -> [SearchSuggestion] {
        guard query.count >= 2 else { return [] }
        
        var suggestions: [SearchSuggestion] = []
        
        // Add recent searches that match
        let recentSearches = getRecentSearches()
        let matchingSearches = recentSearches.filter { $0.lowercased().contains(query.lowercased()) }
        suggestions.append(contentsOf: matchingSearches.map { 
            SearchSuggestion(text: $0, type: .query, count: nil)
        })
        
        // Add matching tags
        let matchingTags = try await getMatchingTags(for: query)
        suggestions.append(contentsOf: matchingTags.map {
            SearchSuggestion(text: $0.name ?? "", type: .tag, count: $0.contentItemsCount)
        })
        
        // Add matching file names
        let matchingFileNames = try await getMatchingFileNames(for: query)
        suggestions.append(contentsOf: matchingFileNames.map {
            SearchSuggestion(text: $0, type: .fileName, count: nil)
        })
        
        // Limit and sort suggestions
        return Array(suggestions.prefix(10))
    }
    
    private func getMatchingTags(for query: String) async throws -> [Tag] {
        return try await persistenceController.performBackgroundTask { context in
            let request: NSFetchRequest<Tag> = Tag.fetchRequest()
            request.predicate = NSPredicate(format: "name CONTAINS[cd] %@", query)
            request.sortDescriptors = [NSSortDescriptor(key: "contentItems.@count", ascending: false)]
            request.fetchLimit = 5

            return try context.fetch(request)
        }
    }
    
    private func getMatchingFileNames(for query: String) async throws -> [String] {
        return try await persistenceController.performBackgroundTask { context in
            let request: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            request.predicate = NSPredicate(format: "fileName CONTAINS[cd] %@ AND fileName != nil", query)
            request.propertiesToFetch = ["fileName"]
            request.returnsDistinctResults = true
            request.fetchLimit = 5
            
            let items = try context.fetch(request)
            return items.compactMap { $0.fileName }
        }
    }
    
    func getPopularTags(limit: Int = 10) async throws -> [Tag] {
        return try await persistenceController.performBackgroundTask { context in
            let request = Tag.popularTagsFetchRequest(limit: limit)
            return try context.fetch(request)
        }
    }
    
    // MARK: - Search History
    func getRecentSearches() -> [String] {
        return userDefaults.stringArray(forKey: searchHistoryKey) ?? []
    }
    
    func addToSearchHistory(_ query: String) {
        var history = getRecentSearches()
        
        // Remove if already exists
        history.removeAll { $0 == query }
        
        // Add to beginning
        history.insert(query, at: 0)
        
        // Limit size
        if history.count > maxSearchHistoryItems {
            history = Array(history.prefix(maxSearchHistoryItems))
        }
        
        userDefaults.set(history, forKey: searchHistoryKey)
    }
    
    func clearSearchHistory() {
        userDefaults.removeObject(forKey: searchHistoryKey)
    }
    
    // MARK: - Search Statistics
    func getSearchStatistics() async throws -> SearchStatistics {
        let recentSearches = getRecentSearches()
        let popularTags = try await getPopularTags(limit: 5)
        
        return try await persistenceController.performBackgroundTask { context in
            // Get search statistics by content type
            var searchesByType: [ContentType: Int] = [:]
            for contentType in ContentType.allCases {
                let request = ContentItem.itemsByTypeFetchRequest(contentType)
                let count = try context.count(for: request)
                searchesByType[contentType] = count
            }
            
            return SearchStatistics(
                totalSearches: recentSearches.count,
                popularQueries: Array(recentSearches.prefix(5)),
                popularTags: popularTags.compactMap { $0.name },
                searchesByType: searchesByType,
                averageResultsPerSearch: 0.0, // Would need to track this separately
                lastSearchDate: recentSearches.isEmpty ? nil : Date()
            )
        }
    }
    
    // MARK: - Helper Methods
    private func getAllContent() async throws -> [ContentItem] {
        return try await persistenceController.performBackgroundTask { context in
            let request = ContentItem.allItemsFetchRequest()
            return try context.fetch(request)
        }
    }
    
    // MARK: - Advanced Search Features
    @MainActor
    func searchWithHighlights(query: String, options: SearchOptions) async throws -> [SearchResult] {
        let items = try await searchContent(query: query)
        
        return items.map { item in
            let highlights = generateHighlights(for: item, query: query, options: options)
            let relevanceScore = calculateRelevanceScore(for: item, query: query, highlights: highlights)
            
            return SearchResult(
                item: item,
                highlights: highlights,
                relevanceScore: relevanceScore
            )
        }.sorted { $0.relevanceScore > $1.relevanceScore }
    }
    
    private func generateHighlights(for item: ContentItem, query: String, options: SearchOptions) -> [SearchHighlight] {
        var highlights: [SearchHighlight] = []
        let searchQuery = options.caseSensitive ? query : query.lowercased()
        
        // Title highlights
        if let title = item.title {
            let searchText = options.caseSensitive ? title : title.lowercased()
            if let range = searchText.range(of: searchQuery) {
                let nsRange = NSRange(range, in: searchText)
                highlights.append(SearchHighlight(
                    field: .title,
                    ranges: [nsRange],
                    snippet: title
                ))
            }
        }
        
        // Content highlights
        if let content = item.content {
            let searchText = options.caseSensitive ? content : content.lowercased()
            let ranges = findAllRanges(of: searchQuery, in: searchText)
            if !ranges.isEmpty {
                let snippet = generateSnippet(from: content, ranges: ranges, query: query)
                highlights.append(SearchHighlight(
                    field: .content,
                    ranges: ranges,
                    snippet: snippet
                ))
            }
        }
        
        return highlights
    }
    
    private func findAllRanges(of query: String, in text: String) -> [NSRange] {
        var ranges: [NSRange] = []
        var searchRange = NSRange(location: 0, length: text.count)
        
        while searchRange.location < text.count {
            let foundRange = (text as NSString).range(of: query, options: [], range: searchRange)
            if foundRange.location == NSNotFound {
                break
            }
            
            ranges.append(foundRange)
            searchRange.location = foundRange.location + foundRange.length
            searchRange.length = text.count - searchRange.location
        }
        
        return ranges
    }
    
    private func generateSnippet(from text: String, ranges: [NSRange], query: String) -> String {
        guard let firstRange = ranges.first else { return "" }
        
        let snippetLength = 100
        let start = max(0, firstRange.location - snippetLength / 2)
        let end = min(text.count, start + snippetLength)
        
        let startIndex = text.index(text.startIndex, offsetBy: start)
        let endIndex = text.index(text.startIndex, offsetBy: end)
        
        var snippet = String(text[startIndex..<endIndex])
        
        if start > 0 {
            snippet = "..." + snippet
        }
        if end < text.count {
            snippet = snippet + "..."
        }
        
        return snippet
    }
    
    private func calculateRelevanceScore(for item: ContentItem, query: String, highlights: [SearchHighlight]) -> Double {
        var score = 0.0
        
        // Base score from number of matches
        score += Double(highlights.reduce(0) { $0 + $1.ranges.count })
        
        // Boost for title matches
        if highlights.contains(where: { $0.field == .title }) {
            score += 10.0
        }
        
        // Boost for exact matches
        let queryLower = query.lowercased()
        if item.title?.lowercased() == queryLower {
            score += 20.0
        }
        
        // Boost for recent items
        let daysSinceCreation = Date().timeIntervalSince(item.createdAt ?? Date.distantPast) / (24 * 60 * 60)
        score += max(0, 10.0 - daysSinceCreation)
        
        return score
    }
}