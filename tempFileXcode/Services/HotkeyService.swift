import Foundation
import SwiftUI
import AppKit
import Combine
import os.log

// MARK: - Hotkey Service
class HotkeyService: ObservableObject {

    // MARK: - Properties
    @Published var isEnabled = false
    @Published var currentHotkey: String = "⌘⇧V"

    private var hotkeyCallback: (() -> Void)?
    private var eventMonitor: Any?
    private let logger = Logger(subsystem: "com.contentmanager.app", category: "HotkeyService")

    // MARK: - Initialization
    init() {
        setupDefaultHotkey()
        requestAccessibilityPermissions()
    }

    // MARK: - Setup
    private func setupDefaultHotkey() {
        logger.info("Hotkey service initialized with shortcut: \(self.currentHotkey)")
    }

    private func requestAccessibilityPermissions() {
        // 检查辅助功能权限
        let checkOptPrompt = kAXTrustedCheckOptionPrompt.takeUnretainedValue() as NSString
        let options = [checkOptPrompt: true] // 允许弹出权限对话框
        let accessEnabled = AXIsProcessTrustedWithOptions(options as CFDictionary)

        DispatchQueue.main.async {
            if accessEnabled {
                self.logger.info("Accessibility permissions granted")
                self.isEnabled = true
                self.setupGlobalHotkey()
            } else {
                self.logger.warning("Accessibility permissions not granted")
                self.isEnabled = false
                // 提示用户开启权限
                self.showPermissionAlert()
            }
        }
    }

    private func checkAccessibilityPermissions() -> Bool {
        return AXIsProcessTrusted()
    }

    // MARK: - Hotkey Setup
    private func setupGlobalHotkey() {
        // 检查权限
        guard checkAccessibilityPermissions() else {
            logger.warning("Cannot setup hotkey: accessibility permissions not granted")
            return
        }

        // 使用NSEvent监听全局按键
        logger.info("Setting up NSEvent monitor for hotkey")
        setupNSEventMonitor()
    }

    private func setupNSEventMonitor() {
        logger.info("Setting up NSEvent monitor")

        // 移除现有的监听器
        if let monitor = eventMonitor {
            NSEvent.removeMonitor(monitor)
        }

        // 创建新的全局按键监听器
        eventMonitor = NSEvent.addGlobalMonitorForEvents(matching: .keyDown) { [weak self] event in
            guard let self = self else { return }

            // 检查是否是我们的热键组合 (Cmd+Shift+V)
            let modifiers = event.modifierFlags
            let keyCode = event.keyCode

            let hasCmd = modifiers.contains(.command)
            let hasShift = modifiers.contains(.shift)

            // V key = 9, D key = 2 (备用)
            if hasCmd && hasShift && (keyCode == 9 || keyCode == 2) {
                Task { @MainActor in
                    self.handleHotkeyPressed()
                }
            }
        }

        if eventMonitor != nil {
            logger.info("NSEvent monitor setup successfully")
        } else {
            logger.error("Failed to setup NSEvent monitor")
        }
    }

    // MARK: - Hotkey Handling
    private func handleHotkeyPressed() {
        logger.info("Hotkey pressed, executing callback")
        hotkeyCallback?()
    }

    // MARK: - Public Methods
    func setHotkeyCallback(_ callback: @escaping () -> Void) {
        hotkeyCallback = callback
        logger.info("Hotkey callback set")
    }

    func enableHotkey() {
        guard !isEnabled else { return }

        if checkAccessibilityPermissions() {
            setupGlobalHotkey()
            isEnabled = true
            logger.info("Accessibility permissions granted, hotkey enabled")
        } else {
            logger.warning("Cannot enable hotkey: accessibility permissions not granted")
        }
    }

    func disableHotkey() {
        if let monitor = eventMonitor {
            NSEvent.removeMonitor(monitor)
            eventMonitor = nil
        }

        isEnabled = false
        logger.info("Hotkey disabled")
    }

    func updateHotkey(_ newHotkey: String) {
        self.currentHotkey = newHotkey
        if self.isEnabled {
            // 重新设置热键
            Task { @MainActor in
                disableHotkey()
                enableHotkey()
            }
        }
        logger.info("Hotkey updated to: \(newHotkey)")
    }

    // MARK: - Cleanup
    deinit {
        // 在deinit中不能调用MainActor方法，所以直接清理
        if let monitor = eventMonitor {
            NSEvent.removeMonitor(monitor)
        }
    }
}

// MARK: - Accessibility Extensions
extension HotkeyService {

    func checkAndRequestPermissions() -> Bool {
        let checkOptPrompt = kAXTrustedCheckOptionPrompt.takeUnretainedValue() as NSString
        let options = [checkOptPrompt: true]
        return AXIsProcessTrustedWithOptions(options as CFDictionary)
    }

    func openSystemPreferences() {
        let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")!
        NSWorkspace.shared.open(url)
    }
    
    private func showPermissionAlert() {
        let alert = NSAlert()
        alert.messageText = "需要辅助功能权限"
        alert.informativeText = "为了使用全局快捷键功能，请在系统偏好设置中授予此应用辅助功能权限。"
        alert.addButton(withTitle: "打开系统偏好设置")
        alert.addButton(withTitle: "稍后")
        
        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            openSystemPreferences()
        }
    }
}

// MARK: - Preview
#if DEBUG
struct HotkeyServicePreview: View {
    var body: some View {
        Text("Hotkey Service Preview")
    }
}

#Preview {
    HotkeyServicePreview()
}
#endif