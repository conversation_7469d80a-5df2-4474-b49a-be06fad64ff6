import Foundation
import AppKit
import UniformTypeIdentifiers
import Combine
import os.log

// MARK: - Pasteboard Monitor
@MainActor
class PasteboardMonitor: ObservableObject {
    
    // MARK: - Properties
    @Published var isMonitoring = false
    @Published var lastDetectedContent: PasteboardContent?
    
    private let pasteboard = NSPasteboard.general
    private var monitoringTimer: Timer?
    private var lastChangeCount: Int = 0
    
    // MARK: - Callbacks
    var onContentDetected: ((PasteboardContent) -> Void)?
    
    // MARK: - Initialization
    init() {
        lastChangeCount = pasteboard.changeCount
    }
    
    deinit {
        Task { [weak self] in
            await self?.stopMonitoring()
        }
    }
    
    // MARK: - Monitoring Control
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        lastChangeCount = pasteboard.changeCount
        
        // Start timer to check pasteboard changes
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.checkPasteboardChanges()
            }
        }
        
        Logger.pasteboard.info("Started pasteboard monitoring")
    }
    
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        Logger.pasteboard.info("Stopped pasteboard monitoring")
    }
    
    // MARK: - Pasteboard Change Detection
    private func checkPasteboardChanges() {
        let currentChangeCount = pasteboard.changeCount
        
        if currentChangeCount != lastChangeCount {
            lastChangeCount = currentChangeCount
            
            do {
                if let content = try detectPasteboardContent() {
                    lastDetectedContent = content
                    onContentDetected?(content)
                    Logger.pasteboard.info("Detected new pasteboard content: \(String(describing: content.type))")
                }
            } catch {
                Logger.pasteboard.error("Failed to detect pasteboard content: \(error)")
            }
        }
    }
    
    // MARK: - Content Detection（智能版）
    func detectPasteboardContent() throws -> PasteboardContent? {
        guard !(pasteboard.pasteboardItems?.isEmpty ?? true) else {
            print("Pasteboard is empty")
            throw ContentManagerError.pasteboardEmpty
        }
        
        print("Starting smart pasteboard content detection...")
        
        // 🚫 首先检查是否是文件复制操作，如果是则忽略
        if isFileCopyOperation() {
            print("🚫 检测到文件复制操作，忽略以避免弹窗")
            return nil
        }
        
        // ✅ 只处理用户主动复制的内容（文本/图片）
        
        // 1. 检查图片内容
        if let imageContent = try detectImageContent() {
            print("✅ 检测到图片内容")
            return imageContent
        }
        
        // 2. 检查文本内容（排除文件路径）
        if let textContent = try detectTextContent() {
            print("✅ 检测到文本内容，验证是否为有效文本...")
            if isValidUserText(textContent.text) {
                print("✅ 有效的用户文本内容")
                return textContent
            } else {
                print("🚫 过滤掉无效的文本内容")
            }
        }
        
        // 3. 检查富文本内容
        if let richTextContent = try detectRichTextContent() {
            print("✅ 检测到富文本内容")
            return richTextContent
        }
        
        print("ℹ️ 未检测到需要处理的内容")
        return nil
    }
    
    /// 检测是否是文件复制操作
    private func isFileCopyOperation() -> Bool {
        // 检查是否包含文件URL类型
        guard pasteboard.canReadItem(withDataConformingToTypes: [UTType.fileURL.identifier]) else {
            return false
        }
        
        // 尝试读取文件URLs
        if let fileURLs = pasteboard.readObjects(forClasses: [NSURL.self], options: nil) as? [URL],
           !fileURLs.isEmpty {
            let validFiles = fileURLs.filter { $0.isFileURL && FileManager.default.fileExists(atPath: $0.path) }
            if !validFiles.isEmpty {
                print("🚫 检测到 \(validFiles.count) 个文件被复制: \(validFiles.map(\.lastPathComponent))")
                return true
            }
        }
        
        return false
    }
    
    /// 验证是否为有效的用户文本
    private func isValidUserText(_ text: String?) -> Bool {
        guard let text = text?.trimmingCharacters(in: .whitespacesAndNewlines), !text.isEmpty else {
            return false
        }
        
        // 过滤条件：
        
        // 1. 文本长度合理（不要太短或太长）
        guard text.count >= 3 && text.count <= 10000 else {
            print("🚫 文本长度不合理: \(text.count) 字符")
            return false
        }
        
        // 2. 不是文件路径
        if isFilePathText(text) {
            print("🚫 文本是文件路径: \(text.prefix(50))...")
            return false
        }
        
        // 3. 不是URL
        if text.hasPrefix("http://") || text.hasPrefix("https://") || text.hasPrefix("file://") {
            print("🚫 文本是URL: \(text.prefix(50))...")
            return false
        }
        
        // 4. 不是系统内部文本
        let systemPrefixes = ["/System/", "/usr/", "/Library/", "/private/var/", "com.apple.", "CFBundle"]
        if systemPrefixes.contains(where: text.hasPrefix) {
            print("🚫 文本是系统内部内容: \(text.prefix(50))...")
            return false
        }
        
        // 5. 不是纯数字或特殊字符
        let alphanumericSet = CharacterSet.alphanumerics.union(.whitespaces).union(.punctuationCharacters)
        if text.unicodeScalars.allSatisfy({ CharacterSet.decimalDigits.contains($0) }) {
            print("🚫 文本是纯数字: \(text)")
            return false
        }
        
        print("✅ 有效的用户文本: \(text.prefix(50))...")
        return true
    }
    
    // MARK: - Helper method to detect file paths
    private func isFilePathText(_ text: String?) -> Bool {
        guard let text = text else { return false }
        
        // 如果文本太短，不可能是文件路径
        guard text.count > 25 else { return false }
        
        // 检查是否是单一的完整路径
        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
        let lines = trimmedText.components(separatedBy: .newlines)
        
        // 多行文本不是单个文件路径
        if lines.count > 2 {
            return false
        }
        
        // 必须是绝对路径或file://协议开头
        guard trimmedText.hasPrefix("/") || trimmedText.hasPrefix("file://") else {
            return false
        }
        
        // 检查是否整个文本就是路径（没有其他内容）
        let isOnlyPath = trimmedText == text.trimmingCharacters(in: .whitespacesAndNewlines) && lines.count == 1
        guard isOnlyPath else { return false }
        
        // 路径必须有足够的层级
        let pathComponents = trimmedText.components(separatedBy: "/")
        guard pathComponents.count >= 6 else { return false }
        
        // 检查是否包含明显的系统路径特征
        let hasSystemPath = trimmedText.contains("/Users/") || 
                           trimmedText.contains("/Applications/") || 
                           trimmedText.contains("/System/") ||
                           trimmedText.contains("/Library/")
        
        let hasDevPath = trimmedText.contains("/DerivedData/") ||
                        trimmedText.contains("/Build/") ||
                        trimmedText.contains("/Xcode/")
        
        let hasSpecificExtension = trimmedText.contains(".app/") ||
                                  trimmedText.contains(".xcodeproj/") ||
                                  trimmedText.contains(".build/")
        
        // 只有在明显是开发相关的完整路径时才过滤
        let isClearlyBuildPath = hasSystemPath && hasDevPath && (hasSpecificExtension || pathComponents.count > 10)
        
        // 超长的开发路径
        let isVeryLongDevPath = trimmedText.count > 120 && pathComponents.count > 8 && hasDevPath
        
        let result = isClearlyBuildPath || isVeryLongDevPath
        if result {
            print("Filtered development path: length=\(trimmedText.count), components=\(pathComponents.count)")
        } else {
            print("Allowed text (not filtered): \(trimmedText.prefix(50))...")
        }
        
        return result
    }
    
    // MARK: - Image Content Detection
    private func detectImageContent() throws -> PasteboardContent? {
        // Try different image types
        let imageTypes: [NSPasteboard.PasteboardType] = [
            .png, .tiff, .pdf
        ]
        
        for imageType in imageTypes {
            if let imageData = pasteboard.data(forType: imageType) {
                let fileName = generateImageFileName(for: imageType)
                
                return PasteboardContent(
                    type: .image,
                    data: imageData,
                    text: nil,
                    fileName: fileName,
                    mimeType: mimeTypeForPasteboardType(imageType),
                    fileSize: Int64(imageData.count)
                )
            }
        }
        
        return nil
    }
    
    // MARK: - File Content Detection
    private func detectFileContent() throws -> PasteboardContent? {
        if let fileURLs = pasteboard.readObjects(forClasses: [NSURL.self]) as? [URL] {
            // Handle multiple files - for now, take the first one
            guard let fileURL = fileURLs.first else { return nil }
            
            // Check if file exists and is readable
            guard FileManager.default.fileExists(atPath: fileURL.path) else {
                throw ContentManagerError.fileNotFound(fileURL.path)
            }
            
            do {
                let fileData = try Data(contentsOf: fileURL)
                let contentType = ContentType.detectType(from: fileURL)
                
                return PasteboardContent(
                    type: contentType,
                    data: fileData,
                    text: contentType == .text ? String(data: fileData, encoding: .utf8) : nil,
                    fileName: fileURL.lastPathComponent,
                    mimeType: mimeTypeForURL(fileURL),
                    fileSize: Int64(fileData.count),
                    sourceURL: fileURL
                )
            } catch {
                throw ContentManagerError.fileOperationError("Failed to read file: \(error.localizedDescription)")
            }
        }
        
        return nil
    }
    
    // MARK: - Text Content Detection
    private func detectTextContent() throws -> PasteboardContent? {
        if let text = pasteboard.string(forType: .string), !text.isEmpty {
            // 性能优化：对于很长的文本，只检查前后部分来判断是否为空
            let isEmpty: Bool
            if text.count > 1000 {
                // 对于长文本，只检查前100和后100字符
                let prefix = String(text.prefix(100))
                let suffix = String(text.suffix(100))
                isEmpty = (prefix + suffix).trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            } else {
                isEmpty = text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            }
            
            guard !isEmpty else {
                return nil
            }
            
            // 性能优化：对于超长文本，延迟生成 Data
            let textData: Data?
            let fileSize: Int64
            
            if text.count > 10000 {
                // 对于超长文本，使用更高效的方式计算字节数
                fileSize = Int64(text.utf8.count)
                textData = nil // 延迟生成，在真正需要时才转换
            } else {
                textData = text.data(using: .utf8)
                fileSize = Int64(textData?.count ?? 0)
            }
            
            // 性能优化：只打印前50字符的日志
            let logText = text.count > 50 ? String(text.prefix(50)) + "..." : text
            print("Detected text content: '\(logText)' (length: \(text.count))")
            
            return PasteboardContent(
                type: .text,
                data: textData,
                text: text,
                fileName: nil,
                mimeType: "text/plain",
                fileSize: fileSize
            )
        }
        
        return nil
    }
    
    // MARK: - Rich Text Content Detection
    private func detectRichTextContent() throws -> PasteboardContent? {
        if let rtfData = pasteboard.data(forType: .rtf) {
            // Convert RTF to plain text
            let attributedString = try NSAttributedString(
                data: rtfData,
                options: [.documentType: NSAttributedString.DocumentType.rtf],
                documentAttributes: nil
            )
            
            let plainText = attributedString.string
            let textData = plainText.data(using: .utf8) ?? Data()
            
            return PasteboardContent(
                type: .text,
                data: textData,
                text: plainText,
                fileName: nil,
                mimeType: "text/plain",
                fileSize: Int64(textData.count)
            )
        }
        
        return nil
    }
    
    // MARK: - Utility Methods
    private func generateImageFileName(for type: NSPasteboard.PasteboardType) -> String {
        let timestamp = Int(Date().timeIntervalSince1970)
        let fileExtension: String
        
        switch type {
        case .png:
            fileExtension = "png"
        case .tiff:
            fileExtension = "tiff"
        case .pdf:
            fileExtension = "pdf"
        default:
            fileExtension = "png"
        }
        
        return "clipboard_image_\(timestamp).\(fileExtension)"
    }
    
    private func mimeTypeForPasteboardType(_ type: NSPasteboard.PasteboardType) -> String {
        switch type {
        case .png:
            return "image/png"
        case .tiff:
            return "image/tiff"
        case .pdf:
            return "application/pdf"
        case .string:
            return "text/plain"
        case .rtf:
            return "text/rtf"
        default:
            return "application/octet-stream"
        }
    }
    
    private func mimeTypeForURL(_ url: URL) -> String {
        if #available(macOS 11.0, *) {
            if let utType = UTType(filenameExtension: url.pathExtension) {
                return utType.preferredMIMEType ?? "application/octet-stream"
            }
        }
        
        // Fallback for older systems
        let pathExtension = url.pathExtension.lowercased()
        switch pathExtension {
        case "txt", "text":
            return "text/plain"
        case "png":
            return "image/png"
        case "jpg", "jpeg":
            return "image/jpeg"
        case "gif":
            return "image/gif"
        case "pdf":
            return "application/pdf"
        case "html", "htm":
            return "text/html"
        case "json":
            return "application/json"
        case "xml":
            return "application/xml"
        default:
            return "application/octet-stream"
        }
    }
    
    // MARK: - Manual Content Retrieval
    func getCurrentPasteboardContent() throws -> PasteboardContent? {
        return try detectPasteboardContent()
    }
    
    func clearPasteboard() {
        pasteboard.clearContents()
        lastChangeCount = pasteboard.changeCount
        Logger.pasteboard.info("Cleared pasteboard")
    }
    
    // MARK: - Content Validation
    func validatePasteboardContent(_ content: PasteboardContent) -> Bool {
        // Basic validation
        guard content.fileSize > 0 else { return false }
        
        switch content.type {
        case .text:
            return content.text != nil && !content.text!.isEmpty
        case .image, .file:
            return content.data != nil && !content.data!.isEmpty
        }
    }
    
    // MARK: - Supported Types Check
    func getSupportedPasteboardTypes() -> [NSPasteboard.PasteboardType] {
        return [
            .string,
            .rtf,
            .png,
            .tiff,
            .pdf,
            .fileURL
        ]
    }
    
    func hasSupportedContent() -> Bool {
        let supportedTypes = getSupportedPasteboardTypes()
        return supportedTypes.contains { pasteboard.canReadItem(withDataConformingToTypes: [$0.rawValue]) }
    }
}

// MARK: - Pasteboard Content Model
struct PasteboardContent {
    let type: ContentType
    let data: Data?
    let text: String?
    let fileName: String?
    let mimeType: String
    let fileSize: Int64
    let sourceURL: URL?
    let detectedAt: Date
    
    init(
        type: ContentType,
        data: Data?,
        text: String?,
        fileName: String?,
        mimeType: String,
        fileSize: Int64,
        sourceURL: URL? = nil
    ) {
        self.type = type
        self.data = data
        self.text = text
        self.fileName = fileName
        self.mimeType = mimeType
        self.fileSize = fileSize
        self.sourceURL = sourceURL
        self.detectedAt = Date()
    }
    
    // MARK: - Computed Properties
    var displayTitle: String {
        if let fileName = fileName {
            return fileName
        }
        
        if let text = text, !text.isEmpty {
            return String(text.prefix(50))
        }
        
        return "Clipboard \(type.displayName)"
    }
    
    var formattedFileSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useBytes, .useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
    
    // MARK: - Conversion to ContentData
    func toContentData() -> ContentData {
        // 性能优化：对于长文本，延迟生成 data
        let actualData: Data?
        if type == .text && data == nil && text != nil {
            // 如果是文本但没有 data，需要时才生成
            actualData = text?.data(using: .utf8)
        } else {
            actualData = data
        }
        
        return ContentData(
            title: displayTitle,
            content: text,
            contentType: type,
            data: actualData,
            fileName: fileName,
            fileSize: fileSize,
            mimeType: mimeType
        )
    }
}

// MARK: - Drag and Drop Support
extension PasteboardMonitor {
    
    func handleDroppedFiles(_ urls: [URL]) -> [PasteboardContent] {
        var contents: [PasteboardContent] = []
        
        for url in urls {
            do {
                let fileData = try Data(contentsOf: url)
                let contentType = ContentType.detectType(from: url)
                
                let content = PasteboardContent(
                    type: contentType,
                    data: fileData,
                    text: contentType == .text ? String(data: fileData, encoding: .utf8) : nil,
                    fileName: url.lastPathComponent,
                    mimeType: mimeTypeForURL(url),
                    fileSize: Int64(fileData.count),
                    sourceURL: url
                )
                
                contents.append(content)
                Logger.pasteboard.info("Processed dropped file: \(url.lastPathComponent)")
                
            } catch {
                Logger.pasteboard.error("Failed to process dropped file \(url.path): \(error)")
            }
        }
        
        return contents
    }
    
    func handleDroppedText(_ text: String) -> PasteboardContent {
        // 性能优化：对于长文本延迟生成 Data
        let textData: Data?
        let fileSize: Int64
        
        if text.count > 10000 {
            fileSize = Int64(text.utf8.count)
            textData = nil
        } else {
            textData = text.data(using: .utf8)
            fileSize = Int64(textData?.count ?? 0)
        }
        
        return PasteboardContent(
            type: .text,
            data: textData,
            text: text,
            fileName: nil,
            mimeType: "text/plain",
            fileSize: fileSize
        )
    }
    
    func handleDroppedImage(_ image: NSImage) -> PasteboardContent? {
        guard let tiffData = image.tiffRepresentation,
              let bitmapRep = NSBitmapImageRep(data: tiffData),
              let pngData = bitmapRep.representation(using: .png, properties: [:]) else {
            return nil
        }
        
        let fileName = "dropped_image_\(Int(Date().timeIntervalSince1970)).png"
        
        return PasteboardContent(
            type: .image,
            data: pngData,
            text: nil,
            fileName: fileName,
            mimeType: "image/png",
            fileSize: Int64(pngData.count)
        )
    }
}