import Foundation
import CoreData
import UniformTypeIdentifiers
import Combine
import os.log

// MARK: - Content Service Implementation
class ContentService: ContentServiceProtocol, ObservableObject {
    
    // MARK: - Properties
    private let persistenceController: PersistenceController
    private let fileManager: FileManager
    private let applicationSupportURL: URL
    private let filesDirectoryURL: URL
    
    // MARK: - Constants
    private static let maxFileSize: Int64 = 100 * 1024 * 1024 // 100MB
    private static let maxTextLength = 1_000_000 // 1M characters
    
    // MARK: - Initialization
    init(persistenceController: PersistenceController? = nil) {
        self.persistenceController = persistenceController ?? PersistenceController.shared
        self.fileManager = FileManager.default
        
        // Setup application support directory
        let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        self.applicationSupportURL = appSupportURL.appendingPathComponent("TempBox")
        self.filesDirectoryURL = applicationSupportURL.appendingPathComponent("Files")
        
        // Create directories if they don't exist
        createDirectoriesIfNeeded()
    }
    
    // MARK: - Directory Setup
    private func createDirectoriesIfNeeded() {
        let directories = [
            applicationSupportURL,
            filesDirectoryURL,
            filesDirectoryURL.appendingPathComponent("Images"),
            filesDirectoryURL.appendingPathComponent("Documents"),
            filesDirectoryURL.appendingPathComponent("Temp")
        ]
        
        for directory in directories {
            do {
                try fileManager.createDirectory(at: directory, withIntermediateDirectories: true)
            } catch {
                Logger.fileOperations.error("Failed to create directory \(directory.path): \(error)")
            }
        }
    }
    
    // MARK: - Basic CRUD Operations
    func addContent(_ content: ContentData) async throws -> ContentItem {
        return try await addContent(content, toBatch: nil)
    }

    func addContent(_ content: ContentData, toBatch batch: NSManagedObject?) async throws -> ContentItem {
        // 快速验证（不做深度验证避免阻塞）
        try validateContentQuickly(content)

        return try await persistenceController.performBackgroundTask { context in
            let contentItem = ContentItem(from: content, context: context)

            // Associate with batch if provided
            if let batch = batch {
                // Get the batch in this context
                let contextBatch = context.object(with: batch.objectID)
                contentItem.setValue(contextBatch, forKey: "batch")
            }

            // 性能优化：对于长文本，使用文件存储而非数据库存储
            if content.contentType == .text && content.content != nil {
                try self.handleTextContent(content, for: contentItem)
            } else if let data = content.data {
                let filePath = try self.saveFileData(data, for: contentItem)
                contentItem.filePath = filePath
            }

            try context.save()
            Logger.database.info("Added new content item: \(contentItem.id?.uuidString ?? "unknown")")

            return contentItem
        }
    }
    
    func updateContent(_ item: ContentItem, with data: ContentData) async throws {
        try validateContent(data)
        
        try await persistenceController.performBackgroundTask { context in
            // Fetch the item in this context
            guard let contextItem = context.object(with: item.objectID) as? ContentItem else {
                throw ContentManagerError.databaseError("Content item not found in context")
            }
            
            // Update the item
            contextItem.updateFrom(data)
            
            // Update file data if present
            if let newData = data.data {
                // Delete old file if exists
                if let oldPath = contextItem.filePath {
                    try? self.fileManager.removeItem(atPath: oldPath)
                }
                
                // Save new file
                let filePath = try self.saveFileData(newData, for: contextItem)
                contextItem.filePath = filePath
            }
            
            try context.save()
            Logger.database.info("Updated content item: \(contextItem.id?.uuidString ?? "unknown")")
        }
    }
    
    func deleteContent(_ item: ContentItem) async throws {
        try await persistenceController.performBackgroundTask { context in
            // Fetch the item in this context
            guard let contextItem = context.object(with: item.objectID) as? ContentItem else {
                throw ContentManagerError.databaseError("Content item not found in context")
            }
            
            // Delete associated file
            if let filePath = contextItem.filePath {
                try? self.fileManager.removeItem(atPath: filePath)
            }
            
            // Delete the item
            context.delete(contextItem)
            try context.save()
            
            Logger.database.info("Deleted content item: \(item.id?.uuidString ?? "unknown")")
        }
    }
    
    func getAllContent() async throws -> [ContentItem] {
        return try await persistenceController.performBackgroundTask { context in
            let request = ContentItem.allItemsFetchRequest()
            return try context.fetch(request)
        }
    }
    
    func getContentByType(_ type: ContentType) async throws -> [ContentItem] {
        return try await persistenceController.performBackgroundTask { context in
            let request = ContentItem.itemsByTypeFetchRequest(type)
            return try context.fetch(request)
        }
    }
    
    func getContentForBatch(_ batch: NSManagedObject) async throws -> [ContentItem] {
        return try await persistenceController.performBackgroundTask { context in
            let request = NSFetchRequest<ContentItem>(entityName: "ContentItem")
            request.predicate = NSPredicate(format: "batch == %@", batch)
            request.sortDescriptors = [NSSortDescriptor(key: "createdAt", ascending: false)]
            return try context.fetch(request)
        }
    }
    
    // MARK: - File Management
    func saveContentFile(_ data: Data, for item: ContentItem) async throws -> String {
        return try saveFileData(data, for: item)
    }
    
    private func saveFileData(_ data: Data, for item: ContentItem) throws -> String {
        let contentType = item.contentTypeEnum
        let subdirectory: String
        let fileExtension: String
        
        switch contentType {
        case .image:
            subdirectory = "Images"
            fileExtension = "png"
        case .text:
            subdirectory = "Documents"
            fileExtension = "txt"
        case .file:
            subdirectory = "Documents"
            fileExtension = item.fileName?.components(separatedBy: ".").last ?? "dat"
        }
        
        let directoryURL = filesDirectoryURL.appendingPathComponent(subdirectory)
        let fileName = "\(item.id?.uuidString ?? UUID().uuidString).\(fileExtension)"
        let fileURL = directoryURL.appendingPathComponent(fileName)
        
        try data.write(to: fileURL)
        Logger.fileOperations.info("Saved file: \(fileURL.path)")
        
        return fileURL.path
    }
    
    func loadContentFile(for item: ContentItem) async throws -> Data? {
        guard let filePath = item.filePath else { return nil }
        
        let fileURL = URL(fileURLWithPath: filePath)
        guard fileManager.fileExists(atPath: filePath) else {
            Logger.fileOperations.warning("File not found: \(filePath)")
            return nil
        }
        
        return try Data(contentsOf: fileURL)
    }
    
    func deleteContentFile(for item: ContentItem) async throws {
        guard let filePath = item.filePath else { return }
        
        if fileManager.fileExists(atPath: filePath) {
            try fileManager.removeItem(atPath: filePath)
            Logger.fileOperations.info("Deleted file: \(filePath)")
        }
    }
    
    // MARK: - Content Type Detection
    func detectContentType(from data: Data) -> ContentType {
        return ContentType.detectType(from: data)
    }
    
    func detectContentType(from url: URL) -> ContentType {
        return ContentType.detectType(from: url)
    }
    
    func detectContentType(from string: String) -> ContentType {
        return ContentType.detectType(from: string)
    }
    
    // MARK: - Cleanup Operations
    func cleanupExpiredContent() async throws {
        try await persistenceController.cleanupExpiredContent()
        Logger.database.info("Expired content cleanup completed")
    }
    
    func cleanupOrphanedFiles() async throws {
        let allFiles = try getAllFilesInDirectory()
        let activeFilePaths = try await getAllActiveFilePaths()
        
        var orphanedCount = 0
        for fileURL in allFiles {
            let filePath = fileURL.path
            if !activeFilePaths.contains(filePath) {
                try fileManager.removeItem(at: fileURL)
                orphanedCount += 1
                Logger.fileOperations.info("Deleted orphaned file: \(filePath)")
            }
        }
        
        Logger.fileOperations.info("Orphaned files cleanup completed, deleted \(orphanedCount) files")
    }
    
    private func getAllFilesInDirectory() throws -> [URL] {
        let subdirectories = ["Images", "Documents", "Temp"]
        var allFiles: [URL] = []
        
        for subdirectory in subdirectories {
            let directoryURL = filesDirectoryURL.appendingPathComponent(subdirectory)
            if fileManager.fileExists(atPath: directoryURL.path) {
                let files = try fileManager.contentsOfDirectory(
                    at: directoryURL,
                    includingPropertiesForKeys: nil,
                    options: .skipsHiddenFiles
                )
                allFiles.append(contentsOf: files)
            }
        }
        
        return allFiles
    }
    
    private func getAllActiveFilePaths() async throws -> Set<String> {
        return try await persistenceController.performBackgroundTask { context in
            let request: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            request.predicate = NSPredicate(format: "filePath != nil")
            
            let items = try context.fetch(request)
            return Set(items.compactMap { $0.filePath })
        }
    }
    
    func getStorageStatistics() async throws -> StorageStatistics {
        // Count orphaned files outside the background task since it needs async
        let allFiles = try getAllFilesInDirectory()
        let activeFilePaths = try await getAllActiveFilePaths()
        let orphanedFiles = allFiles.filter { !activeFilePaths.contains($0.path) }.count

        return try await persistenceController.performBackgroundTask { context in
            let allRequest: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
            let allItems = try context.fetch(allRequest)

            let textItems = allItems.filter { $0.contentTypeEnum == .text }.count
            let imageItems = allItems.filter { $0.contentTypeEnum == .image }.count
            let fileItems = allItems.filter { $0.contentTypeEnum == .file }.count

            let expiredRequest = ContentItem.expiredItemsFetchRequest()
            let expiredItems = try context.count(for: expiredRequest)

            let totalSize = allItems.reduce(0) { $0 + $1.fileSize }

            return StorageStatistics(
                totalItems: allItems.count,
                totalSize: totalSize,
                textItems: textItems,
                imageItems: imageItems,
                fileItems: fileItems,
                expiredItems: expiredItems,
                orphanedFiles: orphanedFiles
            )
        }
    }
    
    // MARK: - Validation
    func validateContent(_ content: ContentData) throws {
        // Check file size
        if content.fileSize > Self.maxFileSize {
            throw ContentManagerError.contentTooLarge("File size exceeds maximum limit of \(Self.maxFileSize) bytes")
        }
        
        // Check text length
        if let text = content.content, text.count > Self.maxTextLength {
            throw ContentManagerError.contentTooLarge("Text content exceeds maximum length of \(Self.maxTextLength) characters")
        }
        
        // Validate content type specific requirements
        switch content.contentType {
        case .text:
            if content.content?.isEmpty != false {
                throw ContentManagerError.invalidContent("Text content cannot be empty")
            }
        case .image, .file:
            if content.data?.isEmpty != false {
                throw ContentManagerError.invalidContent("File data cannot be empty")
            }
        }
        
        // Validate required fields
        if content.fileSize < 0 {
            throw ContentManagerError.invalidContent("File size cannot be negative")
        }
        
        // Validate expiration date
        if let expiresAt = content.expiresAt, expiresAt <= Date() {
            throw ContentManagerError.invalidContent("Expiration date must be in the future")
        }
    }
    
    // MARK: - 快速验证（性能优化）
    private func validateContentQuickly(_ content: ContentData) throws {
        // 只做关键验证，避免深度检查
        if content.fileSize > Self.maxFileSize {
            throw ContentManagerError.contentTooLarge("File size exceeds maximum limit")
        }
        
        if content.fileSize < 0 {
            throw ContentManagerError.invalidContent("File size cannot be negative")
        }
        
        // 对于文本类型，只检查是否为空
        if content.contentType == .text && (content.content?.isEmpty ?? true) {
            throw ContentManagerError.invalidContent("Text content cannot be empty")
        }
    }
    
    // MARK: - 高性能文本内容处理
    private func handleTextContent(_ content: ContentData, for item: ContentItem) throws {
        guard let text = content.content else { return }
        
        // 高性能策略：根据文本大小选择存储策略
        let textLength = text.count
        
        if textLength <= 2000 {
            // 小文本：直接存储在数据库中
            item.content = text
            
            // 延迟生成文件备份
            if let data = content.getTextData() {
                let filePath = try saveFileData(data, for: item)
                item.filePath = filePath
            }
        } else {
            // 大文本：数据库存储摘要，文件存储完整内容
            let summary = generateOptimizedTextSummary(text, length: textLength)
            item.content = summary
            
            // 存储完整文本到文件
            if let data = content.getTextData() {
                let filePath = try saveFileData(data, for: item)
                item.filePath = filePath
            }
        }
    }
    
    // MARK: - 高效文本摘要生成
    private func generateOptimizedTextSummary(_ text: String, length: Int) -> String {
        let maxSummaryLength = 300
        
        if length <= maxSummaryLength {
            return text
        }
        
        // 高效截取：使用prefix方法
        let summary = String(text.prefix(maxSummaryLength))
        
        // 简化的摘要标记
        return summary + "...[+\(length - maxSummaryLength)]"
    }
    
    func isContentValid(_ content: ContentData) -> Bool {
        do {
            try validateContent(content)
            return true
        } catch {
            return false
        }
    }
    
    // MARK: - Utility Methods
    private func generateUniqueFileName(for contentType: ContentType, originalName: String? = nil) -> String {
        let timestamp = Int(Date().timeIntervalSince1970)
        let uuid = UUID().uuidString.prefix(8)
        
        if let originalName = originalName, !originalName.isEmpty {
            let nameWithoutExtension = (originalName as NSString).deletingPathExtension
            let fileExtension = (originalName as NSString).pathExtension
            return "\(nameWithoutExtension)_\(timestamp)_\(uuid).\(fileExtension)"
        }
        
        let defaultExtension: String
        switch contentType {
        case .text:
            defaultExtension = "txt"
        case .image:
            defaultExtension = "png"
        case .file:
            defaultExtension = "dat"
        }
        
        return "\(contentType.rawValue)_\(timestamp)_\(uuid).\(defaultExtension)"
    }
}