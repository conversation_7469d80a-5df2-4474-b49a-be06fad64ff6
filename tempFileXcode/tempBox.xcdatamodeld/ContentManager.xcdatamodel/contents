<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22522" systemVersion="23C71" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="AppSettings" representedClassName="AppSettings" syncable="YES" codeGenerationType="class">
        <attribute name="autoCleanupEnabled" optional="YES" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="cleanupInterval" optional="YES" attributeType="Integer 32" defaultValueString="30" usesScalarValueType="YES"/>
        <attribute name="defaultExportPath" optional="YES" attributeType="String"/>
        <attribute name="defaultStorageMode" optional="YES" attributeType="String" defaultValueString="temporary"/>
        <attribute name="globalHotkey" optional="YES" attributeType="String" defaultValueString="cmd+shift+v"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
    </entity>
    <entity name="Batch" representedClassName="Batch" syncable="YES" codeGenerationType="class">
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="notes" optional="YES" attributeType="String"/>
        <attribute name="updatedAt" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="contentItems" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="ContentItem" inverseName="batch" inverseEntity="ContentItem"/>
    </entity>
    <entity name="ContentItem" representedClassName="ContentItem" syncable="YES" codeGenerationType="class">
        <attribute name="content" optional="YES" attributeType="String"/>
        <attribute name="contentType" attributeType="String"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="expiresAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="fileName" optional="YES" attributeType="String"/>
        <attribute name="filePath" optional="YES" attributeType="String"/>
        <attribute name="fileSize" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="isPermanent" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="mimeType" optional="YES" attributeType="String"/>
        <attribute name="notes" optional="YES" attributeType="String"/>
        <attribute name="title" optional="YES" attributeType="String"/>
        <attribute name="updatedAt" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="batch" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Batch" inverseName="contentItems" inverseEntity="Batch"/>
        <relationship name="tags" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="Tag" inverseName="contentItems" inverseEntity="Tag"/>
    </entity>
    <entity name="Tag" representedClassName="Tag" syncable="YES" codeGenerationType="class">
        <attribute name="color" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" attributeType="String"/>
        <relationship name="contentItems" optional="YES" toMany="YES" deletionRule="Nullify" destinationEntity="ContentItem" inverseName="tags" inverseEntity="ContentItem"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="name"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
</model>