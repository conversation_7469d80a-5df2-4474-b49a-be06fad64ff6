<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 主渐变 - 更现代的蓝紫色 -->
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0066FF;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#7C3AED;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C026D3;stop-opacity:1" />
    </linearGradient>
    
    <!-- 盒子渐变 -->
    <linearGradient id="boxGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#FFFFFF;stop-opacity:0.85" />
    </linearGradient>
    
    <!-- 内容渐变 -->
    <linearGradient id="docGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#34C759;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#30D158;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="12" stdDeviation="24" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
    
    <filter id="innerShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect x="64" y="64" width="896" height="896" rx="200" ry="200" 
        fill="url(#mainGradient)" filter="url(#shadow)"/>
  
  <!-- 主要容器 -->
  <g transform="translate(512, 512)">
    <!-- 盒子主体 -->
    <rect x="-220" y="-80" width="440" height="200" rx="32" ry="32" 
          fill="url(#boxGradient)" filter="url(#innerShadow)" stroke="#FFFFFF" stroke-width="3"/>
    
    <!-- 盒子顶盖（微微倾斜表示打开状态） -->
    <rect x="-220" y="-160" width="440" height="80" rx="32" ry="32" 
          fill="url(#boxGradient)" filter="url(#innerShadow)" stroke="#FFFFFF" stroke-width="3"
          transform="rotate(-3)" transform-origin="0 -120"/>
    
    <!-- 盒子铰链装饰 -->
    <rect x="-200" y="-80" width="12" height="8" rx="6" ry="4" fill="#FFFFFF" opacity="0.8"/>
    <rect x="188" y="-80" width="12" height="8" rx="6" ry="4" fill="#FFFFFF" opacity="0.8"/>
    
    <!-- 内容物品组 -->
    <g transform="translate(0, -20)">
      <!-- 文档1 -->
      <g transform="translate(-120, 0) rotate(-5)">
        <rect x="-20" y="-30" width="40" height="52" rx="6" ry="6" 
              fill="#10B981" filter="url(#innerShadow)"/>
        <rect x="-16" y="-24" width="32" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
        <rect x="-16" y="-18" width="24" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
        <rect x="-16" y="-12" width="28" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
        <rect x="-16" y="-6" width="20" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
      </g>
      
      <!-- 文档2 -->
      <g transform="translate(-40, 0) rotate(2)">
        <rect x="-20" y="-30" width="40" height="52" rx="6" ry="6" 
              fill="#F59E0B" filter="url(#innerShadow)"/>
        <rect x="-16" y="-24" width="32" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
        <rect x="-16" y="-18" width="26" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
        <rect x="-16" y="-12" width="22" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
        <rect x="-16" y="-6" width="30" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
      </g>
      
      <!-- 文档3 -->
      <g transform="translate(40, 0) rotate(-2)">
        <rect x="-20" y="-30" width="40" height="52" rx="6" ry="6" 
              fill="#EF4444" filter="url(#innerShadow)"/>
        <rect x="-16" y="-24" width="32" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
        <rect x="-16" y="-18" width="28" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
        <rect x="-16" y="-12" width="24" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
        <rect x="-16" y="-6" width="26" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
      </g>
      
      <!-- 文档4 -->
      <g transform="translate(120, 0) rotate(4)">
        <rect x="-20" y="-30" width="40" height="52" rx="6" ry="6" 
              fill="#3B82F6" filter="url(#innerShadow)"/>
        <rect x="-16" y="-24" width="32" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
        <rect x="-16" y="-18" width="30" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
        <rect x="-16" y="-12" width="22" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
        <rect x="-16" y="-6" width="28" height="3" rx="1.5" fill="#FFFFFF" opacity="0.8"/>
      </g>
    </g>
    
    <!-- BOX 标签 -->
    <g transform="translate(0, 80)">
      <rect x="-32" y="-12" width="64" height="24" rx="12" ry="12" 
            fill="url(#mainGradient)" opacity="0.95" filter="url(#innerShadow)"/>
      <text x="0" y="4" text-anchor="middle" font-family="system-ui, -apple-system" 
            font-size="15" font-weight="800" fill="#FFFFFF" filter="url(#glow)">BOX</text>
    </g>
    
    <!-- 装饰光效 -->
    <ellipse cx="0" cy="-120" rx="180" ry="20" 
             fill="#FFFFFF" opacity="0.4" transform="rotate(-3)"/>
    
    <!-- 角落装饰点 -->
    <circle cx="-180" cy="-80" r="6" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="180" cy="-80" r="6" fill="#FFFFFF" opacity="0.6"/>
    
    <!-- 微妙的反光效果 -->
    <rect x="-220" y="-160" width="100" height="40" rx="32" ry="20" 
          fill="url(#boxGradient)" opacity="0.3" transform="rotate(-3)" transform-origin="0 -120"/>
  </g>
</svg>