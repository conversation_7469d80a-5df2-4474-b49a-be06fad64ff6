import SwiftUI
import CloudKit

// MARK: - Cloud Sync View
struct CloudSyncView: View {
    @EnvironmentObject private var batchService: BatchService
    @EnvironmentObject private var contentService: ContentService
    @StateObject private var cloudSyncService: CloudSyncService
    
    @State private var showingEnableAlert = false
    @State private var showingShareSheet = false
    @State private var showingErrorAlert = false
    @State private var errorMessage = ""
    @State private var selectedBatch: NSManagedObject?
    @State private var sharedURL: URL?
    
    init(contentService: ContentService, batchService: BatchService) {
        self._cloudSyncService = StateObject(wrappedValue: CloudSyncService(
            contentService: contentService,
            batchService: batchService
        ))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 头部状态
            headerView
            
            Divider()
            
            if cloudSyncService.isEnabled {
                enabledContentView
            } else {
                disabledContentView
            }
        }
        .alert("启用云同步", isPresented: $showingEnableAlert) {
            Button("启用") {
                enableCloudSync()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("启用云同步将允许您在多个设备间同步批次和内容，并与他人分享。需要iCloud账户。")
        }
        .alert("错误", isPresented: $showingErrorAlert) {
            Button("确定") { }
        } message: {
            Text(errorMessage)
        }
        .sheet(isPresented: $showingShareSheet) {
            if let url = sharedURL {
                ShareSheetView(url: url)
            }
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("云同步与协作")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("在设备间同步内容并与他人协作")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 同步状态指示器
                syncStatusIndicator
            }
            
            // 同步信息
            if cloudSyncService.isEnabled {
                syncInfoView
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    private var syncStatusIndicator: some View {
        HStack(spacing: 8) {
            Circle()
                .fill(statusColor)
                .frame(width: 8, height: 8)
            
            Text(cloudSyncService.syncStatus.displayName)
                .font(.caption)
                .foregroundColor(.secondary)
                
            if cloudSyncService.syncStatus == .syncing {
                ProgressView()
                    .scaleEffect(0.6)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
    
    private var statusColor: Color {
        switch cloudSyncService.syncStatus {
        case .disabled:
            return .gray
        case .idle:
            return .green
        case .syncing:
            return .blue
        case .error:
            return .red
        }
    }
    
    private var syncInfoView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text("最后同步")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                if let lastSync = cloudSyncService.lastSyncDate {
                    Text(lastSync, style: .relative)
                        .font(.caption)
                } else {
                    Text("从未同步")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Button("立即同步") {
                syncCurrentBatch()
            }
            .buttonStyle(.bordered)
            .controlSize(.small)
            .disabled(cloudSyncService.syncStatus == .syncing)
        }
    }
    
    private var disabledContentView: some View {
        VStack(spacing: 24) {
            // iCloud图标和说明
            VStack(spacing: 16) {
                Image(systemName: "icloud")
                    .font(.system(size: 64))
                    .foregroundColor(.blue)
                
                Text("启用云同步")
                    .font(.title3)
                    .fontWeight(.semibold)
                
                Text("云同步功能允许您：")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // 功能列表
            VStack(alignment: .leading, spacing: 12) {
                FeatureRow(
                    icon: "arrow.triangle.2.circlepath",
                    title: "多设备同步",
                    description: "在iPhone、iPad和Mac之间同步内容"
                )
                
                FeatureRow(
                    icon: "person.2.circle",
                    title: "实时协作",
                    description: "与团队成员分享批次并协作编辑"
                )
                
                FeatureRow(
                    icon: "link.circle",
                    title: "分享链接",
                    description: "生成分享链接让他人访问您的内容"
                )
                
                FeatureRow(
                    icon: "externaldrive.connected.to.line.below",
                    title: "自动备份",
                    description: "重要内容自动备份到iCloud"
                )
            }
            .padding()
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
            
            // 启用按钮
            Button("启用云同步") {
                showingEnableAlert = true
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
            
            Spacer()
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var enabledContentView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 当前批次同步
                currentBatchSyncSection
                
                // 批次分享
                batchSharingSection
                
                // 协作管理
                collaborationSection
                
                // 同步设置
                syncSettingsSection
            }
            .padding()
        }
    }
    
    private var currentBatchSyncSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("当前批次")
                .font(.headline)
            
            if let currentBatch = batchService.currentBatch {
                BatchSyncCard(
                    batch: currentBatch,
                    onSync: { syncBatch(currentBatch) },
                    onShare: { shareBatch(currentBatch) }
                )
            } else {
                Text("暂无活动批次")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
        }
    }
    
    private var batchSharingSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("批次分享")
                .font(.headline)
            
            Text("选择要分享的批次并生成分享链接")
                .font(.caption)
                .foregroundColor(.secondary)
            
            LazyVStack(spacing: 8) {
                ForEach(batchService.batches, id: \.objectID) { batch in
                    BatchSyncCard(
                        batch: batch,
                        onSync: { syncBatch(batch) },
                        onShare: { shareBatch(batch) }
                    )
                }
            }
        }
    }
    
    private var collaborationSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("协作管理")
                .font(.headline)
            
            // 接受分享的批次
            HStack {
                Text("从分享链接导入批次")
                    .font(.subheadline)
                
                Spacer()
                
                Button("导入") {
                    // 这里应该打开文件选择器或者URL输入框
                    showImportDialog()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
            }
            .padding()
            .background(Color.blue.opacity(0.1))
            .cornerRadius(8)
            
            // 协作者列表（示例）
            VStack(alignment: .leading, spacing: 8) {
                Text("协作者")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text("暂无协作者")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var syncSettingsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("同步设置")
                .font(.headline)
            
            VStack(spacing: 16) {
                // 自动同步设置
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("自动同步")
                            .font(.subheadline)
                        
                        Text("内容变更时自动上传到iCloud")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Toggle("", isOn: .constant(true))
                }
                
                Divider()
                
                // 同步频率
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("同步频率")
                            .font(.subheadline)
                        
                        Text("检查云端更新的频率")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Picker("同步频率", selection: .constant("auto")) {
                        Text("自动").tag("auto")
                        Text("每小时").tag("hourly")
                        Text("每天").tag("daily")
                        Text("手动").tag("manual")
                    }
                    .pickerStyle(.menu)
                }
                
                Divider()
                
                // 禁用云同步
                Button("禁用云同步") {
                    disableCloudSync()
                }
                .foregroundColor(.red)
            }
            .padding()
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Actions
    private func enableCloudSync() {
        Task {
            do {
                try await cloudSyncService.enableSync()
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    showingErrorAlert = true
                }
            }
        }
    }
    
    private func disableCloudSync() {
        Task {
            await cloudSyncService.disableSync()
        }
    }
    
    private func syncCurrentBatch() {
        guard let currentBatch = batchService.currentBatch else { return }
        syncBatch(currentBatch)
    }
    
    private func syncBatch(_ batch: NSManagedObject) {
        Task {
            do {
                try await cloudSyncService.syncBatch(batch)
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    showingErrorAlert = true
                }
            }
        }
    }
    
    private func shareBatch(_ batch: NSManagedObject) {
        Task {
            do {
                let share = try await cloudSyncService.shareBatch(batch)
                
                if let url = share.url {
                    await MainActor.run {
                        sharedURL = url
                        showingShareSheet = true
                    }
                }
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    showingErrorAlert = true
                }
            }
        }
    }
    
    private func showImportDialog() {
        // 这里应该实现导入分享批次的逻辑
        // 可以通过文件选择器选择.cloudsharing文件
        // 或者通过URL输入框输入分享链接
    }
}

// MARK: - Feature Row
struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Batch Sync Card
struct BatchSyncCard: View {
    let batch: NSManagedObject
    let onSync: () -> Void
    let onShare: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(batch.value(forKey: "name") as? String ?? "未命名批次")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    if let updatedAt = batch.value(forKey: "updatedAt") as? Date {
                        Text("更新于 \(updatedAt, style: .relative)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 同步状态指示
                syncStatusBadge
            }
            
            HStack(spacing: 12) {
                Button("同步") {
                    onSync()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                
                Button("分享") {
                    onShare()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                
                Spacer()
                
                // 批次统计
                Text("包含 X 个项目") // 这里需要实际的统计数据
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(12)
    }
    
    private var syncStatusBadge: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(Color.green) // 这里应该根据实际同步状态显示
                .frame(width: 6, height: 6)
            
            Text("已同步")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Share Sheet View
struct ShareSheetView: View {
    let url: URL
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(spacing: 12) {
                    Image(systemName: "link.circle.fill")
                        .font(.system(size: 48))
                        .foregroundColor(.blue)
                    
                    Text("分享链接已生成")
                        .font(.title3)
                        .fontWeight(.semibold)
                    
                    Text("其他人可以通过此链接访问您的批次")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // 链接显示
                VStack(alignment: .leading, spacing: 8) {
                    Text("分享链接:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack {
                        Text(url.absoluteString)
                            .font(.caption)
                            .lineLimit(2)
                            .textSelection(.enabled)
                        
                        Spacer()
                        
                        Button("复制") {
                            NSPasteboard.general.clearContents()
                            NSPasteboard.general.setString(url.absoluteString, forType: .string)
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
                
                // 分享选项
                VStack(spacing: 12) {
                    ShareLink(item: url) {
                        Label("通过系统分享", systemImage: "square.and.arrow.up")
                            .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.borderedProminent)
                    
                    Button("生成二维码") {
                        // 生成二维码的逻辑
                    }
                    .buttonStyle(.bordered)
                    .frame(maxWidth: .infinity)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("分享批次")
            // navigationBarTitleDisplayMode is iOS only, removed for macOS
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    CloudSyncView(contentService: ContentService(), batchService: BatchService())
        .environmentObject(BatchService())
        .environmentObject(ContentService())
}