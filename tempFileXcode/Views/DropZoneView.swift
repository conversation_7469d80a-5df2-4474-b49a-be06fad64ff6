import SwiftUI
import UniformTypeIdentifiers

// MARK: - Drop Zone View
struct DropZoneView<Content: View>: View {
    let content: Content
    let onFilesDropped: ([URL]) -> Void
    let onTextDropped: (String) -> Void
    let onImageDropped: (NSImage) -> Void

    @State private var isTargeted = false

    init(
        onFilesDropped: @escaping ([URL]) -> Void = { _ in },
        onTextDropped: @escaping (String) -> Void = { _ in },
        onImageDropped: @escaping (NSImage) -> Void = { _ in },
        @ViewBuilder content: () -> Content
    ) {
        self.onFilesDropped = onFilesDropped
        self.onTextDropped = onTextDropped
        self.onImageDropped = onImageDropped
        self.content = content()
    }

    var body: some View {
        content
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isTargeted ? Color.accentColor.opacity(0.1) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(
                                isTargeted ? Color.accentColor : Color.clear,
                                style: StrokeStyle(lineWidth: 2, dash: [5])
                            )
                    )
            )
            .onDrop(of: supportedTypes, isTargeted: $isTargeted) { providers in
                return handleDrop(providers: providers)
            }
    }

    private var supportedTypes: [UTType] {
        return [
            .fileURL,
            .image,
            .text,
            .plainText,
            .utf8PlainText
        ]
    }

    private func handleDrop(providers: [NSItemProvider]) -> Bool {
        var handled = false

        for provider in providers {
            // Handle file URLs
            if provider.hasItemConformingToTypeIdentifier(UTType.fileURL.identifier) {
                provider.loadItem(forTypeIdentifier: UTType.fileURL.identifier, options: nil) { (item: NSSecureCoding?, error: Error?) in
                    if let data = item as? Data,
                       let url = URL(dataRepresentation: data, relativeTo: nil) {
                        DispatchQueue.main.async {
                            onFilesDropped([url])
                        }
                    }
                }
                handled = true
            }

            // Handle images
            else if provider.hasItemConformingToTypeIdentifier(UTType.image.identifier) {
                provider.loadItem(forTypeIdentifier: UTType.image.identifier, options: nil) { (item: NSSecureCoding?, error: Error?) in
                    if let data = item as? Data,
                       let image = NSImage(data: data) {
                        DispatchQueue.main.async {
                            onImageDropped(image)
                        }
                    }
                }
                handled = true
            }

            // Handle text
            else if provider.hasItemConformingToTypeIdentifier(UTType.plainText.identifier) {
                provider.loadItem(forTypeIdentifier: UTType.plainText.identifier, options: nil) { item, error in
                    if let text = item as? String {
                        DispatchQueue.main.async {
                            onTextDropped(text)
                        }
                    }
                }
                handled = true
            }
        }

        return handled
    }
}

// MARK: - File Drop Zone
struct FileDropZone: View {
    let onFilesDropped: ([URL]) -> Void
    @State private var isHovering = false

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "plus.circle.dashed")
                .font(.system(size: 48))
                .foregroundColor(.secondary)

            VStack(spacing: 4) {
                Text("拖拽文件到这里")
                    .font(.headline)
                    .foregroundColor(.primary)

                Text("或点击添加内容")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity, minHeight: 120)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.secondary.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.secondary.opacity(0.3), style: StrokeStyle(lineWidth: 1, dash: [8, 4]))
                )
        )
        .onDrop(of: [.fileURL], isTargeted: $isHovering) { providers in
            handleFileDrop(providers: providers)
            return true
        }
        .scaleEffect(isHovering ? 1.02 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isHovering)
    }

    private func handleFileDrop(providers: [NSItemProvider]) {
        var urls: [URL] = []

        let group = DispatchGroup()

        for provider in providers {
            if provider.hasItemConformingToTypeIdentifier(UTType.fileURL.identifier) {
                group.enter()
                provider.loadItem(forTypeIdentifier: UTType.fileURL.identifier, options: nil) { item, error in
                    defer { group.leave() }

                    if let data = item as? Data,
                       let url = URL(dataRepresentation: data, relativeTo: nil) {
                        urls.append(url)
                    }
                }
            }
        }

        group.notify(queue: .main) {
            if !urls.isEmpty {
                onFilesDropped(urls)
            }
        }
    }
}

#Preview {
    FileDropZone { urls in
        print("Files dropped: \(urls)")
    }
    .padding()
}

// MARK: - Content Drop Handler Extension
extension View {
    func contentDropHandler(
        contentService: ContentService,
        pasteboardMonitor: PasteboardMonitor
    ) -> some View {
        self.onDrop(of: [.fileURL, .image, .text], isTargeted: nil) { providers in
            handleContentDrop(providers: providers, contentService: contentService, pasteboardMonitor: pasteboardMonitor)
            return true
        }
    }

    private func handleContentDrop(
        providers: [NSItemProvider],
        contentService: ContentService,
        pasteboardMonitor: PasteboardMonitor
    ) {
        Task {
            for provider in providers {
                // 处理文件URL
                if provider.hasItemConformingToTypeIdentifier("public.file-url") {
                    _ = provider.loadObject(ofClass: URL.self) { url, error in
                        if let url = url {
                            Task { @MainActor in
                                do {
                                    let contentData = try ContentData.fromFile(at: url)
                                    _ = try await contentService.addContent(contentData)
                                    print("Successfully added file: \(url.lastPathComponent)")
                                } catch {
                                    print("Failed to add file: \(error)")
                                }
                            }
                        }
                    }
                }
                // 处理文字
                else if provider.hasItemConformingToTypeIdentifier("public.plain-text") {
                    _ = provider.loadObject(ofClass: String.self) { text, error in
                        if let text = text {
                            Task { @MainActor in
                                do {
                                    let contentData = ContentData.fromText(text)
                                    _ = try await contentService.addContent(contentData)
                                    print("Successfully added text content")
                                } catch {
                                    print("Failed to add text: \(error)")
                                }
                            }
                        }
                    }
                }
                // 处理图片
                else if provider.hasItemConformingToTypeIdentifier("public.image") {
                    provider.loadItem(forTypeIdentifier: "public.image", options: nil) { item, error in
                        if let data = item as? Data {
                            Task { @MainActor in
                                do {
                                    let contentData = ContentData.fromImage(data, fileName: "dropped_image.png")
                                    _ = try await contentService.addContent(contentData)
                                    print("Successfully added image content")
                                } catch {
                                    print("Failed to add image: \(error)")
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}