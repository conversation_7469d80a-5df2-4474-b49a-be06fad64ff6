import SwiftUI
import CoreData

// MARK: - 现代化批次选择器
struct ModernBatchSelector: View {
    @EnvironmentObject private var batchService: BatchService
    @State private var showingBatchList = false
    @State private var showingNewBatchSheet = false
    @State private var newBatchName = ""
    @State private var newBatchNotes = ""
    
    var body: some View {
        HStack(spacing: 12) {
            // 当前批次信息
            currentBatchInfo
            
            Spacer()
            
            // 批次操作按钮
            batchActions
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            Color(NSColor.controlBackgroundColor)
                .overlay(
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 1),
                    alignment: .bottom
                )
        )
        .sheet(isPresented: $showingNewBatchSheet) {
            newBatchSheet
        }
    }
    
    // MARK: - 当前批次信息
    private var currentBatchInfo: some View {
        HStack(spacing: 12) {
            // 批次图标
            Image(systemName: "folder.fill")
                .foregroundColor(.accentColor)
                .font(.system(size: 20))
            
            VStack(alignment: .leading, spacing: 2) {
                Text("当前批次")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if let currentBatch = batchService.currentBatch {
                    let batchName = currentBatch.value(forKey: "name") as? String ?? "未命名批次"
                    let itemCount = (currentBatch.value(forKey: "contentItems") as? NSSet)?.count ?? 0
                    
                    HStack(spacing: 8) {
                        Text(batchName)
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Text("(\(itemCount) 项)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                } else {
                    Text("无批次")
                        .font(.headline)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    // MARK: - 批次操作
    private var batchActions: some View {
        HStack(spacing: 8) {
            // 批次列表按钮
            Menu {
                batchListMenu
            } label: {
                HStack(spacing: 4) {
                    Text("切换批次")
                        .font(.subheadline)
                    Image(systemName: "chevron.down")
                        .font(.system(size: 10))
                }
                .foregroundColor(.accentColor)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color.accentColor.opacity(0.1))
                )
            }
            .menuStyle(.borderlessButton)
            
            // 新建批次按钮
            ActionButton(
                title: "新建批次",
                action: { showingNewBatchSheet = true },
                style: .primary
            )
        }
    }
    
    // MARK: - 批次列表菜单
    @ViewBuilder
    private var batchListMenu: some View {
        if batchService.batches.isEmpty {
            Text("没有可用的批次")
                .foregroundColor(.secondary)
        } else {
            ForEach(batchService.batches, id: \.objectID) { batch in
                let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
                let isCurrentBatch = batch == batchService.currentBatch
                let itemCount = (batch.value(forKey: "contentItems") as? NSSet)?.count ?? 0
                let createdAt = batch.value(forKey: "createdAt") as? Date ?? Date()
                
                Button(action: {
                    batchService.setCurrentBatch(batch)
                }) {
                    HStack {
                        VStack(alignment: .leading, spacing: 2) {
                            HStack {
                                Text(batchName)
                                    .fontWeight(isCurrentBatch ? .semibold : .regular)
                                
                                if isCurrentBatch {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.accentColor)
                                        .font(.system(size: 12))
                                }
                            }
                            
                            HStack(spacing: 8) {
                                Text("\(itemCount) 项")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                
                                Text(createdAt.formatted(date: .abbreviated, time: .omitted))
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                    }
                }
                .disabled(isCurrentBatch)
            }
            
            Divider()
            
            Button("管理批次...") {
                // TODO: 打开批次管理界面
            }
            
            Button("删除批次", role: .destructive) {
                if let currentBatch = batchService.currentBatch {
                    deleteBatch(currentBatch)
                }
            }
            .disabled(batchService.currentBatch == nil)
        }
    }
    
    // MARK: - 新建批次表单
    private var newBatchSheet: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("批次名称")
                        .font(.headline)
                    
                    TextField("输入批次名称", text: $newBatchName)
                        .textFieldStyle(.roundedBorder)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("描述（可选）")
                        .font(.headline)
                    
                    TextField("输入批次描述", text: $newBatchNotes, axis: .vertical)
                        .textFieldStyle(.roundedBorder)
                        .lineLimit(3...6)
                }
                
                Spacer()
                
                HStack(spacing: 12) {
                    ActionButton(
                        title: "取消",
                        action: {
                            showingNewBatchSheet = false
                            resetForm()
                        },
                        style: .secondary
                    )
                    
                    ActionButton(
                        title: "创建批次",
                        action: createNewBatch,
                        style: .primary,
                        isEnabled: !newBatchName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
                    )
                }
            }
            .padding()
            .navigationTitle("新建批次")
        }
        .frame(width: 400, height: 300)
        .onAppear {
            newBatchName = generateDefaultBatchName()
        }
    }
    
    // MARK: - 辅助方法
    private func deleteBatch(_ batch: NSManagedObject) {
        do {
            try batchService.deleteBatch(batch)
        } catch {
            NSLog("Failed to delete batch: \(error)")
        }
    }
    
    private func createNewBatch() {
        let trimmedName = newBatchName.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedNotes = newBatchNotes.trimmingCharacters(in: .whitespacesAndNewlines)
        
        do {
            let newBatch = try batchService.createNewBatch(
                name: trimmedName,
                notes: trimmedNotes.isEmpty ? nil : trimmedNotes
            )
            batchService.setCurrentBatch(newBatch)
            showingNewBatchSheet = false
            resetForm()
        } catch {
            NSLog("Failed to create new batch: \(error)")
        }
    }
    
    private func resetForm() {
        newBatchName = ""
        newBatchNotes = ""
    }
    
    private func generateDefaultBatchName() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return "批次 \(formatter.string(from: Date()))"
    }
}

// MARK: - 批次信息卡片
struct BatchInfoCard: View {
    let batch: NSManagedObject
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "folder.fill")
                    .foregroundColor(.accentColor)
                    .font(.system(size: 16))
                
                Text(batch.value(forKey: "name") as? String ?? "未命名批次")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
            }
            
            let itemCount = (batch.value(forKey: "contentItems") as? NSSet)?.count ?? 0
            let createdAt = batch.value(forKey: "createdAt") as? Date ?? Date()
            
            HStack {
                Text("\(itemCount) 个项目")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("创建于 \(createdAt.formatted(date: .abbreviated, time: .omitted))")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            if let notes = batch.value(forKey: "notes") as? String, !notes.isEmpty {
                Text(notes)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(NSColor.controlBackgroundColor))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
    }
}