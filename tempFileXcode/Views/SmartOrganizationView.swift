import SwiftUI
import CoreData

// MARK: - Smart Organization View
struct SmartOrganizationView: View {
    @EnvironmentObject private var batchService: BatchService
    @EnvironmentObject private var contentService: ContentService
    
    @StateObject private var smartOrganizationService: SmartOrganizationService
    @State private var selectedTab: OrganizationTab = .duplicates
    @State private var isScanning = false
    @State private var duplicateGroups: [DuplicateGroup] = []
    @State private var batchSuggestions: [BatchSuggestion] = []
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    init(contentService: ContentService) {
        self._smartOrganizationService = StateObject(wrappedValue: SmartOrganizationService(contentService: contentService))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题和控制
            headerView
            
            // 标签页选择
            tabSelector
            
            Divider()
            
            // 内容区域
            TabView(selection: $selectedTab) {
                DuplicateDetectionView(
                    duplicateGroups: $duplicateGroups,
                    isScanning: $isScanning,
                    onScan: scanForDuplicates,
                    onRemoveDuplicates: removeDuplicates
                )
                .tag(OrganizationTab.duplicates)
                
                BatchSuggestionsView(
                    suggestions: $batchSuggestions,
                    isScanning: $isScanning,
                    onScan: generateSuggestions,
                    onCreateBatch: createSuggestedBatch
                )
                .tag(OrganizationTab.suggestions)
                
                AutoTaggingView(
                    onAnalyze: analyzeForTags
                )
                .tag(OrganizationTab.tagging)
            }
            .tabViewStyle(DefaultTabViewStyle())
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("智能整理")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("自动检测重复文件并优化批次组织")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if isScanning {
                ProgressView()
                    .scaleEffect(0.8)
            }
        }
        .padding()
    }
    
    private var tabSelector: some View {
        HStack(spacing: 0) {
            ForEach(OrganizationTab.allCases, id: \.self) { tab in
                Button(action: {
                    selectedTab = tab
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: tab.icon)
                            .font(.title3)
                        
                        Text(tab.title)
                            .font(.caption)
                    }
                    .foregroundColor(selectedTab == tab ? .accentColor : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(selectedTab == tab ? Color.accentColor.opacity(0.1) : Color.clear)
                    )
                }
                .buttonStyle(.plain)
            }
        }
        .padding(.horizontal)
    }
    
    // MARK: - Actions
    private func scanForDuplicates() {
        Task {
            isScanning = true
            
            do {
                let allItems = try await contentService.getAllContent()
                let groups = await smartOrganizationService.detectDuplicates(in: allItems)
                
                await MainActor.run {
                    duplicateGroups = groups
                    isScanning = false
                    
                    if groups.isEmpty {
                        alertMessage = "未发现重复文件"
                        showingAlert = true
                    }
                }
            } catch {
                await MainActor.run {
                    isScanning = false
                    alertMessage = "扫描失败: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
        }
    }
    
    private func generateSuggestions() {
        Task {
            isScanning = true
            
            do {
                let allItems = try await contentService.getAllContent()
                let suggestions = await smartOrganizationService.suggestBatchOrganization(for: allItems)
                
                await MainActor.run {
                    batchSuggestions = suggestions
                    isScanning = false
                    
                    if suggestions.isEmpty {
                        alertMessage = "暂无批次整理建议"
                        showingAlert = true
                    }
                }
            } catch {
                await MainActor.run {
                    isScanning = false
                    alertMessage = "分析失败: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
        }
    }
    
    private func removeDuplicates(_ group: DuplicateGroup) {
        Task {
            do {
                for item in group.duplicateItems {
                    try await contentService.deleteContent(item)
                }
                
                await MainActor.run {
                    duplicateGroups.removeAll { $0.id == group.id }
                    alertMessage = "已删除 \(group.duplicateItems.count) 个重复文件"
                    showingAlert = true
                }
            } catch {
                await MainActor.run {
                    alertMessage = "删除失败: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
        }
    }
    
    private func createSuggestedBatch(_ suggestion: BatchSuggestion) {
        Task {
            do {
                let _ = try batchService.createNewBatch(name: suggestion.name, notes: nil)
                
                for _ in suggestion.items {
                    // 这里需要实现将内容项移动到新批次的逻辑
                    // 暂时不实现，因为需要修改BatchService
                }
                
                await MainActor.run {
                    batchSuggestions.removeAll { $0.id == suggestion.id }
                    alertMessage = "已创建批次: \(suggestion.name)"
                    showingAlert = true
                }
            } catch {
                await MainActor.run {
                    alertMessage = "创建批次失败: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
        }
    }
    
    private func analyzeForTags() {
        Task {
            do {
                let allItems = try await contentService.getAllContent()
                
                for item in allItems.prefix(10) { // 限制处理数量以避免性能问题
                    let suggestedTags = await smartOrganizationService.suggestTags(for: item)
                    
                    if !suggestedTags.isEmpty {
                        // 这里需要实现自动标签的应用逻辑
                        print("Suggested tags for \(item.displayTitle): \(suggestedTags)")
                    }
                }
                
                await MainActor.run {
                    alertMessage = "标签分析完成"
                    showingAlert = true
                }
            } catch {
                await MainActor.run {
                    alertMessage = "标签分析失败: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
        }
    }
}

// MARK: - Organization Tab
enum OrganizationTab: String, CaseIterable {
    case duplicates = "duplicates"
    case suggestions = "suggestions"
    case tagging = "tagging"
    
    var title: String {
        switch self {
        case .duplicates:
            return "重复检测"
        case .suggestions:
            return "批次建议"
        case .tagging:
            return "智能标签"
        }
    }
    
    var icon: String {
        switch self {
        case .duplicates:
            return "doc.on.doc"
        case .suggestions:
            return "folder.badge.gearshape"
        case .tagging:
            return "tag.circle"
        }
    }
}

// MARK: - Duplicate Detection View
struct DuplicateDetectionView: View {
    @Binding var duplicateGroups: [DuplicateGroup]
    @Binding var isScanning: Bool
    let onScan: () -> Void
    let onRemoveDuplicates: (DuplicateGroup) -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            // 控制按钮
            HStack {
                Button("扫描重复文件") {
                    onScan()
                }
                .buttonStyle(.borderedProminent)
                .disabled(isScanning)
                
                Spacer()
                
                if !duplicateGroups.isEmpty {
                    Text("\(duplicateGroups.count) 组重复文件")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal)
            
            if duplicateGroups.isEmpty {
                emptyStateView
            } else {
                duplicatesList
            }
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.on.doc")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("暂无重复文件")
                .font(.title3)
                .foregroundColor(.secondary)
            
            Text("点击\"扫描重复文件\"开始检测")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var duplicatesList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(duplicateGroups) { group in
                    DuplicateGroupCard(
                        group: group,
                        onRemove: { onRemoveDuplicates(group) }
                    )
                }
            }
            .padding(.horizontal)
        }
    }
}

// MARK: - Duplicate Group Card
struct DuplicateGroupCard: View {
    let group: DuplicateGroup
    let onRemove: () -> Void
    
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 头部
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(group.reason.displayName)
                        .font(.headline)
                    
                    Text("\(group.items.count) 个重复文件 · 相似度 \(Int(group.similarity * 100))%")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Button("删除重复") {
                    onRemove()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
                
                Button(action: { isExpanded.toggle() }) {
                    Image(systemName: isExpanded ? "chevron.down" : "chevron.right")
                }
                .buttonStyle(.plain)
            }
            
            // 主要文件预览
            if let primaryItem = group.primaryItem {
                ContentItemPreview(item: primaryItem, isPrimary: true)
            }
            
            // 重复文件列表（可展开）
            if isExpanded {
                VStack(spacing: 8) {
                    Text("重复文件:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    ForEach(group.duplicateItems, id: \.id) { item in
                        ContentItemPreview(item: item, isPrimary: false)
                    }
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(12)
    }
}

// MARK: - Content Item Preview
struct ContentItemPreview: View {
    let item: ContentItem
    let isPrimary: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: item.contentTypeEnum.systemImage)
                .foregroundColor(Color(tagColor: item.contentTypeEnum.color))
                .font(.title3)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(item.displayTitle)
                    .font(.subheadline)
                    .fontWeight(isPrimary ? .semibold : .regular)
                    .lineLimit(1)
                
                HStack(spacing: 8) {
                    Text(item.formattedFileSize)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    if let createdAt = item.createdAt {
                        Text(createdAt, style: .relative)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            if isPrimary {
                Text("保留")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.2))
                    .foregroundColor(.green)
                    .cornerRadius(6)
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Batch Suggestions View
struct BatchSuggestionsView: View {
    @Binding var suggestions: [BatchSuggestion]
    @Binding var isScanning: Bool
    let onScan: () -> Void
    let onCreateBatch: (BatchSuggestion) -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            // 控制按钮
            HStack {
                Button("分析批次建议") {
                    onScan()
                }
                .buttonStyle(.borderedProminent)
                .disabled(isScanning)
                
                Spacer()
                
                if !suggestions.isEmpty {
                    Text("\(suggestions.count) 个建议")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal)
            
            if suggestions.isEmpty {
                emptyStateView
            } else {
                suggestionsList
            }
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "folder.badge.gearshape")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("暂无批次建议")
                .font(.title3)
                .foregroundColor(.secondary)
            
            Text("点击\"分析批次建议\"开始分析")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var suggestionsList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(suggestions) { suggestion in
                    BatchSuggestionCard(
                        suggestion: suggestion,
                        onCreate: { onCreateBatch(suggestion) }
                    )
                }
            }
            .padding(.horizontal)
        }
    }
}

// MARK: - Batch Suggestion Card
struct BatchSuggestionCard: View {
    let suggestion: BatchSuggestion
    let onCreate: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(suggestion.name)
                        .font(.headline)
                    
                    Text(suggestion.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("\(suggestion.items.count) 个文件 · 置信度 \(Int(suggestion.confidence * 100))%")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Button("创建批次") {
                    onCreate()
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.small)
            }
            
            // 文件预览
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                ForEach(suggestion.items.prefix(6), id: \.id) { item in
                    VStack(spacing: 4) {
                        Image(systemName: item.contentTypeEnum.systemImage)
                            .foregroundColor(Color(tagColor: item.contentTypeEnum.color))
                            .font(.caption)
                        
                        Text(item.displayTitle)
                            .font(.caption2)
                            .lineLimit(1)
                            .truncationMode(.middle)
                    }
                    .padding(.vertical, 4)
                }
                
                if suggestion.items.count > 6 {
                    Text("+\(suggestion.items.count - 6)")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, minHeight: 40)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(12)
    }
}

// MARK: - Auto Tagging View
struct AutoTaggingView: View {
    let onAnalyze: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            // 控制按钮
            HStack {
                Button("分析并添加标签") {
                    onAnalyze()
                }
                .buttonStyle(.borderedProminent)
                
                Spacer()
            }
            .padding(.horizontal)
            
            // 功能说明
            VStack(spacing: 16) {
                Image(systemName: "tag.circle")
                    .font(.system(size: 48))
                    .foregroundColor(.secondary)
                
                Text("智能标签分析")
                    .font(.title3)
                    .foregroundColor(.secondary)
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("• 基于文件类型自动添加标签")
                    Text("• 分析文件内容提取关键词")
                    Text("• 根据创建时间添加时间标签")
                    Text("• 基于文件扩展名分类")
                }
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
    }
}

#Preview {
    SmartOrganizationView(contentService: ContentService())
        .environmentObject(BatchService())
        .environmentObject(ContentService())
}