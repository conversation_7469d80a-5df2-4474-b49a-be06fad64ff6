import SwiftUI

// MARK: - Interactive Button
// 专门解决按钮点击问题的自定义按钮组件
struct InteractiveButton<Content: View>: View {
    let action: () -> Void
    let content: () -> Content
    
    @State private var isPressed = false
    @State private var isHovered = false
    
    init(action: @escaping () -> Void, @ViewBuilder content: @escaping () -> Content) {
        self.action = action
        self.content = content
    }
    
    var body: some View {
        Button(action: {
            // 确保在主线程执行
            DispatchQueue.main.async {
                action()
            }
        }) {
            content()
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .opacity(isPressed ? 0.8 : 1.0)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(isHovered ? Color.gray.opacity(0.1) : Color.clear)
                )
        }
        .buttonStyle(.plain)
        .contentShape(Rectangle()) // 确保整个区域都可点击
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.1)) {
                isHovered = hovering
            }
        }
        .pressEvents(
            onPress: {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = true
                }
            },
            onRelease: {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
        )
    }
}

// MARK: - Press Events Modifier
struct PressEvents: ViewModifier {
    let onPress: () -> Void
    let onRelease: () -> Void
    
    func body(content: Content) -> some View {
        content
            .simultaneousGesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { _ in
                        onPress()
                    }
                    .onEnded { _ in
                        onRelease()
                    }
            )
    }
}

extension View {
    func pressEvents(onPress: @escaping () -> Void, onRelease: @escaping () -> Void) -> some View {
        modifier(PressEvents(onPress: onPress, onRelease: onRelease))
    }
}

// MARK: - Toolbar Button
// 专门用于工具栏的按钮
struct ToolbarButton: View {
    let systemImage: String
    let action: () -> Void
    let isEnabled: Bool
    let helpText: String
    
    init(systemImage: String, action: @escaping () -> Void, isEnabled: Bool = true, helpText: String = "") {
        self.systemImage = systemImage
        self.action = action
        self.isEnabled = isEnabled
        self.helpText = helpText
    }
    
    var body: some View {
        InteractiveButton(action: action) {
            Image(systemName: systemImage)
                .foregroundColor(isEnabled ? .accentColor : .secondary)
                .frame(minWidth: 24, minHeight: 24)
                .font(.system(size: 16, weight: .medium))
        }
        .disabled(!isEnabled)
        .help(helpText)
    }
}

// MARK: - Action Button
// 用于主要操作的按钮
struct ActionButton: View {
    let title: String
    let action: () -> Void
    let style: ActionButtonStyle
    let isEnabled: Bool
    
    enum ActionButtonStyle {
        case primary
        case secondary
        case destructive
        
        var backgroundColor: Color {
            switch self {
            case .primary: return .accentColor
            case .secondary: return .gray.opacity(0.2)
            case .destructive: return .red
            }
        }
        
        var foregroundColor: Color {
            switch self {
            case .primary: return .white
            case .secondary: return .primary
            case .destructive: return .white
            }
        }
    }
    
    init(title: String, action: @escaping () -> Void, style: ActionButtonStyle = .primary, isEnabled: Bool = true) {
        self.title = title
        self.action = action
        self.style = style
        self.isEnabled = isEnabled
    }
    
    var body: some View {
        InteractiveButton(action: action) {
            Text(title)
                .foregroundColor(isEnabled ? style.foregroundColor : .secondary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(isEnabled ? style.backgroundColor : Color.gray.opacity(0.3))
                )
        }
        .disabled(!isEnabled)
    }
}

#Preview {
    VStack(spacing: 20) {
        HStack {
            ToolbarButton(systemImage: "plus", action: {}, helpText: "新建")
            ToolbarButton(systemImage: "gear", action: {}, helpText: "设置")
            ToolbarButton(systemImage: "square.and.arrow.up", action: {}, isEnabled: false, helpText: "导出")
        }
        
        VStack(spacing: 12) {
            ActionButton(title: "主要操作", action: {}, style: .primary)
            ActionButton(title: "次要操作", action: {}, style: .secondary)
            ActionButton(title: "危险操作", action: {}, style: .destructive)
            ActionButton(title: "禁用状态", action: {}, isEnabled: false)
        }
    }
    .padding()
}