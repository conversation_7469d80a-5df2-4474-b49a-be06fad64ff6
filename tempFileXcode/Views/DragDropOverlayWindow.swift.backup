import SwiftUI
import AppKit
import UniformTypeIdentifiers
import Combine

// MARK: - 拖拽覆盖窗口
class DragDropOverlayWindow: NSWindow {
    private var hostingView: NSHostingView<DragDropOverlayView>?
    
    init(contentService: ContentService, batchService: BatchService, dragDropManager: DragDropWindowManager? = nil) {
        let contentView = DragDropOverlayView(
            contentService: contentService,
            batchService: batchService,
            dragDropManager: dragDropManager
        )
        
        super.init(
            contentRect: NSRect(x: 0, y: 0, width: 400, height: 300),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )
        
        setupWindow(contentView: contentView)
    }
    
    private func setupWindow(contentView: DragDropOverlayView) {
        isReleasedWhenClosed = false
        level = .floating
        backgroundColor = NSColor.clear
        isOpaque = false
        hasShadow = true
        ignoresMouseEvents = false
        acceptsMouseMovedEvents = true
        
        // 允许窗口拖动
        isMovableByWindowBackground = true
        
        // 设置窗口位置到屏幕左侧中间
        if let screen = NSScreen.main {
            let screenFrame = screen.visibleFrame
            let windowWidth: CGFloat = 400
            let windowHeight: CGFloat = 300
            let x = screenFrame.minX + 20 // 距离左边缘20像素
            let y = screenFrame.midY - windowHeight / 2
            
            setFrame(NSRect(x: x, y: y, width: windowWidth, height: windowHeight), display: true)
        }
        
        // 设置内容视图
        hostingView = NSHostingView(rootView: contentView)
        self.contentView = hostingView
    }
    
    override var canBecomeKey: Bool {
        return true
    }
    
    override var canBecomeMain: Bool {
        return true
    }
    
    func showWindow() {
        orderFront(nil)
        makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }
    
    func hideWindow() {
        orderOut(nil)
    }
}

// MARK: - 拖拽覆盖视图
struct DragDropOverlayView: View {
    let contentService: ContentService
    let batchService: BatchService
    weak var dragDropManager: DragDropWindowManager?
    
    @State private var isTargeted = false
    @State private var draggedFiles: [URL] = []
    @State private var showingBatchSelector = false
    @State private var selectedBatch: NSManagedObject?
    @State private var isProcessing = false
    @State private var processingMessage = ""
    @State private var isRequestingPermission = false // 防止无限循环的标志
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏（可拖动区域）
            HStack {
                Image(systemName: "doc.badge.plus")
                    .foregroundColor(.accentColor)
                
                Text("拖拽文件到此处")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button(action: closeWindow) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                        .font(.system(size: 16))
                }
                .buttonStyle(.plain)
                .help("关闭窗口")
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(NSColor.controlBackgroundColor).opacity(0.8))
            
            // 主要内容区域
            VStack(spacing: 20) {
            
            // 拖拽区域
            dragDropArea
            
            // 处理状态显示
            if isProcessing {
                processingStatusView
            }
            
            // 批次选择（手动模式时显示）
            if !draggedFiles.isEmpty && !isProcessing && batchService.activeBatch == nil {
                batchSelectionArea
            }
            
            // 操作按钮（手动模式时显示）
            if !draggedFiles.isEmpty && !isProcessing && batchService.activeBatch == nil {
                actionButtons
            }
            
            // 自动模式提示
            if !draggedFiles.isEmpty && !isProcessing && batchService.activeBatch != nil {
                autoModeInfo
            }
            
            // 权限说明
            if draggedFiles.isEmpty && !isProcessing {
                permissionInfoView
            }
            }
            .padding(20)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.2), radius: 12, x: 0, y: 6)
        )
        .onAppear {
            print("🔄 DragDropOverlayView onAppear 被调用")
            print("🔍 当前状态 - isProcessing: \(isProcessing), draggedFiles.count: \(draggedFiles.count)")
            
            // 只在没有正在处理的情况下重置状态
            if !isProcessing {
                print("🔄 重置拖拽状态")
                draggedFiles.removeAll()
                processingMessage = ""
            }
            
            // 默认选择活跃批次，如果没有活跃批次则选择当前批次
            selectedBatch = batchService.activeBatch ?? batchService.currentBatch
        }
    }
    
    private var dragDropArea: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(isTargeted ? Color.accentColor.opacity(0.3) : Color.gray.opacity(0.1))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .strokeBorder(
                        isTargeted ? Color.accentColor : Color.gray.opacity(0.3),
                        style: StrokeStyle(lineWidth: 3, dash: isTargeted ? [] : [8, 4])
                    )
            )
            .overlay(
                VStack(spacing: 12) {
                    Image(systemName: draggedFiles.isEmpty ? "plus.circle.fill" : "checkmark.circle.fill")
                        .font(.system(size: 48))
                        .foregroundColor(draggedFiles.isEmpty ? (isTargeted ? .white : .accentColor) : .green)
                        .scaleEffect(isTargeted ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: isTargeted)
                    
                    if draggedFiles.isEmpty {
                        VStack(spacing: 4) {
                            Text(isTargeted ? "释放以添加文件" : "拖拽文件到此处")
                                .font(.headline)
                                .foregroundColor(isTargeted ? .white : .secondary)
                                .animation(.easeInOut(duration: 0.2), value: isTargeted)
                            
                            if !isTargeted {
                                Text("应用会自动引导您完成权限设置")
                                    .font(.caption)
                                    .foregroundColor(.secondary.opacity(0.7))
                            }
                        }
                    } else {
                        Text("已选择 \(draggedFiles.count) 个文件")
                            .font(.headline)
                            .foregroundColor(.green)
                    }
                }
            )
            .frame(height: 120)
            .onDrop(of: [UTType.fileURL.identifier, UTType.item.identifier], isTargeted: $isTargeted) { providers in
                print("拖拽检测到 \(providers.count) 个项目")
                handleDropWithPasteboard(providers: providers)
                return true
            }
    }
    
    private var processingStatusView: some View {
        VStack(spacing: 12) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .accentColor))
                .scaleEffect(1.2)
            
            Text(processingMessage)
                .font(.subheadline)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
        }
        .frame(height: 80)
    }
    
    private var autoModeInfo: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "star.fill")
                    .foregroundColor(.orange)
                    .font(.system(size: 16))
                
                Text("自动添加到活跃批次")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            
            if let activeBatch = batchService.activeBatch {
                let batchName = activeBatch.value(forKey: "name") as? String ?? "活跃批次"
                Text("目标批次: \(batchName)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.orange.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(Color.orange.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    private var batchSelectionArea: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("选择目标批次")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Menu {
                ForEach(batchService.batches, id: \.objectID) { batch in
                    let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
                    let isCurrentBatch = batch == selectedBatch
                    let isActiveBatch = batch == batchService.activeBatch
                    
                    Button(action: {
                        selectedBatch = batch
                    }) {
                        HStack {
                            Text(batchName)
                            if isActiveBatch {
                                Image(systemName: "star.fill")
                                    .foregroundColor(.orange)
                            }
                            if isCurrentBatch {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                }
            } label: {
                HStack {
                    Text(selectedBatch?.value(forKey: "name") as? String ?? "选择批次")
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.down")
                        .foregroundColor(.secondary)
                        .font(.system(size: 12))
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(NSColor.controlBackgroundColor))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                )
            }
            .menuStyle(.borderlessButton)
        }
    }
    
    private var actionButtons: some View {
        HStack(spacing: 12) {
            Button("取消") {
                // 清除所有状态
                draggedFiles.removeAll()
                isProcessing = false
                processingMessage = ""
                closeWindow()
            }
            .buttonStyle(.bordered)
            
            Button("添加到批次") {
                addFilesToBatch()
            }
            .buttonStyle(.borderedProminent)
            .disabled(selectedBatch == nil || isProcessing)
        }
    }
    
    private var permissionInfoView: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                    .font(.system(size: 14))
                
                Text("文件访问权限说明")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            
            VStack(spacing: 4) {
                Text(PermissionManager.shared.getPermissionStatusDescription())
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Text("拖拽文件时，应用会自动引导您完成权限设置。")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(10)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.blue.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(Color.blue.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 专业的拖拽处理方法（使用NSPasteboard + 权限管理器）
    private func handleDropWithPasteboard(providers: [NSItemProvider]) {
        print("🚀 开始专业拖拽处理，提供者数量: \(providers.count)")
        print("🔍 当前状态 - isProcessing: \(isProcessing), draggedFiles.count: \(draggedFiles.count)")
        
        // 如果正在处理，忽略新的拖拽
        guard !isProcessing else {
            print("⚠️ 正在处理中，忽略新的拖拽操作")
            return
        }
        
        draggedFiles.removeAll()
        
        // 使用拖拽剪贴板获取安全作用域资源
        let dragPasteboard = NSPasteboard(name: .drag)
        print("📋 拖拽剪贴板类型: \(dragPasteboard.types ?? [])")
        
        // 尝试从拖拽剪贴板获取文件URL
        if let fileURLs = dragPasteboard.readObjects(forClasses: [NSURL.self], options: nil) as? [URL] {
            print("✅ 从拖拽剪贴板获取到 \(fileURLs.count) 个URL")
            
            // 首先尝试使用已保存的权限恢复文件访问
            var authorizedURLs: [URL] = []
            var needsAuthorizationURLs: [URL] = []
            
            for url in fileURLs {
                // 尝试从书签恢复权限
                if let bookmarkURL = PermissionManager.shared.restoreFromBookmark(for: url.path) {
                    let needsSecurityScope = bookmarkURL.startAccessingSecurityScopedResource()
                    if needsSecurityScope {
                        do {
                            _ = try bookmarkURL.resourceValues(forKeys: [.fileSizeKey])
                            authorizedURLs.append(bookmarkURL)
                            print("✅ 从书签恢复权限成功: \(url.lastPathComponent)")
                            continue
                        } catch {
                            bookmarkURL.stopAccessingSecurityScopedResource()
                            print("❌ 书签权限已失效: \(url.lastPathComponent)")
                        }
                    }
                }
                
                // 尝试直接访问
                do {
                    _ = try url.resourceValues(forKeys: [.fileSizeKey])
                    authorizedURLs.append(url)
                    print("✅ 直接访问成功: \(url.lastPathComponent)")
                } catch {
                    needsAuthorizationURLs.append(url)
                    print("❌ 需要授权: \(url.lastPathComponent)")
                }
            }
            
            // 如果所有文件都已有权限，直接处理
            if needsAuthorizationURLs.isEmpty && !authorizedURLs.isEmpty {
                print("🎉 所有文件都已有权限，直接处理")
                processAuthorizedFiles(authorizedURLs)
                return
            }
            
            // 如果有部分文件需要授权
            if !needsAuthorizationURLs.isEmpty {
                print("🔐 有 \(needsAuthorizationURLs.count) 个文件需要授权，启动权限引导流程")
                PermissionManager.shared.showPermissionGuide(for: needsAuthorizationURLs) { newlyAuthorizedURLs in
                    if let newURLs = newlyAuthorizedURLs {
                        print("✅ 权限引导完成，获得 \(newURLs.count) 个新授权文件")
                        // 合并已有权限的文件和新授权的文件
                        let allAuthorizedURLs = authorizedURLs + newURLs
                        self.processAuthorizedFiles(allAuthorizedURLs)
                    } else {
                        print("❌ 权限引导取消")
                        // 如果用户取消了授权，但还有已授权的文件，询问是否继续
                        if !authorizedURLs.isEmpty {
                            self.showPartialAuthorizationDialog(authorizedCount: authorizedURLs.count, totalCount: fileURLs.count) { shouldContinue in
                                if shouldContinue {
                                    self.processAuthorizedFiles(authorizedURLs)
                                } else {
                                    self.showAuthorizationCancelledMessage()
                                }
                            }
                        } else {
                            self.showAuthorizationCancelledMessage()
                        }
                    }
                }
            } else {
                print("❌ 没有任何有效文件")
                showNoValidFilesMessage()
            }
        } else {
            print("❌ 无法从拖拽剪贴板获取文件URL，回退到原始方法")
            handleDrop(providers: providers)
        }
    }
    
    // MARK: - 处理已授权的文件
    private func processAuthorizedFiles(_ urls: [URL]) {
        print("🔐 开始处理已授权的文件: \(urls.count) 个")
        print("🔍 当前状态 - isProcessing: \(isProcessing), draggedFiles.count: \(draggedFiles.count)")
        
        // 使用权限管理器处理授权文件（创建书签）
        let processedURLs = PermissionManager.shared.processAuthorizedFiles(urls)
        var validFiles: [URL] = []
        
        for (index, url) in processedURLs.enumerated() {
            print("🔍 处理已授权文件 \(index + 1): \(url.lastPathComponent)")
            print("🔍 URL路径: \(url.path)")
            
            if url.isFileURL {
                let needsSecurityScope = url.startAccessingSecurityScopedResource()
                print("🔐 已授权文件的安全作用域状态: \(needsSecurityScope)")
                
                let fileExists = FileManager.default.fileExists(atPath: url.path)
                print("📁 已授权文件存在: \(fileExists)")
                
                if fileExists {
                    validFiles.append(url)
                    print("✅ 添加已授权文件: \(url.lastPathComponent)")
                    // 不要立即释放安全作用域访问
                } else if needsSecurityScope {
                    url.stopAccessingSecurityScopedResource()
                    print("❌ 已授权文件不存在，释放安全作用域访问")
                }
            }
        }
        
        draggedFiles = validFiles
        print("🏁 最终获取到 \(draggedFiles.count) 个已授权文件")
        
        if !draggedFiles.isEmpty {
            // 重置自动隐藏计时器
            if let windowManager = dragDropManager {
                windowManager.keepWindowVisible()
            }
            
            // 如果有活跃批次，自动添加文件
            if let activeBatch = batchService.activeBatch {
                let batchName = activeBatch.value(forKey: "name") as? String ?? "活跃批次"
                print("🎯 检测到活跃批次: \(batchName)，准备自动添加已授权文件")
                print("🎯 draggedFiles.count: \(draggedFiles.count), validFiles.count: \(validFiles.count)")
                selectedBatch = activeBatch
                
                // 立即自动添加到活跃批次
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    print("🚀 开始执行自动添加已授权文件到活跃批次")
                    print("🚀 当前 draggedFiles.count: \(self.draggedFiles.count)")
                    self.addFilesToActiveBatch()
                }
            } else {
                print("⚠️ 没有活跃批次，显示手动选择界面")
                selectedBatch = batchService.currentBatch
            }
        } else {
            print("❌ 没有获取到任何有效的已授权文件")
            showNoValidFilesMessage()
        }
    }
    
    // MARK: - 显示授权取消消息
    private func showAuthorizationCancelledMessage() {
        DispatchQueue.main.async {
            self.processingMessage = "❌ 用户取消了文件访问授权"
            self.isProcessing = false
            
            // 清除拖拽文件状态
            self.draggedFiles.removeAll()
            
            // 3秒后自动关闭窗口
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                self.closeWindow()
            }
        }
    }
    
    // MARK: - 显示无有效文件消息
    private func showNoValidFilesMessage() {
        DispatchQueue.main.async {
            self.processingMessage = "❌ 没有找到有效的文件"
            self.isProcessing = false
            
            // 清除拖拽文件状态
            self.draggedFiles.removeAll()
            
            // 3秒后自动关闭窗口
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                self.closeWindow()
            }
        }
    }
    
    // MARK: - 显示部分授权对话框
    private func showPartialAuthorizationDialog(authorizedCount: Int, totalCount: Int, completion: @escaping (Bool) -> Void) {
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "部分文件已授权"
            alert.informativeText = """
            在 \(totalCount) 个文件中，有 \(authorizedCount) 个文件已获得访问权限。
            
            您可以选择：
            • 继续处理已授权的文件
            • 取消本次操作
            """
            alert.alertStyle = .informational
            alert.addButton(withTitle: "继续处理已授权文件")
            alert.addButton(withTitle: "取消")
            
            let response = alert.runModal()
            completion(response == .alertFirstButtonReturn)
        }
    }
    
    // MARK: - 原始拖拽处理方法（作为备用）
    private func handleDrop(providers: [NSItemProvider]) {
        print("🚀 开始处理拖拽，提供者数量: \(providers.count)")
        draggedFiles.removeAll()
        
        Task {
            var collectedFiles: [URL] = []
            
            for (index, provider) in providers.enumerated() {
                print("🔍 处理提供者 \(index + 1)/\(providers.count)")
                print("📋 支持的类型: \(provider.registeredTypeIdentifiers)")
                
                // 尝试不同的类型标识符，按优先级排序
                let typeIdentifiers = [
                    UTType.fileURL.identifier,
                    "public.file-url",
                    UTType.url.identifier,
                    "public.url",
                    UTType.item.identifier
                ]
                
                var fileProcessed = false
                
                for typeId in typeIdentifiers {
                    if provider.hasItemConformingToTypeIdentifier(typeId) {
                        print("✅ 找到支持的类型: \(typeId)")
                        do {
                            let item = try await provider.loadItem(forTypeIdentifier: typeId, options: nil)
                            print("📦 加载的项目类型: \(type(of: item))")
                            
                            // 处理直接的URL对象
                            if let url = item as? URL {
                                print("🎯 直接获取URL: \(url.path)")
                                print("🎯 URL绝对字符串: \(url.absoluteString)")
                                if url.isFileURL {
                                    // 立即开始安全作用域访问，不要立即释放
                                    let needsSecurityScope = url.startAccessingSecurityScopedResource()
                                    print("🎯 开始安全作用域访问: \(needsSecurityScope)")
                                    
                                    let fileExists = FileManager.default.fileExists(atPath: url.path)
                                    print("🎯 文件存在检查: \(fileExists)")
                                    
                                    if fileExists {
                                        // 不要立即释放安全作用域访问，让它保持到文件处理完成
                                        collectedFiles.append(url)
                                        fileProcessed = true
                                        break
                                    } else if needsSecurityScope {
                                        // 只有在文件不存在时才释放
                                        url.stopAccessingSecurityScopedResource()
                                    }
                                }
                            }
                            // 处理Data类型（可能包含URL字符串）
                            else if let data = item as? Data {
                                print("📄 处理Data类型，大小: \(data.count) bytes")
                                
                                // 尝试解析为URL字符串
                                if let urlString = String(data: data, encoding: .utf8) {
                                    print("📝 解析的字符串: \(urlString)")
                                    
                                    // 处理file://协议的URL
                                    if let url = URL(string: urlString), url.isFileURL {
                                        print("🔗 从字符串创建URL: \(url.path)")
                                        let needsSecurityScope = url.startAccessingSecurityScopedResource()
                                        let fileExists = FileManager.default.fileExists(atPath: url.path)
                                        if needsSecurityScope {
                                            url.stopAccessingSecurityScopedResource()
                                        }
                                        
                                        if fileExists {
                                            collectedFiles.append(url)
                                            fileProcessed = true
                                            break
                                        }
                                    }
                                    // 处理直接的文件路径
                                    else if urlString.hasPrefix("/") {
                                        let url = URL(fileURLWithPath: urlString)
                                        print("📁 从路径创建URL: \(url.path)")
                                        let needsSecurityScope = url.startAccessingSecurityScopedResource()
                                        let fileExists = FileManager.default.fileExists(atPath: url.path)
                                        if needsSecurityScope {
                                            url.stopAccessingSecurityScopedResource()
                                        }
                                        
                                        if fileExists {
                                            collectedFiles.append(url)
                                            fileProcessed = true
                                            break
                                        }
                                    }
                                }
                                
                                // 尝试直接从Data创建URL
                                if let url = URL(dataRepresentation: data, relativeTo: nil) {
                                    print("🔄 从Data表示创建URL: \(url.path)")
                                    if url.isFileURL {
                                        let needsSecurityScope = url.startAccessingSecurityScopedResource()
                                        let fileExists = FileManager.default.fileExists(atPath: url.path)
                                        if needsSecurityScope {
                                            url.stopAccessingSecurityScopedResource()
                                        }
                                        
                                        if fileExists {
                                            collectedFiles.append(url)
                                            fileProcessed = true
                                            break
                                        }
                                    }
                                }
                            }
                            // 处理NSString类型
                            else if let urlString = item as? String {
                                print("📝 处理字符串类型: \(urlString)")
                                if let url = URL(string: urlString), url.isFileURL {
                                    let needsSecurityScope = url.startAccessingSecurityScopedResource()
                                    let fileExists = FileManager.default.fileExists(atPath: url.path)
                                    if needsSecurityScope {
                                        url.stopAccessingSecurityScopedResource()
                                    }
                                    
                                    if fileExists {
                                        collectedFiles.append(url)
                                        fileProcessed = true
                                        break
                                    }
                                }
                            }
                            
                        } catch {
                            print("❌ 加载类型 \(typeId) 失败: \(error)")
                        }
                    }
                }
                
                if !fileProcessed {
                    print("⚠️ 提供者 \(index + 1) 未能处理任何文件")
                }
            }
            
            await MainActor.run {
                draggedFiles = collectedFiles
                print("🏁 最终获取到 \(draggedFiles.count) 个文件:")
                for (index, file) in draggedFiles.enumerated() {
                    print("  \(index + 1). \(file.lastPathComponent) (\(file.path))")
                }
                
                if !draggedFiles.isEmpty {
                    // 重置自动隐藏计时器
                    if let windowManager = dragDropManager {
                        windowManager.keepWindowVisible()
                    }
                    
                    // 如果有活跃批次，自动添加文件
                    if let activeBatch = batchService.activeBatch {
                        let batchName = activeBatch.value(forKey: "name") as? String ?? "活跃批次"
                        print("🎯 检测到活跃批次: \(batchName)，准备自动添加文件")
                        selectedBatch = activeBatch
                        
                        // 立即自动添加到活跃批次
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            print("🚀 开始执行自动添加到活跃批次")
                            self.addFilesToActiveBatch()
                        }
                    } else {
                        print("⚠️ 没有活跃批次，显示手动选择界面")
                        // 如果没有活跃批次，显示选择界面
                        selectedBatch = batchService.currentBatch
                    }
                } else {
                    print("❌ 没有获取到任何有效文件")
                }
            }
        }
    }
    
    private func addFilesToBatch() {
        // 如果正在请求权限，不要开始新的文件处理
        if isRequestingPermission {
            print("⚠️ 正在请求权限，跳过文件添加")
            return
        }
        
        guard let batch = selectedBatch else { 
            print("❌ 没有选择批次")
            return 
        }
        
        isProcessing = true
        processingMessage = "正在添加文件到批次..."
        
        let batchName = batch.value(forKey: "name") as? String ?? "批次"
        print("📁 开始手动添加 \(draggedFiles.count) 个文件到批次: \(batchName)")
        
        Task {
            var successCount = 0
            
            for (index, url) in draggedFiles.enumerated() {
                do {
                    print("📄 处理文件 \(index + 1)/\(draggedFiles.count): \(url.lastPathComponent)")
                    
                    // 开始安全作用域访问并保持到处理完成
                    let needsSecurityScope = url.startAccessingSecurityScopedResource()
                    print("🔐 开始文件处理的安全作用域访问: \(needsSecurityScope)")
                    
                    defer {
                        if needsSecurityScope {
                            url.stopAccessingSecurityScopedResource()
                            print("🔐 文件处理完成，释放安全作用域访问")
                        }
                    }
                    
                    let fileExists = FileManager.default.fileExists(atPath: url.path)
                    guard fileExists else {
                        print("❌ 文件不存在: \(url.path)")
                        continue
                    }
                    
                    let contentData = try ContentData.fromFile(at: url)
                    print("✅ 成功创建ContentData: \(String(describing: contentData.title))")
                    
                    // 添加到指定批次
                    let addedContent = try await contentService.addContent(contentData, toBatch: batch)
                    successCount += 1
                    
                    print("✅ 成功添加文件到批次: \(url.lastPathComponent) -> \(addedContent.value(forKey: "title") as? String ?? "未知标题")")
                    
                    // 更新进度消息
                    await MainActor.run {
                        processingMessage = "已添加 \(successCount)/\(draggedFiles.count) 个文件..."
                    }
                    
                } catch {
                    print("❌ 添加文件失败: \(url.lastPathComponent) - \(error)")
                    NSLog("添加文件失败: \(url.lastPathComponent) - \(error)")
                    
                    // 检查是否为权限错误，如果是则标记需要权限处理
                    if let contentError = error as? ContentManagerError,
                       case .permissionDenied = contentError {
                        print("🔐 检测到权限错误，需要权限引导")
                        
                        // 检查是否已经在请求权限，避免无限循环
                        if isRequestingPermission {
                            print("⚠️ 权限请求已在进行中，跳过重复请求")
                            return
                        }
                        
                        // 立即停止当前循环并处理权限问题
                        await MainActor.run {
                            processingMessage = "检测到权限问题，启动权限引导..."
                            isProcessing = false
                            isRequestingPermission = true
                        }
                        
                        // 异步处理权限请求，不在这个Task中
                        Task {
                            await handlePermissionRequest(for: draggedFiles)
                        }
                        return // 立即返回，权限处理在新的Task中
                    }
                }
            }
            
            await MainActor.run {
                isProcessing = false
                let finalMessage = successCount > 0 ? 
                    "✅ 成功添加 \(successCount) 个文件到 \(batchName)" : 
                    "❌ 没有文件被成功添加"
                processingMessage = finalMessage
                
                print("🏁 完成手动添加操作，成功: \(successCount)/\(draggedFiles.count)")
                
                // 清除拖拽文件状态
                draggedFiles.removeAll()
                
                // 显示结果后自动关闭
                let delay = successCount > 0 ? 2.0 : 3.0
                DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                    self.closeWindow()
                }
            }
        }
    }
    
    private func addFilesToActiveBatch() {
        print("🚀 addFilesToActiveBatch 开始执行")
        print("🔍 当前 draggedFiles.count: \(draggedFiles.count)")
        
        // 如果正在请求权限，不要开始新的文件处理
        if isRequestingPermission {
            print("⚠️ 正在请求权限，跳过文件添加")
            return
        }
        
        guard let activeBatch = batchService.activeBatch else {
            print("❌ 没有活跃批次")
            return
        }
        
        guard !draggedFiles.isEmpty else {
            print("❌ 没有要处理的文件")
            return
        }
        
        isProcessing = true
        processingMessage = "正在自动添加文件到活跃批次..."
        
        let batchName = activeBatch.value(forKey: "name") as? String ?? "活跃批次"
        print("🎯 开始自动添加 \(draggedFiles.count) 个文件到活跃批次: \(batchName)")
        
        Task {
            var successCount = 0
            
            for (index, url) in draggedFiles.enumerated() {
                do {
                    print("📄 处理文件 \(index + 1)/\(draggedFiles.count): \(url.lastPathComponent)")
                    
                    // 开始安全作用域访问并保持到处理完成
                    let needsSecurityScope = url.startAccessingSecurityScopedResource()
                    print("🔐 开始文件处理的安全作用域访问: \(needsSecurityScope)")
                    
                    defer {
                        if needsSecurityScope {
                            url.stopAccessingSecurityScopedResource()
                            print("🔐 文件处理完成，释放安全作用域访问")
                        }
                    }
                    
                    let fileExists = FileManager.default.fileExists(atPath: url.path)
                    guard fileExists else {
                        print("❌ 文件不存在: \(url.path)")
                        continue
                    }
                    
                    let contentData = try ContentData.fromFile(at: url)
                    print("✅ 成功创建ContentData: \(String(describing: contentData.title))")
                    
                    // 添加到活跃批次
                    let addedContent = try await contentService.addContent(contentData, toBatch: activeBatch)
                    successCount += 1
                    
                    print("✅ 成功添加文件到活跃批次: \(url.lastPathComponent) -> \(addedContent.value(forKey: "title") as? String ?? "未知标题")")
                    
                    // 更新进度消息
                    await MainActor.run {
                        processingMessage = "已添加 \(successCount)/\(draggedFiles.count) 个文件到 \(batchName)..."
                    }
                    
                } catch {
                    print("❌ 添加文件失败: \(url.lastPathComponent) - \(error)")
                    NSLog("添加文件失败: \(url.lastPathComponent) - \(error)")
                    
                    // 检查是否为权限错误，如果是则标记需要权限处理
                    if let contentError = error as? ContentManagerError,
                       case .permissionDenied = contentError {
                        print("🔐 检测到权限错误，需要权限引导")
                        
                        // 检查是否已经在请求权限，避免无限循环
                        if isRequestingPermission {
                            print("⚠️ 权限请求已在进行中，跳过重复请求")
                            return
                        }
                        
                        // 立即停止当前循环并处理权限问题
                        await MainActor.run {
                            processingMessage = "检测到权限问题，启动权限引导..."
                            isProcessing = false
                            isRequestingPermission = true
                        }
                        
                        // 异步处理权限请求，不在这个Task中
                        Task {
                            await handlePermissionRequest(for: draggedFiles)
                        }
                        return // 立即返回，权限处理在新的Task中
                    }
                }
            }
            
            await MainActor.run {
                isProcessing = false
                let finalMessage = successCount > 0 ? 
                    "✅ 成功添加 \(successCount) 个文件到 \(batchName)" : 
                    "❌ 没有文件被成功添加"
                processingMessage = finalMessage
                
                print("🏁 完成自动添加操作，成功: \(successCount)/\(draggedFiles.count)")
                
                // 清除拖拽文件状态
                draggedFiles.removeAll()
                
                // 显示结果后自动关闭
                let delay = successCount > 0 ? 1.5 : 3.0
                DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                    self.closeWindow()
                }
            }
        }
    }
    
    // MARK: - 权限处理方法
    private func handlePermissionRequest(for files: [URL]) async {
        print("🔐 开始处理权限请求，涉及 \(files.count) 个文件")
        
        await MainActor.run {
            processingMessage = "正在请求文件访问权限..."
        }
        
        // 使用PermissionManager处理权限请求 - 直接授权拖拽的文件
        await withUnsafeContinuation { continuation in
            PermissionManager.shared.requestDirectAuthorization(for: files) { authorizedURLs in
                
                Task {
                    if let authorizedURLs = authorizedURLs, !authorizedURLs.isEmpty {
                        print("✅ 用户授权了 \(authorizedURLs.count) 个文件")
                        
                        // 验证授权的文件是否真的可以访问
                        var accessibleFiles: [URL] = []
                        for url in authorizedURLs {
                            let needsScope = url.startAccessingSecurityScopedResource()
                            defer {
                                if needsScope {
                                    url.stopAccessingSecurityScopedResource()
                                }
                            }
                            
                            do {
                                _ = try url.resourceValues(forKeys: [.fileSizeKey])
                                accessibleFiles.append(url)
                                print("✅ 验证文件可访问: \(url.lastPathComponent)")
                            } catch {
                                print("❌ 授权文件仍无法访问: \(url.lastPathComponent) - \(error)")
                            }
                        }
                        
                        if accessibleFiles.isEmpty {
                            print("❌ 没有文件可以成功访问，停止处理")
                            await MainActor.run {
                                processingMessage = "文件访问授权失败，请检查权限设置"
                                isProcessing = false
                                isRequestingPermission = false
                                
                                // 3秒后关闭窗口
                                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                                    closeWindow()
                                }
                            }
                            continuation.resume()
                            return
                        }
                        
                        // 更新拖拽文件列表为可访问的文件
                        await MainActor.run {
                            draggedFiles = accessibleFiles
                            processingMessage = "权限授权成功，继续添加文件..."
                            isRequestingPermission = false // 重置权限请求标志
                        }
                        
                        // 权限处理完成，直接处理文件（避免递归调用导致循环）
                        print("🔄 权限授权成功，开始直接处理 \(accessibleFiles.count) 个文件")
                        
                        // 确定目标批次
                        let targetBatch: NSManagedObject?
                        if let activeBatch = batchService.activeBatch {
                            targetBatch = activeBatch
                            await MainActor.run {
                                selectedBatch = activeBatch
                            }
                        } else {
                            targetBatch = selectedBatch
                        }
                        
                        guard let batch = targetBatch else {
                            print("❌ 没有可用的批次")
                            await MainActor.run {
                                processingMessage = "没有可用的批次"
                                isProcessing = false
                                isRequestingPermission = false
                                
                                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                                    closeWindow()
                                }
                            }
                            return
                        }
                        
                        // 直接处理文件，不调用原方法避免循环
                        await processFilesDirectly(accessibleFiles, toBatch: batch)
                        
                    } else {
                        print("❌ 用户取消了权限授权或没有文件被授权")
                        await MainActor.run {
                            processingMessage = "用户取消了权限授权"
                            isProcessing = false
                            isRequestingPermission = false // 重置权限请求标志
                            
                            // 3秒后关闭窗口
                            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                                closeWindow()
                            }
                        }
                    }
                    
                    continuation.resume()
                }
            }
        }
    }
    
    // MARK: - 直接文件处理方法（避免循环）
    private func processFilesDirectly(_ files: [URL], toBatch batch: NSManagedObject) async {
        print("📁 直接处理 \(files.count) 个文件到批次")
        
        let batchName = batch.value(forKey: "name") as? String ?? "批次"
        var successCount = 0
        
        await MainActor.run {
            isProcessing = true
            processingMessage = "正在处理授权文件到 \(batchName)..."
        }
        
        for (index, url) in files.enumerated() {
            do {
                print("📄 直接处理文件 \(index + 1)/\(files.count): \(url.lastPathComponent)")
                
                // 开始安全作用域访问
                let needsSecurityScope = url.startAccessingSecurityScopedResource()
                defer {
                    if needsSecurityScope {
                        url.stopAccessingSecurityScopedResource()
                    }
                }
                
                // 验证文件存在
                guard FileManager.default.fileExists(atPath: url.path) else {
                    print("❌ 文件不存在: \(url.path)")
                    continue
                }
                
                // 创建内容数据
                let contentData = try ContentData.fromFile(at: url)
                print("✅ 成功创建ContentData: \(String(describing: contentData.title))")
                
                // 添加到批次
                let addedContent = try await contentService.addContent(contentData, toBatch: batch)
                successCount += 1
                
                print("✅ 直接添加文件成功: \(url.lastPathComponent) -> \(addedContent.value(forKey: "title") as? String ?? "未知标题")")
                
                // 更新进度
                await MainActor.run {
                    processingMessage = "已处理 \(successCount)/\(files.count) 个文件到 \(batchName)..."
                }
                
            } catch {
                print("❌ 直接处理文件失败: \(url.lastPathComponent) - \(error)")
                NSLog("直接处理文件失败: \(url.lastPathComponent) - \(error)")
                
                // 不再触发权限处理，因为文件已经授权过了
                // 如果仍然失败，说明是其他问题，记录错误但继续处理其他文件
            }
        }
        
        // 完成处理
        await MainActor.run {
            isProcessing = false
            isRequestingPermission = false
            
            let finalMessage = successCount > 0 ? 
                "✅ 成功处理 \(successCount) 个文件到 \(batchName)" : 
                "❌ 没有文件被成功处理"
            processingMessage = finalMessage
            
            print("🏁 直接处理完成，成功: \(successCount)/\(files.count)")
            
            // 清除拖拽文件状态
            draggedFiles.removeAll()
            
            // 显示结果后自动关闭
            let delay = successCount > 0 ? 1.5 : 3.0
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                closeWindow()
            }
        }
    }
    
    private func closeWindow() {
        // 清除所有状态
        draggedFiles.removeAll()
        isProcessing = false
        processingMessage = ""
        isRequestingPermission = false // 重置权限请求标志
        
        dragDropManager?.hideWindow()
    }
}