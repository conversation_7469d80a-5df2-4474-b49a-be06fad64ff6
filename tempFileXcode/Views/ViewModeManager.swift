import SwiftUI
import AppKit
import Combine
import CoreData

// MARK: - 视图模式管理器
@MainActor
class ViewModeManager: ObservableObject {
    @Published var currentMode: ViewMode = .grid
    @Published var gridColumns: Int = 3
    @Published var listRowHeight: CGFloat = 44
    @Published var galleryItemSize: CGFloat = 200
    
    enum ViewMode: String, CaseIterable {
        case list = "list"
        case grid = "grid"
        case gallery = "gallery"
        case column = "column"
        case timeline = "timeline"

        var displayName: String {
            switch self {
            case .list: return "列表视图"
            case .grid: return "网格视图"
            case .gallery: return "画廊视图"
            case .column: return "分栏视图"
            case .timeline: return "时间线视图"
            }
        }

        var systemImage: String {
            switch self {
            case .list: return "list.bullet"
            case .grid: return "square.grid.2x2"
            case .gallery: return "square.grid.3x3"
            case .column: return "sidebar.left"
            case .timeline: return "clock.arrow.circlepath"
            }
        }
    }
    
    // MARK: - 视图模式切换
    
    func setViewMode(_ mode: ViewMode) {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentMode = mode
        }
    }
    
    func nextViewMode() {
        let modes = ViewMode.allCases
        if let currentIndex = modes.firstIndex(of: currentMode) {
            let nextIndex = (currentIndex + 1) % modes.count
            setViewMode(modes[nextIndex])
        }
    }
    
    // MARK: - 网格设置
    
    func increaseGridColumns() {
        gridColumns = min(gridColumns + 1, 6)
    }
    
    func decreaseGridColumns() {
        gridColumns = max(gridColumns - 1, 1)
    }
    
    // MARK: - 画廊设置
    
    func increaseGallerySize() {
        galleryItemSize = min(galleryItemSize + 20, 300)
    }
    
    func decreaseGallerySize() {
        galleryItemSize = max(galleryItemSize - 20, 100)
    }
}

// MARK: - 视图模式选择器
struct ViewModeSelector: View {
    @ObservedObject var viewModeManager: ViewModeManager
    
    var body: some View {
        HStack(spacing: 4) {
            ForEach(ViewModeManager.ViewMode.allCases, id: \.self) { mode in
                Button(action: {
                    viewModeManager.setViewMode(mode)
                }) {
                    Image(systemName: mode.systemImage)
                        .font(.system(size: 14))
                        .foregroundColor(viewModeManager.currentMode == mode ? .accentColor : .secondary)
                        .frame(width: 24, height: 24)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(viewModeManager.currentMode == mode ? Color.accentColor.opacity(0.2) : Color.clear)
                        )
                }
                .buttonStyle(.plain)
                .help(mode.displayName)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color(NSColor.controlBackgroundColor))
        )
    }
}

// MARK: - 统一内容视图
struct UnifiedContentView: View {
    let items: [ContentItem]
    @ObservedObject var viewModeManager: ViewModeManager
    @ObservedObject var selectionManager: SelectionManager
    
    let onItemSelect: (ContentItem) -> Void
    let onItemEdit: (ContentItem) -> Void
    let onItemQuickLook: (ContentItem) -> Void
    
    var body: some View {
        Group {
            switch viewModeManager.currentMode {
            case .list:
                listView
            case .grid:
                gridView
            case .gallery:
                galleryView
            case .column:
                columnView
            case .timeline:
                timelineView
            }
        }
        .onAppear {
            selectionManager.setAllItems(items)
        }
        .onChange(of: items) { _, newItems in
            selectionManager.setAllItems(newItems)
        }
    }
    
    // MARK: - 列表视图
    @ViewBuilder
    private var listView: some View {
        ScrollView {
            LazyVStack(spacing: 1) {
                ForEach(items, id: \.self.id) { item in
                    ListRowView(
                        item: item,
                        isSelected: selectionManager.isSelected(item.id?.uuidString ?? ""),
                        isMultiSelectMode: selectionManager.isMultiSelectMode,
                        onSelect: { onItemSelect(item) },
                        onToggleSelect: { 
                            selectionManager.toggleSelection(item.id?.uuidString ?? "")
                        },
                        onEdit: { onItemEdit(item) },
                        onQuickLook: { onItemQuickLook(item) }
                    )
                    .frame(height: 60) // 统一固定高度
                }
            }
        }
    }
    
    // MARK: - 网格视图
    @ViewBuilder
    private var gridView: some View {
        ScrollView {
            LazyVGrid(
                columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: viewModeManager.gridColumns),
                spacing: 12
            ) {
                ForEach(items, id: \.self.id) { item in
                    EnhancedContentCard(
                        item: item,
                        isSelected: selectionManager.isSelected(item.id?.uuidString ?? ""),
                        isMultiSelectMode: selectionManager.isMultiSelectMode,
                        onSelect: { onItemSelect(item) },
                        onToggleSelect: { 
                            selectionManager.toggleSelection(item.id?.uuidString ?? "")
                        },
                        onEdit: { onItemEdit(item) },
                        onQuickLook: { onItemQuickLook(item) }
                    )
                    .aspectRatio(0.75, contentMode: ContentMode.fit)
                }
            }
            .padding(12)
        }
    }
    
    // MARK: - 画廊视图（类似Finder画廊视图）
    @ViewBuilder
    private var galleryView: some View {
        VStack(spacing: 0) {
            // 上方：预览区域
            if let selectedPreviewItem = selectionManager.getSelectedItems().first {
                finderStylePreviewArea(item: selectedPreviewItem)
            } else {
                // 没有选中项时显示空状态
                galleryEmptyPreview
            }
            
            // 分隔线
            Divider()
            
            // 下方：缩略图列表（水平滚动）
            galleryThumbnailList
        }
    }
    
    // 画廊预览区域
    @ViewBuilder
    private func finderStylePreviewArea(item: ContentItem) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            // 预览内容
            ZStack {
                Rectangle()
                    .fill(Color(NSColor.controlBackgroundColor))
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                
                galleryContentPreview(item: item)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .clipped()
            }
            .frame(minHeight: 300, maxHeight: 400)
            .cornerRadius(8)
            .padding(.horizontal, 16)
            
            // 文件信息
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(item.displayTitle)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .lineLimit(2)
                    
                    HStack(spacing: 12) {
                        let fileCategory = getFileCategory(for: item)
                        
                        HStack(spacing: 4) {
                            Image(systemName: fileCategory.systemImage)
                                .foregroundColor(fileCategory.color)
                            Text(fileCategory.displayName)
                                .foregroundColor(fileCategory.color)
                        }
                        .font(.subheadline)
                        
                        Text(item.formattedFileSize)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text(item.formattedCreatedDate)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 快速操作按钮
                HStack(spacing: 8) {
                    Button("预览") {
                        onItemQuickLook(item)
                    }
                    .buttonStyle(.bordered)
                    
                    Button("编辑") {
                        onItemEdit(item)
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(Color(NSColor.windowBackgroundColor))
    }
    
    // 空预览状态
    @ViewBuilder
    private var galleryEmptyPreview: some View {
        VStack(spacing: 16) {
            Image(systemName: "photo.on.rectangle.angled")
                .font(.system(size: 64))
                .foregroundColor(.secondary)
            
            Text("选择一个文件来预览")
                .font(.title2)
                .foregroundColor(.secondary)
        }
        .frame(minHeight: 300, maxHeight: 400)
        .frame(maxWidth: .infinity)
        .background(Color(NSColor.windowBackgroundColor))
    }
    
    // 缩略图列表
    @ViewBuilder
    private var galleryThumbnailList: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: 8) {
                ForEach(items, id: \.self.id) { item in
                    GalleryThumbnailView(
                        item: item,
                        isSelected: selectionManager.isSelected(item.id?.uuidString ?? ""),
                        onSelect: { 
                            selectionManager.selectSingle(item.id?.uuidString ?? "")
                            onItemSelect(item) 
                        },
                        onToggleSelect: { 
                            selectionManager.toggleSelection(item.id?.uuidString ?? "")
                        }
                    )
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
        .frame(height: 120)
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    // 画廊内容预览
    @ViewBuilder
    private func galleryContentPreview(item: ContentItem) -> some View {
        let fileCategory = getFileCategory(for: item)
        
        switch fileCategory {
        case .image:
            if let data = try? item.loadFileData(), let nsImage = NSImage(data: data) {
                Image(nsImage: nsImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                galleryPlaceholderView(fileCategory: fileCategory)
            }
        case .text, .code, .document:
            if let content = item.content, !content.isEmpty {
                ScrollView {
                    Text(content)
                        .font(.system(size: 12, design: .monospaced))
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(16)
                }
                .background(Color(NSColor.textBackgroundColor))
                .cornerRadius(6)
            } else {
                galleryPlaceholderView(fileCategory: fileCategory)
            }
        default:
            galleryPlaceholderView(fileCategory: fileCategory)
        }
    }
    
    // 画廊占位符视图
    @ViewBuilder
    private func galleryPlaceholderView(fileCategory: FileTypeManager.FileCategory) -> some View {
        VStack(spacing: 16) {
            Image(systemName: fileCategory.systemImage)
                .font(.system(size: 48))
                .foregroundColor(fileCategory.color)
            
            Text(fileCategory.displayName)
                .font(.title3)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // 获取文件类别的辅助方法
    private func getFileCategory(for item: ContentItem) -> FileTypeManager.FileCategory {
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            return FileTypeManager.shared.identifyFileType(for: url)
        }
        return .unknown
    }
    
    // 检查是否为文档文件（PDF等）
    private func isDocumentFile(item: ContentItem) -> Bool {
        guard let filePath = item.filePath else { return false }
        let fileExtension = URL(fileURLWithPath: filePath).pathExtension.lowercased()
        return ["pdf"].contains(fileExtension)
    }
    
    // MARK: - 分栏视图
    @ViewBuilder
    private var columnView: some View {
        HSplitView {
            // 左侧：文件列表
            VStack(alignment: .leading, spacing: 0) {
                Text("文件列表")
                    .font(.headline)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                
                Divider()
                
                List(items, id: \.id, selection: Binding<UUID?>(
                    get: { 
                        // 将 String 转换为 UUID
                        if let firstSelectedString = selectionManager.selectedItems.first {
                            return UUID(uuidString: firstSelectedString)
                        }
                        return nil
                    },
                    set: { newValue in
                        if let itemId = newValue {
                            selectionManager.selectSingle(itemId.uuidString)
                        }
                    }
                )) { item in
                    ColumnListItem(item: item)
                        .tag(item.id)
                }
                .listStyle(.sidebar)
            }
            .frame(minWidth: 200, maxWidth: 300)
            
            // 右侧：详细预览
            VStack {
                if let selectedItem = selectionManager.getSelectedItems().first {
                    DetailPreviewView(
                        item: selectedItem,
                        onEdit: { onItemEdit(selectedItem) },
                        onQuickLook: { onItemQuickLook(selectedItem) }
                    )
                } else {
                    ContentUnavailableView(
                        "选择一个文件",
                        systemImage: "doc.text.magnifyingglass",
                        description: Text("在左侧列表中选择一个文件来查看详细信息")
                    )
                }
            }
            .frame(minWidth: 300)
        }
    }
    
    // MARK: - 时间线视图
    @ViewBuilder
    private var timelineView: some View {
        ScrollView {
            LazyVStack(alignment: .leading, spacing: 0) {
                ForEach(Array(groupedTimelineItems.enumerated()), id: \.0) { index, group in
                    VStack(alignment: .leading, spacing: 12) {
                        // 日期分组标题
                        TimelineDateHeader(dateGroup: group.key)
                        
                        // 该日期下的内容项
                        ForEach(group.value, id: \.self.id) { item in
                            TimelineItemView(
                                item: item,
                                isSelected: selectionManager.isSelected(item.id?.uuidString ?? ""),
                                isMultiSelectMode: selectionManager.isMultiSelectMode,
                                onSelect: { onItemSelect(item) },
                                onToggleSelect: { 
                                    selectionManager.toggleSelection(item.id?.uuidString ?? "")
                                },
                                onEdit: { onItemEdit(item) },
                                onQuickLook: { onItemQuickLook(item) }
                            )
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 24)
                    
                    // 添加分隔线（除了最后一组）
                    if index < groupedTimelineItems.count - 1 {
                        Divider()
                            .padding(.horizontal, 16)
                            .padding(.bottom, 16)
                    }
                }
            }
            .padding(.vertical, 16)
        }
    }
    
    // 时间线项目分组
    private var groupedTimelineItems: [(key: String, value: [ContentItem])] {
        let calendar = Calendar.current
        let now = Date()
        
        let grouped: [String: [ContentItem]] = Dictionary(grouping: items) { item -> String in
            guard let date = item.createdAt else { return "未知日期" }
            
            if calendar.isDateInToday(date) {
                return "今天"
            } else if calendar.isDateInYesterday(date) {
                return "昨天"
            } else if calendar.isDate(date, equalTo: now, toGranularity: .weekOfYear) {
                let formatter = DateFormatter()
                formatter.dateFormat = "EEEE"
                formatter.locale = Locale(identifier: "zh_CN")
                return formatter.string(from: date)
            } else if calendar.isDate(date, equalTo: now, toGranularity: .year) {
                let formatter = DateFormatter()
                formatter.dateFormat = "M月d日"
                formatter.locale = Locale(identifier: "zh_CN")
                return formatter.string(from: date)
            } else {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy年M月d日"
                formatter.locale = Locale(identifier: "zh_CN")
                return formatter.string(from: date)
            }
        }
        
        // 按日期排序分组
        return grouped.sorted { first, second in
            let firstDate = first.value.compactMap { $0.createdAt }.max() ?? Date.distantPast
            let secondDate = second.value.compactMap { $0.createdAt }.max() ?? Date.distantPast
            return firstDate > secondDate
        }
    }
}

// MARK: - 时间线组件

// 时间线日期标题
struct TimelineDateHeader: View {
    let dateGroup: String
    
    var body: some View {
        HStack {
            Text(dateGroup)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Spacer()
            
            // 日期装饰图标
            Image(systemName: dateIcon)
                .font(.system(size: 16))
                .foregroundColor(.accentColor)
        }
        .padding(.vertical, 8)
    }
    
    private var dateIcon: String {
        switch dateGroup {
        case "今天":
            return "calendar.circle.fill"
        case "昨天":
            return "calendar.circle"
        default:
            return "calendar"
        }
    }
}

// 时间线项目视图
struct TimelineItemView: View {
    let item: ContentItem
    let isSelected: Bool
    let isMultiSelectMode: Bool
    let onSelect: () -> Void
    let onToggleSelect: () -> Void
    let onEdit: () -> Void
    let onQuickLook: () -> Void
    
    @State private var isHovered = false
    private let fileTypeManager = FileTypeManager.shared
    
    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            // 时间线标记
            VStack(spacing: 0) {
                Circle()
                    .fill(timelineColor)
                    .frame(width: 12, height: 12)
                
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 2)
                    .frame(maxHeight: .infinity)
            }
            .frame(height: 60)
            
            // 内容区域
            VStack(alignment: .leading, spacing: 8) {
                // 标题和时间
                HStack(alignment: .top) {
                    VStack(alignment: .leading, spacing: 4) {
                        // 内容类型和标题
                        HStack(spacing: 8) {
                            let fileCategory = getFileCategory()
                            Image(systemName: fileCategory.systemImage)
                                .foregroundColor(fileCategory.color)
                                .font(.system(size: 14))
                            
                            Text(item.displayTitle)
                                .font(.system(size: 15, weight: .medium))
                                .lineLimit(2)
                        }
                        
                        // 内容预览
                        if let content = item.content, !content.isEmpty {
                            ExpandableContentView(content: content)
                                .padding(.leading, 22) // 对齐图标
                        }
                    }
                    
                    Spacer()
                    
                    // 时间显示
                    VStack(alignment: .trailing, spacing: 2) {
                        if let createdAt = item.createdAt {
                            Text(createdAt, style: .time)
                                .font(.system(size: 11))
                                .foregroundColor(.secondary)
                        }
                        
                        Text(item.formattedFileSize)
                            .font(.system(size: 10))
                            .foregroundColor(.secondary)
                    }
                }
                
                // 标签显示
                if !item.tagNames.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 6) {
                            ForEach(item.tagNames.prefix(5), id: \.self) { tagName in
                                Text(tagName)
                                    .font(.system(size: 10))
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(
                                        Capsule()
                                            .fill(Color.accentColor.opacity(0.15))
                                    )
                                    .foregroundColor(.accentColor)
                            }
                            
                            if item.tagNames.count > 5 {
                                Text("+\(item.tagNames.count - 5)")
                                    .font(.system(size: 10))
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.leading, 22) // 对齐图标
                    }
                }
                
                // 操作按钮（悬停时显示）
                if isHovered || isMultiSelectMode {
                    HStack(spacing: 8) {
                        if isMultiSelectMode {
                            Button(action: onToggleSelect) {
                                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                                    .foregroundColor(isSelected ? .accentColor : .secondary)
                            }
                            .buttonStyle(.plain)
                        }
                        
                        if !isMultiSelectMode {
                            Button("预览", action: onQuickLook)
                                .buttonStyle(.bordered)
                                .controlSize(.small)
                            
                            Button("编辑", action: onEdit)
                                .buttonStyle(.borderedProminent)
                                .controlSize(.small)
                        }
                        
                        Spacer()
                    }
                    .padding(.leading, 22) // 对齐图标
                    .animation(.easeInOut(duration: 0.2), value: isHovered)
                }
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.accentColor.opacity(0.1) : 
                          (isHovered ? Color.gray.opacity(0.05) : Color.clear))
                    .strokeBorder(
                        isSelected ? Color.accentColor.opacity(0.3) : Color.clear,
                        lineWidth: 1
                    )
            )
            .onHover { hovering in
                withAnimation(.easeInOut(duration: 0.2)) {
                    isHovered = hovering
                }
            }
            .onTapGesture {
                if isMultiSelectMode {
                    onToggleSelect()
                } else {
                    onSelect()
                }
            }
            .contextMenu {
                EnhancedContextMenu(
                    selectedItems: [item],
                    selectionManager: SelectionManager(),
                    onQuickLook: onQuickLook,
                    onEdit: onEdit,
                    onDelete: { /* TODO */ }
                )
            }
        }
    }
    
    private var timelineColor: Color {
        let fileCategory = getFileCategory()
        return fileCategory.color
    }
    
    private func getFileCategory() -> FileTypeManager.FileCategory {
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            return fileTypeManager.identifyFileType(for: url)
        }
        return .unknown
    }
}

// MARK: - 列表行视图
struct ListRowView: View {
    let item: ContentItem
    let isSelected: Bool
    let isMultiSelectMode: Bool
    let onSelect: () -> Void
    let onToggleSelect: () -> Void
    let onEdit: () -> Void
    let onQuickLook: () -> Void
    
    @State private var isHovered = false
    private let fileTypeManager = FileTypeManager.shared
    
    var body: some View {
        HStack(spacing: 12) {
            // 选择框（始终显示）
            Button(action: onToggleSelect) {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .accentColor : .secondary)
                    .font(.system(size: 16))
            }
            .buttonStyle(.plain)
            
            // 文件类型图标
            let fileCategory = getFileCategory()
            Image(systemName: fileCategory.systemImage)
                .foregroundColor(fileCategory.color)
                .font(.system(size: 16))
                .frame(width: 20)
            
            // 文件信息
            VStack(alignment: .leading, spacing: 2) {
                Text(item.displayTitle)
                    .font(.system(size: 13))
                    .lineLimit(1)
                    .truncationMode(.middle)
                
                Text(fileCategory.displayName)
                    .font(.system(size: 11))
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            .frame(maxHeight: 48) // 调整内容区域高度以匹配60px总高度
            
            Spacer()
            
            // 文件大小
            Text(item.formattedFileSize)
                .font(.system(size: 11))
                .foregroundColor(.secondary)
                .frame(width: 80, alignment: .trailing)
            
            // 修改时间
            Text(item.formattedCreatedDate)
                .font(.system(size: 11))
                .foregroundColor(.secondary)
                .frame(width: 120, alignment: .trailing)
            
            // 快速操作（悬停时显示）
            if isHovered {
                HStack(spacing: 4) {
                    Button(action: onQuickLook) {
                        Image(systemName: "eye")
                            .font(.system(size: 11))
                    }
                    .buttonStyle(.plain)
                    
                    Button(action: onEdit) {
                        Image(systemName: "pencil")
                            .font(.system(size: 11))
                    }
                    .buttonStyle(.plain)
                }
                .foregroundColor(.accentColor)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            Rectangle()
                .fill(isSelected ? Color.accentColor.opacity(0.2) : (isHovered ? Color.gray.opacity(0.1) : Color.clear))
        )
        .onHover { hovering in
            isHovered = hovering
        }
        .onTapGesture {
            // 单击只用于预览，不自动选择
            onSelect()
        }
        .contextMenu {
            EnhancedContextMenu(
                selectedItems: [item],
                selectionManager: SelectionManager(),
                onQuickLook: onQuickLook,
                onEdit: onEdit,
                onDelete: { /* TODO */ }
            )
        }
    }
    
    private func getFileCategory() -> FileTypeManager.FileCategory {
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            return fileTypeManager.identifyFileType(for: url)
        }
        return .unknown
    }
}

// MARK: - 画廊项目视图
struct GalleryItemView: View {
    let item: ContentItem
    let size: CGFloat
    let isSelected: Bool
    let isMultiSelectMode: Bool
    let onSelect: () -> Void
    let onToggleSelect: () -> Void
    let onEdit: () -> Void
    let onQuickLook: () -> Void
    
    @State private var isHovered = false
    private let fileTypeManager = FileTypeManager.shared
    
    var body: some View {
        VStack(spacing: 8) {
            // 主要预览区域
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(NSColor.controlBackgroundColor))
                    .frame(width: size, height: size * 0.75)
                
                // 内容预览
                contentPreview
                    .frame(width: size - 20, height: size * 0.75 - 20)
                    .clipped()
                
                // 选择覆盖层
                if isSelected {
                    RoundedRectangle(cornerRadius: 12)
                        .strokeBorder(Color.accentColor, lineWidth: 3)
                        .frame(width: size, height: size * 0.75)
                }
                
                // 选择框（始终显示）
                VStack {
                    HStack {
                        Button(action: onToggleSelect) {
                            Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                                .foregroundColor(isSelected ? .accentColor : .white)
                                .font(.system(size: 20))
                                .background(
                                    Circle()
                                        .fill(Color.black.opacity(0.3))
                                        .frame(width: 24, height: 24)
                                )
                        }
                        .buttonStyle(.plain)
                        
                        Spacer()
                    }
                    Spacer()
                }
                .padding(8)
                
                // 快速操作按钮（悬停时显示）
                if isHovered && !isMultiSelectMode {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            
                            HStack(spacing: 8) {
                                Button(action: onQuickLook) {
                                    Image(systemName: "eye")
                                        .font(.system(size: 14))
                                        .foregroundColor(.white)
                                        .frame(width: 28, height: 28)
                                        .background(Circle().fill(Color.black.opacity(0.6)))
                                }
                                .buttonStyle(.plain)
                                
                                Button(action: onEdit) {
                                    Image(systemName: "pencil")
                                        .font(.system(size: 14))
                                        .foregroundColor(.white)
                                        .frame(width: 28, height: 28)
                                        .background(Circle().fill(Color.black.opacity(0.6)))
                                }
                                .buttonStyle(.plain)
                            }
                        }
                    }
                    .padding(8)
                }
            }
            
            // 标题和信息
            VStack(spacing: 4) {
                Text(item.displayTitle)
                    .font(.system(size: 12, weight: .medium))
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
                
                let fileCategory = getFileCategory()
                Text(fileCategory.displayName)
                    .font(.system(size: 10))
                    .foregroundColor(fileCategory.color)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(fileCategory.color.opacity(0.15))
                    )
            }
            .frame(width: size)
        }
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .onTapGesture {
            // 单击只用于预览，不自动选择
            onSelect()
        }
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isSelected)
    }
    
    @ViewBuilder
    private var contentPreview: some View {
        let fileCategory = getFileCategory()
        
        switch fileCategory {
        case .image:
            if let data = try? item.loadFileData(), let nsImage = NSImage(data: data) {
                Image(nsImage: nsImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .cornerRadius(8)
            } else {
                imagePlaceholder
            }
        case .text, .code, .document:
            textPreview
        default:
            genericPreview
        }
    }
    
    @ViewBuilder
    private var textPreview: some View {
        if let content = item.content, !content.isEmpty {
            ScrollView {
                Text(content)
                    .font(.system(size: 9, design: .monospaced))
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(8)
            }
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(NSColor.textBackgroundColor))
            )
        } else {
            emptyContentPlaceholder
        }
    }
    
    @ViewBuilder
    private var genericPreview: some View {
        let fileCategory = getFileCategory()
        VStack(spacing: 8) {
            Image(systemName: fileCategory.systemImage)
                .font(.system(size: 32))
                .foregroundColor(fileCategory.color)
            
            Text(fileCategory.displayName)
                .font(.system(size: 11))
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(fileCategory.color.opacity(0.1))
        )
    }
    
    @ViewBuilder
    private var imagePlaceholder: some View {
        VStack(spacing: 8) {
            Image(systemName: "photo")
                .font(.system(size: 32))
                .foregroundColor(.purple)
            
            Text("图片文件")
                .font(.system(size: 11))
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.purple.opacity(0.1))
        )
    }
    
    @ViewBuilder
    private var emptyContentPlaceholder: some View {
        VStack(spacing: 8) {
            Image(systemName: "doc.text")
                .font(.system(size: 24))
                .foregroundColor(.secondary)
            
            Text("无预览内容")
                .font(.system(size: 10))
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(NSColor.controlBackgroundColor))
        )
    }
    
    private func getFileCategory() -> FileTypeManager.FileCategory {
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            return fileTypeManager.identifyFileType(for: url)
        }
        return .unknown
    }
}

// MARK: - 分栏列表项
struct ColumnListItem: View {
    let item: ContentItem
    private let fileTypeManager = FileTypeManager.shared
    
    var body: some View {
        HStack(spacing: 8) {
            let fileCategory = getFileCategory()
            Image(systemName: fileCategory.systemImage)
                .foregroundColor(fileCategory.color)
                .font(.system(size: 14))
                .frame(width: 16)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(item.displayTitle)
                    .font(.system(size: 12))
                    .lineLimit(1)
                
                Text(fileCategory.displayName)
                    .font(.system(size: 10))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 2)
    }
    
    private func getFileCategory() -> FileTypeManager.FileCategory {
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            return fileTypeManager.identifyFileType(for: url)
        }
        return .unknown
    }
}

// MARK: - 画廊缩略图视图
struct GalleryThumbnailView: View {
    let item: ContentItem
    let isSelected: Bool
    let onSelect: () -> Void
    let onToggleSelect: () -> Void
    
    @State private var isHovered = false
    private let fileTypeManager = FileTypeManager.shared
    
    var body: some View {
        VStack(spacing: 4) {
            // 缩略图区域
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(NSColor.controlBackgroundColor))
                    .frame(width: 80, height: 60)
                
                // 内容缩略图
                thumbnailContent
                    .frame(width: 76, height: 56)
                    .clipped()
                    .cornerRadius(6)
                
                // 选中边框
                if isSelected {
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(Color.accentColor, lineWidth: 3)
                        .frame(width: 80, height: 60)
                }
                
                // 选择框
                VStack {
                    HStack {
                        Button(action: onToggleSelect) {
                            Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                                .foregroundColor(isSelected ? .accentColor : .white)
                                .font(.system(size: 12))
                                .background(
                                    Circle()
                                        .fill(Color.black.opacity(0.4))
                                        .frame(width: 16, height: 16)
                                )
                        }
                        .buttonStyle(.plain)
                        .opacity(isHovered || isSelected ? 1 : 0)
                        
                        Spacer()
                    }
                    Spacer()
                }
                .padding(4)
            }
            
            // 文件名
            Text(item.displayTitle)
                .font(.system(size: 10))
                .lineLimit(2)
                .multilineTextAlignment(.center)
                .frame(width: 80)
        }
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .onTapGesture {
            onSelect()
        }
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
    
    @ViewBuilder
    private var thumbnailContent: some View {
        let fileCategory = getFileCategory()
        
        switch fileCategory {
        case .image:
            if let data = try? item.loadFileData(), let nsImage = NSImage(data: data) {
                Image(nsImage: nsImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else {
                thumbnailPlaceholder(fileCategory: fileCategory)
            }
        case .text, .code, .document:
            if let content = item.content, !content.isEmpty {
                Text(content)
                    .font(.system(size: 6, design: .monospaced))
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                    .padding(2)
                    .background(Color(NSColor.textBackgroundColor))
            } else {
                thumbnailPlaceholder(fileCategory: fileCategory)
            }
        default:
            thumbnailPlaceholder(fileCategory: fileCategory)
        }
    }
    
    @ViewBuilder
    private func thumbnailPlaceholder(fileCategory: FileTypeManager.FileCategory) -> some View {
        VStack(spacing: 2) {
            Image(systemName: fileCategory.systemImage)
                .font(.system(size: 16))
                .foregroundColor(fileCategory.color)
            
            if !getFileExtension().isEmpty {
                Text(getFileExtension().uppercased())
                    .font(.system(size: 6, weight: .bold))
                    .foregroundColor(fileCategory.color)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(fileCategory.color.opacity(0.1))
    }
    
    private func getFileCategory() -> FileTypeManager.FileCategory {
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            return fileTypeManager.identifyFileType(for: url)
        }
        return .unknown
    }
    
    private func getFileExtension() -> String {
        if let filePath = item.filePath {
            return URL(fileURLWithPath: filePath).pathExtension
        }
        return ""
    }
}

// MARK: - 可展开内容视图
struct ExpandableContentView: View {
    let content: String
    @State private var isExpanded = false
    
    private let maxPreviewLength = 150
    private let previewLines = 3
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 内容文本
            if isExpanded {
                expandedContent
            } else {
                collapsedContent
            }
            
            // 展开/收起按钮
            if shouldShowToggle {
                toggleButton
            }
        }
    }
    
    @ViewBuilder
    private var collapsedContent: some View {
        Text(previewText)
            .font(.system(size: 13))
            .foregroundColor(.secondary)
            .lineLimit(previewLines)
            .animation(.easeInOut(duration: 0.2), value: isExpanded)
    }
    
    @ViewBuilder
    private var expandedContent: some View {
        ScrollView {
            Text(content)
                .font(.system(size: 13))
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)
                .textSelection(.enabled)
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(NSColor.textBackgroundColor))
                        .strokeBorder(Color.secondary.opacity(0.2), lineWidth: 1)
                )
        }
        .frame(maxHeight: 200)
        .animation(.easeInOut(duration: 0.3), value: isExpanded)
    }
    
    @ViewBuilder
    private var toggleButton: some View {
        HStack {
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    isExpanded.toggle()
                }
            }) {
                HStack(spacing: 4) {
                    Text(isExpanded ? "收起" : "查看全部")
                        .font(.system(size: 12, weight: .medium))
                    
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.system(size: 10, weight: .semibold))
                        .rotationEffect(.degrees(isExpanded ? 0 : 0))
                }
                .foregroundColor(.accentColor)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.accentColor.opacity(0.1))
                        .strokeBorder(Color.accentColor.opacity(0.3), lineWidth: 1)
                )
            }
            .buttonStyle(.plain)
            .scaleEffect(isExpanded ? 1.05 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isExpanded)
            
            Spacer()
        }
    }
    
    private var previewText: String {
        if content.count <= maxPreviewLength {
            return content
        }
        
        let previewContent = String(content.prefix(maxPreviewLength))
        if let lastSpaceIndex = previewContent.lastIndex(of: " ") {
            return String(previewContent[..<lastSpaceIndex]) + "..."
        }
        return previewContent + "..."
    }
    
    private var shouldShowToggle: Bool {
        // 显示切换按钮的条件：
        // 1. 内容长度超过预览长度
        // 2. 或者内容行数超过预览行数
        return content.count > maxPreviewLength || content.components(separatedBy: .newlines).count > previewLines
    }
}

// MARK: - 详细预览视图
struct DetailPreviewView: View {
    let item: ContentItem
    let onEdit: () -> Void
    let onQuickLook: () -> Void
    
    private let fileTypeManager = FileTypeManager.shared
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 标题和操作
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(item.displayTitle)
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        let fileCategory = getFileCategory()
                        Text(fileCategory.displayName)
                            .font(.subheadline)
                            .foregroundColor(fileCategory.color)
                    }
                    
                    Spacer()
                    
                    HStack(spacing: 8) {
                        Button("预览", action: onQuickLook)
                            .buttonStyle(.bordered)
                        
                        Button("编辑", action: onEdit)
                            .buttonStyle(.borderedProminent)
                    }
                }
                
                Divider()
                
                // 内容预览
                contentPreview
                
                Divider()
                
                // 文件信息
                fileInfo
            }
            .padding(20)
        }
    }
    
    @ViewBuilder
    private var contentPreview: some View {
        let fileCategory = getFileCategory()
        
        switch fileCategory {
        case .text, .code, .document:
            if let content = item.content, !content.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("内容预览")
                        .font(.headline)
                    
                    ScrollView {
                        Text(content)
                            .font(.system(size: 12, design: .monospaced))
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(12)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color(NSColor.textBackgroundColor))
                            )
                    }
                    .frame(maxHeight: 300)
                }
            }
        case .image:
            if let data = try? item.loadFileData(), let nsImage = NSImage(data: data) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("图片预览")
                        .font(.headline)
                    
                    Image(nsImage: nsImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 400)
                        .cornerRadius(8)
                }
            }
        default:
            VStack(alignment: .leading, spacing: 8) {
                Text("文件信息")
                    .font(.headline)
                
                Text("此文件类型暂不支持预览")
                    .foregroundColor(.secondary)
            }
        }
    }
    
    @ViewBuilder
    private var fileInfo: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("文件详情")
                .font(.headline)
            
            InfoRow(label: "文件大小", value: item.formattedFileSize)
            InfoRow(label: "创建时间", value: item.formattedCreatedDate)
            
            if let filePath = item.filePath {
                InfoRow(label: "文件路径", value: filePath)
                
                let url = URL(fileURLWithPath: filePath)
                InfoRow(label: "文件扩展名", value: url.pathExtension.isEmpty ? "无" : url.pathExtension.uppercased())
            }
            
            let fileCategory = getFileCategory()
            InfoRow(label: "文件类型", value: fileCategory.displayName)
        }
    }
    
    private func getFileCategory() -> FileTypeManager.FileCategory {
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            return fileTypeManager.identifyFileType(for: url)
        }
        return .unknown
    }
}

// MARK: - 信息行组件
struct InfoRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .font(.system(size: 12))
                .foregroundColor(.secondary)
                .frame(width: 80, alignment: .leading)
            
            Text(value)
                .font(.system(size: 12))
                .textSelection(.enabled)
            
            Spacer()
        }
    }
}