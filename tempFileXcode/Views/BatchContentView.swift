import SwiftUI
import CoreData
import UniformTypeIdentifiers

// MARK: - Batch Content View
struct BatchContentView: View {
    @EnvironmentObject private var batchService: BatchService
    @EnvironmentObject private var contentService: ContentService
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var dragDropManager: DragDropWindowManager
    @EnvironmentObject private var pasteboardMonitor: PasteboardMonitor
    

    @State private var showingBatchSelector = false
    @State private var showingBatchSettings = false
    @State private var editingItem: ContentItem?
    @State private var showingEditSheet = false
    @State private var showingExportSheet = false
    @State private var settingsWindowController: SettingsWindowController?
    
    var body: some View {
        HStack(spacing: 0) {
            // 侧边栏
            BatchSidebarView()
                .frame(width: 250)
                .background(Color(NSColor.controlBackgroundColor))
                .overlay(
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 1)
                        .frame(maxHeight: .infinity),
                    alignment: .trailing
                )
            
            Divider()
            
            // 主内容区域
            VStack(spacing: 0) {
                // 现代化批次选择器
                ModernBatchSelector()
                
                // 内容列表
                contentListView
            }
        }
        .sheet(isPresented: $showingEditSheet) {
            if let item = editingItem {
                ContentEditView(item: item)
            }
        }
        .sheet(isPresented: $showingExportSheet) {
            ExportSheet(selectedItems: [])
        }
        .contentDropHandler(
            contentService: contentService,
            pasteboardMonitor: pasteboardMonitor,
            batchService: batchService
        )
        .toolbar {
            ToolbarItemGroup(placement: .primaryAction) {
                ToolbarButton(
                    systemImage: "square.and.arrow.up",
                    action: { showingExportSheet.toggle() },
                    isEnabled: batchService.currentBatch != nil,
                    helpText: "导出内容"
                )
                
                ToolbarButton(
                    systemImage: "gear",
                    action: showSettings,
                    helpText: "设置"
                )
            }
        }
    }
    

    
    // MARK: - Content List
    private var contentListView: some View {
        VStack {
            if let currentBatch = batchService.currentBatch {
                ModernContentView(batch: currentBatch, selectedItems: .constant(Set<ContentItem>())) { item in
                    editingItem = item
                    showingEditSheet = true
                }
            } else {
                emptyBatchView
            }
        }
        .background(Color(NSColor.textBackgroundColor))
    }
    
    private var emptyBatchView: some View {
        VStack(spacing: 20) {
            // 图标
            Image(systemName: "tray.2")
                .font(.system(size: 64))
                .foregroundColor(.accentColor.opacity(0.6))
            
            // 主标题
            Text("选择或创建一个批次")
                .font(.title)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            // 描述文字
            VStack(spacing: 8) {
                Text("批次用于组织和管理相关的内容")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Text("您可以通过拖拽文件或复制内容来添加项目")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // 操作按钮
            VStack(spacing: 12) {
                ActionButton(
                    title: "创建新批次",
                    action: createNewBatch,
                    style: .primary
                )
                
                HStack(spacing: 16) {
                    ActionButton(
                        title: "快速粘贴",
                        action: {
                            // TODO: 打开快速粘贴窗口
                        },
                        style: .secondary
                    )
                    
                    ActionButton(
                        title: "选择文件",
                        action: {
                            // TODO: 打开文件选择器
                        },
                        style: .secondary
                    )
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(40)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(NSColor.controlBackgroundColor))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
        .padding(20)
    }
    
    private func createNewBatch() {
        DispatchQueue.main.async {
            do {
                let newBatch = try self.batchService.createNewBatch(name: nil, notes: nil)
                self.batchService.setCurrentBatch(newBatch)
            } catch {
                // 显示错误提示给用户
                NSLog("Failed to create new batch: \(error)")
            }
        }
    }
    
    private func showSettings() {
        if settingsWindowController == nil {
            settingsWindowController = SettingsWindowController()
        }
        settingsWindowController?.showWindow()
    }
    

}

// MARK: - Batch Contents View
struct BatchContentsView: View {
    let batch: NSManagedObject
    @Binding var selectedItems: Set<ContentItem>
    let onItemEdit: (ContentItem) -> Void
    
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var searchService: SearchService
    @FetchRequest private var contentItems: FetchedResults<ContentItem>
    @State private var filteredItems: [ContentItem] = []
    
    init(batch: NSManagedObject, selectedItems: Binding<Set<ContentItem>>, onItemEdit: @escaping (ContentItem) -> Void) {
        self.batch = batch
        self._selectedItems = selectedItems
        self.onItemEdit = onItemEdit
        
        // 创建针对特定批次的请求
        let request: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
        request.predicate = NSPredicate(format: "batch == %@", batch)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)]
        
        self._contentItems = FetchRequest(fetchRequest: request)
    }
    
    var body: some View {
        Group {
            if contentItems.isEmpty {
                emptyContentView
            } else if filteredItems.isEmpty && !appState.searchText.isEmpty {
                emptySearchView
            } else {
                if appState.selectedViewMode == .type {
                    typeGroupedView
                } else {
                    contentGridView
                }
            }
        }
        .onAppear {
            updateFilteredItems()
        }
        .onChange(of: appState.searchText) { _, _ in
            updateFilteredItems()
        }
        .onChange(of: appState.selectedTags) { _, _ in
            updateFilteredItems()
        }
        .onChange(of: contentItems.count) { _, _ in
            updateFilteredItems()
        }
    }
    
    private var emptyContentView: some View {
        VStack(spacing: 20) {
            // 动画图标
            Image(systemName: "doc.badge.plus")
                .font(.system(size: 48))
                .foregroundColor(.accentColor)
                .symbolEffect(.bounce, options: .repeat(.continuous))
            
            // 标题
            Text("批次为空")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            // 描述
            VStack(spacing: 8) {
                Text("开始添加您的第一个内容项目")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                Text("支持拖拽文件、复制文本或使用快捷键")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .multilineTextAlignment(.center)
            
            // 快速操作
            HStack(spacing: 12) {
                ActionButton(
                    title: "选择文件",
                    action: {
                        // TODO: 实现文件选择
                    },
                    style: .primary
                )
                
                ActionButton(
                    title: "快速粘贴 ⌘⇧V",
                    action: {
                        // TODO: 打开快速粘贴
                    },
                    style: .secondary
                )
            }
        }
        .frame(minHeight: 300)
        .frame(maxWidth: .infinity)
        .padding(30)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(NSColor.controlBackgroundColor))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .strokeBorder(Color.accentColor.opacity(0.3), style: StrokeStyle(lineWidth: 2, dash: [8, 4]))
                )
        )
        .padding(20)
    }
    
    private var contentGridView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: 250), spacing: 12)
            ], spacing: 12) {
                ForEach(filteredItems, id: \.id) { item in
                    ContentItemCard(item: item, isSelected: false) {
                        // 不再需要选择功能
                    } onEdit: {
                        onItemEdit(item)
                    }
                }
            }
            .padding()
        }
    }
    
    private var typeGroupedView: some View {
        let groupedItems = Dictionary(grouping: filteredItems) { $0.contentTypeEnum.displayName }
        
        return ScrollView {
            LazyVStack(alignment: .leading, spacing: 16) {
                ForEach(groupedItems.keys.sorted(), id: \.self) { typeName in
                    if let items = groupedItems[typeName] {
                        VStack(alignment: .leading, spacing: 8) {
                            Text(typeName)
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            LazyVGrid(columns: [
                                GridItem(.adaptive(minimum: 250), spacing: 12)
                            ], spacing: 12) {
                                ForEach(items, id: \.id) { item in
                                    ContentItemCard(item: item, isSelected: false) {
                                        // 不再需要选择功能
                                    } onEdit: {
                                        onItemEdit(item)
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .padding()
        }
    }
    
    private var emptySearchView: some View {
        VStack(spacing: 20) {
            // 搜索图标
            Image(systemName: "magnifyingglass.circle")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            // 标题
            Text("未找到匹配内容")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            // 建议
            VStack(spacing: 8) {
                Text("尝试以下操作：")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("• 检查搜索词的拼写")
                    Text("• 使用更简单的关键词")
                    Text("• 清除过滤条件")
                    Text("• 切换到其他批次")
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
            }
            
            // 清除按钮
            ActionButton(
                title: "清除搜索和过滤",
                action: {
                    appState.searchText = ""
                    appState.selectedTags.removeAll()
                },
                style: .secondary
            )
        }
        .frame(minHeight: 300)
        .frame(maxWidth: .infinity)
        .padding(30)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(NSColor.controlBackgroundColor))
        )
        .padding(20)
    }
    
    private func updateFilteredItems() {
        var items = Array(contentItems)
        
        // 首先应用搜索文本过滤
        if !appState.searchText.isEmpty {
            items = searchService.searchContent(query: appState.searchText, in: items)
        }
        
        // 应用内容类型过滤
        if !appState.selectedContentTypes.isEmpty {
            items = items.filter { item in
                appState.selectedContentTypes.contains(item.contentTypeEnum)
            }
        }
        
        // 然后应用标签过滤
        if !appState.selectedTags.isEmpty {
            items = items.filter { item in
                let itemTags = Set(item.tagNames)
                return !appState.selectedTags.isDisjoint(with: itemTags)
            }
        }
        
        filteredItems = items
    }
}

// MARK: - Content Item Card
struct ContentItemCard: View {
    let item: ContentItem
    let isSelected: Bool
    let onSelect: () -> Void
    let onEdit: () -> Void
    
    @EnvironmentObject private var contentService: ContentService
    @State private var showingDetailView = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 文件类型图标和标题
            HStack {
                Image(systemName: item.contentTypeEnum.systemImage)
                    .foregroundColor(Color(tagColor: item.contentTypeEnum.color))
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(item.displayTitle)
                        .font(.headline)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                    
                    Text(item.contentTypeEnum.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 查看详情按钮
                Button(action: showFullContent) {
                    Image(systemName: "eye")
                        .foregroundColor(.accentColor)
                        .font(.title3)
                }
                .buttonStyle(.plain)
                .help("查看完整内容")
            }
            
            // 内容预览
            contentPreview
            
            // 文件路径（如果存在）
            if let filePath = item.filePath {
                Text("路径: \(filePath)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                    .padding(.top, 4)
            }
            
            // 底部信息
            HStack {
                Text(item.formattedCreatedDate)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text(item.formattedFileSize)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(NSColor.controlBackgroundColor))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
        .sheet(isPresented: $showingDetailView) {
            ContentDetailView(item: item)
        }
        .contextMenu {
            Button("编辑") {
                onEdit()
            }
            
            Button("查看完整内容") {
                showFullContent()
            }
            
            Divider()
            
            if item.contentTypeEnum == .file || item.contentTypeEnum == .image {
                Button("在Finder中显示") {
                    showInFinder()
                }
                
                Button("复制文件") {
                    copyFile()
                }
            }
            
            Button("复制路径") {
                if let filePath = item.filePath {
                    NSPasteboard.general.clearContents()
                    NSPasteboard.general.setString(filePath, forType: .string)
                }
            }
            
            Button("分享") {
                shareItem()
            }
            
            Divider()
            
            Button("删除") {
                deleteItem()
            }
        }
    }
    
    private func showFullContent() {
        showingDetailView = true
    }
    
    private func showInFinder() {
        if let filePath = item.filePath {
            NSWorkspace.shared.selectFile(filePath, inFileViewerRootedAtPath: "")
        }
    }
    
    private func copyFile() {
        if let filePath = item.filePath {
            let pasteboard = NSPasteboard.general
            pasteboard.clearContents()
            pasteboard.setString(URL(fileURLWithPath: filePath).absoluteString, forType: .fileURL)
        }
    }
    
    private func shareItem() {
        if let filePath = item.filePath {
            let sharingPicker = NSSharingServicePicker(items: [URL(fileURLWithPath: filePath)])
            if let view = NSApp.keyWindow?.contentView {
                sharingPicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
            }
        } else if let content = item.content {
            let sharingPicker = NSSharingServicePicker(items: [content])
            if let view = NSApp.keyWindow?.contentView {
                sharingPicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
            }
        }
    }
    
    private func deleteItem() {
        Task {
            do {
                try await contentService.deleteContent(item)
            } catch {
                NSLog("删除失败: \(error)")
            }
        }
    }
    
    @ViewBuilder
    private var contentPreview: some View {
        switch item.contentTypeEnum {
        case .text:
            if let content = item.content {
                VStack(alignment: .leading, spacing: 4) {
                    Text(content)
                        .font(.body)
                        .lineLimit(3)
                        .padding(8)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                        .onTapGesture {
                            // 复制文本内容到剪贴板
                            NSPasteboard.general.clearContents()
                            NSPasteboard.general.setString(content, forType: .string)
                            // 文本已复制到剪贴板
                        }
                    
                    Button("复制文本") {
                        NSPasteboard.general.clearContents()
                        NSPasteboard.general.setString(content, forType: .string)
                        // 文本已通过按钮复制
                    }
                    .buttonStyle(.bordered)
                    .font(.caption)
                }
            }
            
        case .image:
            if let filePath = item.filePath,
               let image = NSImage(contentsOfFile: filePath) {
                VStack(spacing: 4) {
                    Image(nsImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 120)
                        .cornerRadius(8)
                        .clipped()
                    
                    if let fileName = item.fileName {
                        Text(fileName)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                            .truncationMode(.middle)
                    }
                }
            } else {
                VStack(spacing: 8) {
                    Image(systemName: "photo")
                        .font(.system(size: 40))
                        .foregroundColor(.secondary)
                    
                    Text("图片加载失败")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .frame(height: 100)
            }
            
        case .file:
            VStack(spacing: 8) {
                Image(systemName: "doc.fill")
                    .font(.system(size: 32))
                    .foregroundColor(.accentColor)
                
                if let fileName = item.fileName {
                    Text(fileName)
                        .font(.caption)
                        .lineLimit(3)
                        .multilineTextAlignment(.center)
                        .fixedSize(horizontal: false, vertical: true)
                        .padding(.horizontal, 4)
                }
                
                // 显示文件大小
                Text(item.formattedFileSize)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(minHeight: 100)
            .frame(maxWidth: .infinity)
        }
    }
}

// MARK: - Batch Selector Popover
struct BatchSelectorPopover: View {
    @EnvironmentObject private var batchService: BatchService
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("选择批次")
                .font(.headline)
                .padding(.bottom, 4)
            
            ForEach(batchService.batches, id: \.objectID) { batch in
                let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
                let isCurrentBatch = batch == batchService.currentBatch
                
                Button(action: {
                    batchService.setCurrentBatch(batch)
                    dismiss()
                }) {
                    HStack {
                        Image(systemName: isCurrentBatch ? "checkmark.circle.fill" : "circle")
                            .foregroundColor(isCurrentBatch ? .accentColor : .secondary)
                        
                        Text(batchName)
                            .foregroundColor(.primary)
                        
                        Spacer()
                    }
                    .padding(.vertical, 4)
                }
                .buttonStyle(.plain)
            }
            
            if batchService.batches.isEmpty {
                Text("没有可用的批次")
                    .foregroundColor(.secondary)
                    .font(.subheadline)
            }
        }
        .padding()
        .frame(minWidth: 200)
    }
}




