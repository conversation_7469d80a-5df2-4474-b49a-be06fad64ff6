import SwiftUI
import CoreData
import UniformTypeIdentifiers
import QuickLook

// MARK: - 新的批次内容视图
struct NewBatchContentView: View {
    @EnvironmentObject private var batchService: BatchService
    @EnvironmentObject private var contentService: ContentService
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var dragDropManager: DragDropWindowManager
    @EnvironmentObject private var pasteboardMonitor: PasteboardMonitor
    @EnvironmentObject private var searchService: SearchService
    
    @State private var showingExportSheet = false
    @State private var settingsWindowController: SettingsWindowController?
    @State private var searchText = ""
    @State private var viewMode: ContentViewMode = .grid
    @State private var sortOrder: SortOrder = .dateDescending
    
    enum ContentViewMode: String, CaseIterable {
        case grid = "grid"
        case list = "list"
        case columns = "columns"
        case gallery = "gallery"
        case timeline = "timeline"
        
        var displayName: String {
            switch self {
            case .grid: return "网格视图"
            case .list: return "列表视图"
            case .columns: return "分栏视图"
            case .gallery: return "画廊视图"
            case .timeline: return "时间线视图"
            }
        }
        
        var systemImage: String {
            switch self {
            case .grid: return "square.grid.2x2"
            case .list: return "list.bullet"
            case .columns: return "rectangle.split.3x1"
            case .gallery: return "photo.on.rectangle.angled"
            case .timeline: return "clock.arrow.circlepath"
            }
        }
    }
    
    enum SortOrder: String, CaseIterable {
        case dateDescending = "date_desc"
        case dateAscending = "date_asc"
        case nameAscending = "name_asc"
        case nameDescending = "name_desc"
        case sizeAscending = "size_asc"
        case sizeDescending = "size_desc"
        
        var displayName: String {
            switch self {
            case .dateDescending: return "最新优先"
            case .dateAscending: return "最旧优先"
            case .nameAscending: return "名称 A-Z"
            case .nameDescending: return "名称 Z-A"
            case .sizeAscending: return "大小递增"
            case .sizeDescending: return "大小递减"
            }
        }
    }
    
    var body: some View {
        HStack(spacing: 0) {
            // 左侧批次管理面板
            BatchSidebarView()
                .frame(width: 280)
                .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            // 右侧主内容区域
            VStack(spacing: 0) {
                // 顶部工具栏
                topToolbar
                
                Divider()
                
                // 内容区域
                contentArea
            }
            .background(Color(NSColor.textBackgroundColor))
        }
        .sheet(isPresented: $showingExportSheet) {
            ExportSheet(selectedItems: [])
        }
        .contentDropHandler(
            contentService: contentService,
            pasteboardMonitor: pasteboardMonitor,
            batchService: batchService
        )
        .toolbar {
            ToolbarItemGroup(placement: .primaryAction) {
                ToolbarButton(
                    systemImage: "gear",
                    action: showSettings,
                    helpText: "设置"
                )
            }
        }
    }
    
    // MARK: - 顶部工具栏
    private var topToolbar: some View {
        HStack(spacing: 16) {
            // 左侧：当前批次信息
            if let currentBatch = batchService.currentBatch {
                currentBatchInfo(currentBatch)
            } else {
                Text("未选择批次")
                    .font(.headline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 中间：搜索栏
            searchBar
            
            Spacer()
            
            // 右侧：视图控制和全选按钮
            HStack(spacing: 16) {
                // 全选按钮
                Button(action: {
                    // 通过环境对象访问选择管理器
                    if batchService.currentBatch != nil {
                        // 这里需要通过通知或其他方式触发全选
                        NotificationCenter.default.post(name: NSNotification.Name("SelectAllItems"), object: nil)
                    }
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "checkmark.square")
                        Text("全选")
                    }
                    .font(.system(size: 12))
                    .foregroundColor(.accentColor)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.accentColor.opacity(0.1))
                    )
                }
                .buttonStyle(.plain)
                .help("全选所有项目 (⌘A)")
                
                viewControls
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    private func currentBatchInfo(_ batch: NSManagedObject) -> some View {
        HStack(spacing: 12) {
            Image(systemName: "folder.fill")
                .foregroundColor(.accentColor)
                .font(.system(size: 18))
            
            VStack(alignment: .leading, spacing: 2) {
                let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
                let itemCount = (batch.value(forKey: "contentItems") as? NSSet)?.count ?? 0
                
                Text(batchName)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text("\(itemCount) 个项目")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索内容...", text: $searchText)
                .textFieldStyle(.plain)
                .onChange(of: searchText) { _, newValue in
                    appState.searchText = newValue
                }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(NSColor.textBackgroundColor))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
        .frame(maxWidth: 300)
    }
    
    private var viewControls: some View {
        HStack(spacing: 12) {
            // 排序菜单
            Menu {
                ForEach(SortOrder.allCases, id: \.self) { order in
                    Button(action: {
                        sortOrder = order
                    }) {
                        HStack {
                            Text(order.displayName)
                            if sortOrder == order {
                                Image(systemName: "checkmark")
                            }
                        }
                    }
                }
            } label: {
                Image(systemName: "arrow.up.arrow.down")
                    .foregroundColor(.accentColor)
                    .font(.system(size: 16))
            }
            .menuStyle(.borderlessButton)
            .help("排序方式")
            
            // 视图模式切换
            HStack(spacing: 4) {
                ForEach(ContentViewMode.allCases, id: \.self) { mode in
                    Button(action: {
                        viewMode = mode
                    }) {
                        Image(systemName: mode.systemImage)
                            .foregroundColor(viewMode == mode ? .accentColor : .secondary)
                            .font(.system(size: 14))
                    }
                    .buttonStyle(.plain)
                    .help(mode.displayName)
                }
            }
        }
    }
    
    // MARK: - 内容区域
    @ViewBuilder
    private var contentArea: some View {
        if let currentBatch = batchService.currentBatch {
            OptimizedContentView(
                batch: currentBatch,
                viewMode: viewMode,
                sortOrder: sortOrder
            )
        } else {
            emptyBatchView
        }
    }
    
    private var emptyBatchView: some View {
        VStack(spacing: 24) {
            // 图标
            Image(systemName: "tray.2.fill")
                .font(.system(size: 64))
                .foregroundColor(.accentColor.opacity(0.6))
            
            // 主标题
            Text("选择或创建一个批次")
                .font(.title)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            // 描述文字
            VStack(spacing: 8) {
                Text("批次用于组织和管理相关的内容")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Text("您可以通过拖拽文件或复制内容来添加项目")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // 操作按钮
            VStack(spacing: 12) {
                ActionButton(
                    title: "创建新批次",
                    action: createNewBatch,
                    style: .primary
                )
                
                HStack(spacing: 16) {
                    ActionButton(
                        title: "快速粘贴",
                        action: {
                            // TODO: 打开快速粘贴窗口
                        },
                        style: .secondary
                    )
                    
                    ActionButton(
                        title: "选择文件",
                        action: {
                            // TODO: 打开文件选择器
                        },
                        style: .secondary
                    )
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(40)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(NSColor.controlBackgroundColor))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
        .padding(40)
    }
    
    private func createNewBatch() {
        DispatchQueue.main.async {
            do {
                let newBatch = try self.batchService.createNewBatch(name: nil, notes: nil)
                self.batchService.setCurrentBatch(newBatch)
            } catch {
                NSLog("Failed to create new batch: \(error)")
            }
        }
    }
    
    private func showSettings() {
        if settingsWindowController == nil {
            settingsWindowController = SettingsWindowController()
        }
        settingsWindowController?.showWindow()
    }
}

// MARK: - 优化的内容视图
struct OptimizedContentView: View {
    let batch: NSManagedObject
    let viewMode: NewBatchContentView.ContentViewMode
    let sortOrder: NewBatchContentView.SortOrder
    
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var searchService: SearchService
    @EnvironmentObject private var contentService: ContentService
    
    @FetchRequest private var contentItems: FetchedResults<ContentItem>
    @State private var filteredItems: [ContentItem] = []
    @State private var quickLookURL: URL?
    @StateObject private var selectionManager = SelectionManager()
    
    init(batch: NSManagedObject, viewMode: NewBatchContentView.ContentViewMode, sortOrder: NewBatchContentView.SortOrder) {
        self.batch = batch
        self.viewMode = viewMode
        self.sortOrder = sortOrder
        
        let request: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
        request.predicate = NSPredicate(format: "batch == %@", batch)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)]
        
        self._contentItems = FetchRequest(fetchRequest: request)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 多选工具栏
            if selectionManager.hasSelection {
                MultiSelectionToolbar(
                    selectionManager: selectionManager,
                    onDelete: deleteSelectedItems,
                    onExport: exportSelectedItems,
                    onCopyPaths: {
                        selectionManager.copySelectedFilePaths()
                    }
                )
                .padding(.horizontal, 20)
                .padding(.vertical, 8)
                
                Divider()
            }
            
            // 主内容区域
            Group {
                if contentItems.isEmpty {
                    emptyContentView
                } else if filteredItems.isEmpty && !appState.searchText.isEmpty {
                    emptySearchView
                } else {
                    switch viewMode {
                    case .grid:
                        gridView
                    case .list:
                        listView
                    case .columns:
                        columnsView
                    case .gallery:
                        galleryView
                    case .timeline:
                        timelineView
                    }
                }
            }
        }
        .onAppear {
            updateFilteredItems()
            selectionManager.setAllItems(Array(contentItems))
            
            // 监听全选通知
            NotificationCenter.default.addObserver(
                forName: NSNotification.Name("SelectAllItems"),
                object: nil,
                queue: .main
            ) { _ in
                Task { @MainActor in
                    selectionManager.selectAll()
                }
            }
        }
        .onDisappear {
            // 移除通知监听
            NotificationCenter.default.removeObserver(self, name: NSNotification.Name("SelectAllItems"), object: nil)
        }
        .onChange(of: appState.searchText) { _, _ in
            updateFilteredItems()
        }
        .onChange(of: contentItems.count) { _, _ in
            updateFilteredItems()
            selectionManager.setAllItems(Array(contentItems))
        }
        .onChange(of: sortOrder) { _, _ in
            updateFilteredItems()
        }
        .quickLookPreview($quickLookURL)
        .background(SelectionKeyboardHandler(selectionManager: selectionManager))
    }
    
    // MARK: - 网格视图
    private var gridView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: 280), spacing: 16)
            ], spacing: 16) {
                ForEach(filteredItems, id: \.id) { item in
                    OptimizedContentCard(
                        item: item,
                        onQuickLook: { showQuickLook(for: item) },
                        isSelected: selectionManager.isSelected(item.id?.uuidString ?? ""),
                        onSelectionChange: { selected in
                            handleItemSelection(item: item, selected: selected)
                        }
                    )
                }
            }
            .padding(20)
        }
    }
    
    // MARK: - 列表视图
    private var listView: some View {
        ScrollView {
            LazyVStack(spacing: 1) {
                ForEach(filteredItems, id: \.id) { item in
                    OptimizedContentRow(
                        item: item,
                        onQuickLook: { showQuickLook(for: item) },
                        isSelected: selectionManager.isSelected(item.id?.uuidString ?? ""),
                        onSelectionChange: { selected in
                            handleItemSelection(item: item, selected: selected)
                        }
                    )
                    
                    if item != filteredItems.last {
                        Divider()
                            .padding(.horizontal, 16)
                    }
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - 分栏视图
    private var columnsView: some View {
        HStack(spacing: 0) {
            // 左侧文件列表
            ScrollView {
                LazyVStack(spacing: 1) {
                    ForEach(filteredItems, id: \.id) { item in
                        EnhancedColumnFileRow(
                            item: item,
                            isSelected: selectionManager.isSelected(item.id?.uuidString ?? ""),
                            onSelectionChange: { selected in
                                handleItemSelection(item: item, selected: selected)
                            }
                        )
                        
                        if item != filteredItems.last {
                            Divider()
                        }
                    }
                }
            }
            .frame(width: 300)
            .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            // 右侧内容预览
            if let selectedItem = getSelectedItem() {
                EnhancedColumnContentPreview(item: selectedItem)
            } else {
                VStack(spacing: 16) {
                    Image(systemName: "doc.text.magnifyingglass")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    
                    Text("选择一个项目查看内容")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    
                    Text("在左侧列表中点击任意项目")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(NSColor.textBackgroundColor))
            }
        }
    }
    
    // MARK: - 画廊视图
    private var galleryView: some View {
        VStack(spacing: 0) {
            // 文件网格列表 - 移除上方空白区域，直接显示内容
            ScrollView {
                LazyVGrid(columns: [
                    GridItem(.adaptive(minimum: 200), spacing: 16)
                ], spacing: 16) {
                    ForEach(filteredItems, id: \.id) { item in
                        EnhancedGalleryFileItem(
                            item: item,
                            isSelected: selectionManager.isSelected(item.id?.uuidString ?? ""),
                            onSelectionChange: { selected in
                                handleItemSelection(item: item, selected: selected)
                            }
                        )
                        .frame(height: 180) // 固定高度，保持一致性
                    }
                }
                .padding(20)
            }
            .background(Color(NSColor.textBackgroundColor))
        }
    }
    
    private var timelineView: some View {
        ScrollView {
            LazyVStack(alignment: .leading, spacing: 0) {
                ForEach(Array(groupedTimelineItems.enumerated()), id: \.offset) { index, group in
                    VStack(alignment: .leading, spacing: 12) {
                        // 日期分组标题
                        HStack {
                            Text(group.key)
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            Image(systemName: dateIcon(for: group.key))
                                .font(.system(size: 16))
                                .foregroundColor(.accentColor)
                        }
                        .padding(.vertical, 8)
                        
                        // 该日期下的内容项
                        ForEach(group.value, id: \.id) { item in
                            TimelineContentItem(
                                item: item,
                                isSelected: selectionManager.isSelected(item.id?.uuidString ?? ""),
                                onSelectionChange: { selected in
                                    handleItemSelection(item: item, selected: selected)
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 24)
                    
                    // 添加分隔线（除了最后一组）
                    if index < groupedTimelineItems.count - 1 {
                        Divider()
                            .padding(.horizontal, 16)
                            .padding(.bottom, 16)
                    }
                }
            }
            .padding(.vertical, 16)
        }
        .background(Color(NSColor.textBackgroundColor))
    }
    
    // 时间线项目分组计算属性
    private var groupedTimelineItems: [(key: String, value: [ContentItem])] {
        let calendar = Calendar.current
        let now = Date()
        
        let grouped = Dictionary(grouping: filteredItems) { item -> String in
            guard let date = item.createdAt else { return "未知日期" }
            
            if calendar.isDateInToday(date) {
                return "今天"
            } else if calendar.isDateInYesterday(date) {
                return "昨天"
            } else if calendar.isDate(date, equalTo: now, toGranularity: .weekOfYear) {
                let formatter = DateFormatter()
                formatter.dateFormat = "EEEE"
                formatter.locale = Locale(identifier: "zh_CN")
                return formatter.string(from: date)
            } else if calendar.isDate(date, equalTo: now, toGranularity: .year) {
                let formatter = DateFormatter()
                formatter.dateFormat = "M月d日"
                formatter.locale = Locale(identifier: "zh_CN")
                return formatter.string(from: date)
            } else {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy年M月d日"
                formatter.locale = Locale(identifier: "zh_CN")
                return formatter.string(from: date)
            }
        }
        
        // 按日期排序分组
        return grouped.sorted { first, second in
            let firstDate = first.value.compactMap { $0.createdAt }.max() ?? Date.distantPast
            let secondDate = second.value.compactMap { $0.createdAt }.max() ?? Date.distantPast
            return firstDate > secondDate
        }
    }
    
    // 日期图标辅助方法
    private func dateIcon(for dateGroup: String) -> String {
        switch dateGroup {
        case "今天":
            return "calendar.circle.fill"
        case "昨天":
            return "calendar.circle"
        default:
            return "calendar"
        }
    }
    
    // MARK: - 空状态视图
    private var emptyContentView: some View {
        VStack(spacing: 20) {
            Image(systemName: "doc.badge.plus")
                .font(.system(size: 48))
                .foregroundColor(.accentColor)
                .symbolEffect(.bounce, options: .repeat(.continuous))
            
            Text("批次为空")
                .font(.title2)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                Text("开始添加您的第一个内容项目")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                Text("支持拖拽文件、复制文本或使用快捷键")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .multilineTextAlignment(.center)
            
            HStack(spacing: 12) {
                ActionButton(
                    title: "选择文件",
                    action: selectFiles,
                    style: .primary
                )
                
                ActionButton(
                    title: "快速粘贴 ⌘⇧V",
                    action: quickPaste,
                    style: .secondary
                )
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(40)
    }
    
    private var emptySearchView: some View {
        VStack(spacing: 20) {
            Image(systemName: "magnifyingglass.circle")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            Text("未找到匹配内容")
                .font(.title2)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                Text("尝试以下操作：")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("• 检查搜索词的拼写")
                    Text("• 使用更简单的关键词")
                    Text("• 清除过滤条件")
                    Text("• 切换到其他批次")
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
            }
            
            ActionButton(
                title: "清除搜索",
                action: clearSearch,
                style: .secondary
            )
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(40)
    }
    
    // MARK: - 辅助方法
    private func showQuickLook(for item: ContentItem) {
        if let filePath = item.filePath {
            quickLookURL = URL(fileURLWithPath: filePath)
        }
    }
    
    private func selectFiles() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = true
        panel.canChooseDirectories = false
        panel.canChooseFiles = true
        
        if panel.runModal() == .OK {
            Task {
                for url in panel.urls {
                    do {
                        let contentData = try ContentData.fromFile(at: url)
                        _ = try await contentService.addContent(contentData, toBatch: batch)
                    } catch {
                        NSLog("Failed to add file: \(error)")
                    }
                }
            }
        }
    }
    
    private func quickPaste() {
        // TODO: 实现快速粘贴功能
    }
    
    private func clearSearch() {
        appState.searchText = ""
    }
    
    private func updateFilteredItems() {
        var items = Array(contentItems)
        
        // 应用搜索过滤
        if !appState.searchText.isEmpty {
            items = searchService.searchContent(query: appState.searchText, in: items)
        }
        
        // 应用排序
        items = sortItems(items)
        
        filteredItems = items
    }
    
    private func sortItems(_ items: [ContentItem]) -> [ContentItem] {
        switch sortOrder {
        case .dateDescending:
            return items.sorted { ($0.createdAt ?? Date.distantPast) > ($1.createdAt ?? Date.distantPast) }
        case .dateAscending:
            return items.sorted { ($0.createdAt ?? Date.distantPast) < ($1.createdAt ?? Date.distantPast) }
        case .nameAscending:
            return items.sorted { $0.displayTitle.localizedCaseInsensitiveCompare($1.displayTitle) == .orderedAscending }
        case .nameDescending:
            return items.sorted { $0.displayTitle.localizedCaseInsensitiveCompare($1.displayTitle) == .orderedDescending }
        case .sizeAscending:
            return items.sorted { $0.fileSize < $1.fileSize }
        case .sizeDescending:
            return items.sorted { $0.fileSize > $1.fileSize }
        }
    }
    
    // MARK: - 选择处理
    private func handleItemSelection(item: ContentItem, selected: Bool) {
        let itemId = item.id?.uuidString ?? ""
        if selected {
            handleItemClick(item: item)
        } else {
            // 只取消选择当前项目，不是取消全部选择
            selectionManager.selectedItems.remove(itemId)
            if selectionManager.selectedItems.isEmpty {
                selectionManager.isMultiSelectMode = false
            }
        }
    }
    
    // 处理点击事件，支持修饰键
    private func handleItemClick(item: ContentItem) {
        let itemId = item.id?.uuidString ?? ""
        
        // 从当前 NSEvent 获取修饰键状态
        if let currentEvent = NSApp.currentEvent {
            if currentEvent.modifierFlags.contains(.command) {
                // Cmd+点击：切换选择状态
                selectionManager.toggleSelection(itemId, with: currentEvent)
            } else if currentEvent.modifierFlags.contains(.shift) {
                // Shift+点击：范围选择
                selectionManager.selectRange(itemId, with: currentEvent)
            } else {
                // 普通点击：单选
                selectionManager.selectSingle(itemId)
            }
        } else {
            // 无事件信息时，默认单选
            selectionManager.selectSingle(itemId)
        }
    }
    
    private func getSelectedItem() -> ContentItem? {
        let selectedIds = selectionManager.selectedItems
        if selectedIds.count == 1, let selectedId = selectedIds.first {
            return filteredItems.first { $0.id?.uuidString == selectedId }
        }
        return nil
    }
    
    // MARK: - 批量操作
    private func deleteSelectedItems() {
        let selectedItems = selectionManager.getSelectedItems()
        
        guard !selectedItems.isEmpty else {
            let alert = NSAlert()
            alert.messageText = "没有选中项目"
            alert.informativeText = "请先选择要删除的项目。"
            alert.alertStyle = .informational
            alert.addButton(withTitle: "确定")
            alert.runModal()
            return
        }
        
        // 显示确认对话框
        let alert = NSAlert()
        alert.messageText = "确认删除"
        alert.informativeText = "您确定要删除选中的 \(selectedItems.count) 个项目吗？此操作无法撤销。"
        alert.alertStyle = .warning
        alert.addButton(withTitle: "删除")
        alert.addButton(withTitle: "取消")
        
        // 确保在主线程运行对话框
        DispatchQueue.main.async {
            let response = alert.runModal()
            if response == .alertFirstButtonReturn {
                Task { @MainActor in
                    var successCount = 0
                    var failureCount = 0
                    
                    // 显示进度指示
                    for (index, item) in selectedItems.enumerated() {
                        do {
                            try await self.contentService.deleteContent(item)
                            successCount += 1
                            NSLog("已删除项目 \(index + 1)/\(selectedItems.count): \(item.displayTitle)")
                        } catch {
                            failureCount += 1
                            NSLog("删除失败: \(error)")
                        }
                    }
                    
                    // 清除选择
                    self.selectionManager.deselectAll()
                    
                    // 显示完成通知
                    let completionAlert = NSAlert()
                    if failureCount == 0 {
                        completionAlert.messageText = "删除完成"
                        completionAlert.informativeText = "成功删除 \(successCount) 个项目。"
                        completionAlert.alertStyle = .informational
                    } else {
                        completionAlert.messageText = "删除部分完成"
                        completionAlert.informativeText = "成功删除 \(successCount) 个项目，失败 \(failureCount) 个项目。"
                        completionAlert.alertStyle = .warning
                    }
                    completionAlert.addButton(withTitle: "确定")
                    completionAlert.runModal()
                    
                    NSLog("批量删除完成: 成功 \(successCount), 失败 \(failureCount)")
                }
            }
        }
    }
    
    private func exportSelectedItems() {
        let selectedItems = selectionManager.getSelectedItems()
        
        guard !selectedItems.isEmpty else {
            let alert = NSAlert()
            alert.messageText = "没有选中项目"
            alert.informativeText = "请先选择要导出的项目。"
            alert.alertStyle = .informational
            alert.addButton(withTitle: "确定")
            alert.runModal()
            return
        }
        
        // 创建打开面板来选择目录
        let openPanel = NSOpenPanel()
        openPanel.title = "导出选中项目"
        openPanel.message = "选择导出位置"
        openPanel.canCreateDirectories = true
        openPanel.canChooseDirectories = true
        openPanel.canChooseFiles = false
        
        if openPanel.runModal() == .OK, let exportURL = openPanel.url {
            Task { @MainActor in
                var successCount = 0
                var failureCount = 0
                
                for (index, item) in selectedItems.enumerated() {
                    do {
                        if let filePath = item.filePath {
                            // 复制文件
                            let sourceURL = URL(fileURLWithPath: filePath)
                            let fileName = sourceURL.lastPathComponent
                            let destinationURL = exportURL.appendingPathComponent(fileName)
                            
                            // 如果目标文件已存在，添加数字后缀
                            var finalDestinationURL = destinationURL
                            var counter = 1
                            while FileManager.default.fileExists(atPath: finalDestinationURL.path) {
                                let nameWithoutExt = sourceURL.deletingPathExtension().lastPathComponent
                                let ext = sourceURL.pathExtension
                                let newName = ext.isEmpty ? "\(nameWithoutExt)_\(counter)" : "\(nameWithoutExt)_\(counter).\(ext)"
                                finalDestinationURL = exportURL.appendingPathComponent(newName)
                                counter += 1
                            }
                            
                            try FileManager.default.copyItem(at: sourceURL, to: finalDestinationURL)
                            successCount += 1
                            
                        } else if let content = item.content {
                            // 保存文本内容
                            let fileName = "\(item.displayTitle).txt"
                            var destinationURL = exportURL.appendingPathComponent(fileName)
                            
                            // 如果目标文件已存在，添加数字后缀
                            var counter = 1
                            while FileManager.default.fileExists(atPath: destinationURL.path) {
                                let newName = "\(item.displayTitle)_\(counter).txt"
                                destinationURL = exportURL.appendingPathComponent(newName)
                                counter += 1
                            }
                            
                            try content.write(to: destinationURL, atomically: true, encoding: .utf8)
                            successCount += 1
                        }
                        
                        NSLog("已导出项目 \(index + 1)/\(selectedItems.count): \(item.displayTitle)")
                        
                    } catch {
                        failureCount += 1
                        NSLog("导出失败: \(item.displayTitle) - \(error)")
                    }
                }
                
                // 显示完成通知
                DispatchQueue.main.async {
                    let alert = NSAlert()
                    if failureCount == 0 {
                        alert.messageText = "导出完成"
                        alert.informativeText = "成功导出 \(successCount) 个项目到 \(exportURL.path)"
                        alert.alertStyle = .informational
                    } else {
                        alert.messageText = "导出部分完成"
                        alert.informativeText = "成功导出 \(successCount) 个项目，失败 \(failureCount) 个项目。"
                        alert.alertStyle = .warning
                    }
                    alert.addButton(withTitle: "确定")
                    alert.addButton(withTitle: "打开文件夹")
                    
                    let response = alert.runModal()
                    if response == .alertSecondButtonReturn {
                        NSWorkspace.shared.open(exportURL)
                    }
                }
                
                NSLog("导出完成: 成功 \(successCount), 失败 \(failureCount)")
            }
        }
    }
}

// MARK: - 时间线内容项目组件
struct TimelineContentItem: View {
    let item: ContentItem
    let isSelected: Bool
    let onSelectionChange: (Bool) -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            // 时间线标记
            VStack(spacing: 0) {
                Circle()
                    .fill(timelineColor)
                    .frame(width: 12, height: 12)
                
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 2)
                    .frame(maxHeight: .infinity)
            }
            .frame(height: 60)
            
            // 内容区域
            VStack(alignment: .leading, spacing: 8) {
                // 标题和时间
                HStack(alignment: .top) {
                    VStack(alignment: .leading, spacing: 4) {
                        // 内容类型和标题
                        HStack(spacing: 8) {
                            Image(systemName: item.contentTypeEnum.systemImage)
                                .foregroundColor(Color(tagColor: item.contentTypeEnum.color))
                                .font(.system(size: 14))
                            
                            Text(item.displayTitle)
                                .font(.system(size: 15, weight: .medium))
                                .lineLimit(2)
                        }
                        
                        // 内容预览
                        if let content = item.content, !content.isEmpty {
                            Text(content)
                                .font(.system(size: 13))
                                .foregroundColor(.secondary)
                                .lineLimit(3)
                                .padding(.leading, 22) // 对齐图标
                        }
                    }
                    
                    Spacer()
                    
                    // 时间和大小信息
                    VStack(alignment: .trailing, spacing: 2) {
                        if let createdAt = item.createdAt {
                            Text(createdAt, style: .time)
                                .font(.system(size: 11))
                                .foregroundColor(.secondary)
                        }
                        
                        Text(item.formattedFileSize)
                            .font(.system(size: 10))
                            .foregroundColor(.secondary)
                    }
                }
                
                // 标签显示
                if !item.tagNames.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 6) {
                            ForEach(item.tagNames.prefix(5), id: \.self) { tagName in
                                Text(tagName)
                                    .font(.system(size: 10))
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(
                                        Capsule()
                                            .fill(Color.accentColor.opacity(0.15))
                                    )
                                    .foregroundColor(.accentColor)
                            }
                            
                            if item.tagNames.count > 5 {
                                Text("+\(item.tagNames.count - 5)")
                                    .font(.system(size: 10))
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.leading, 22) // 对齐图标
                    }
                }
                
                // 选择复选框（悬停时显示）
                if isHovered {
                    HStack(spacing: 8) {
                        Button(action: {
                            onSelectionChange(!isSelected)
                        }) {
                            Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                                .foregroundColor(isSelected ? .accentColor : .secondary)
                        }
                        .buttonStyle(.plain)
                        
                        Spacer()
                    }
                    .padding(.leading, 22) // 对齐图标
                    .animation(.easeInOut(duration: 0.2), value: isHovered)
                }
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.accentColor.opacity(0.1) : 
                          (isHovered ? Color.gray.opacity(0.05) : Color.clear))
                    .strokeBorder(
                        isSelected ? Color.accentColor.opacity(0.3) : Color.clear,
                        lineWidth: 1
                    )
            )
            .onHover { hovering in
                withAnimation(.easeInOut(duration: 0.2)) {
                    isHovered = hovering
                }
            }
            .onTapGesture {
                onSelectionChange(!isSelected)
            }
        }
    }
    
    private var timelineColor: Color {
        Color(tagColor: item.contentTypeEnum.color)
    }
}