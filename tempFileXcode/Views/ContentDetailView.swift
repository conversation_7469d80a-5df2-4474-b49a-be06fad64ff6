import SwiftUI
import AppKit

// MARK: - Content Detail View
struct ContentDetailView: View {
    let item: ContentItem
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var contentService: ContentService
    
    @State private var showingFullContent = false
    @State private var copySuccess = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 头部信息
                headerView
                
                Divider()
                
                // 内容区域
                contentView
                
                Divider()
                
                // 操作按钮
                actionButtons
            }
        }
        .frame(minWidth: 600, minHeight: 400)
        .alert("复制成功", isPresented: $copySuccess) {
            Button("确定") { }
        } message: {
            Text("内容已复制到剪贴板")
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: item.contentTypeEnum.systemImage)
                    .foregroundColor(Color(tagColor: item.contentTypeEnum.color))
                    .font(.title)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(item.displayTitle)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .lineLimit(2)
                    
                    HStack(spacing: 16) {
                        Label(item.contentTypeEnum.displayName, systemImage: "doc")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Label(item.formattedFileSize, systemImage: "externaldrive")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        if let createdAt = item.createdAt {
                            Label(createdAt.formatted(date: .abbreviated, time: .shortened), systemImage: "clock")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Spacer()
                
                Button("关闭") {
                    dismiss()
                }
                .keyboardShortcut(.escape)
            }
            
            // 文件路径
            if let filePath = item.filePath {
                HStack {
                    Text("路径:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(filePath)
                        .font(.caption)
                        .foregroundColor(.primary)
                        .textSelection(.enabled)
                    
                    Spacer()
                    
                    Button("复制路径") {
                        copyToClipboard(filePath)
                    }
                    .buttonStyle(.bordered)
                    .font(.caption)
                }
            }
            
            // 标签
            if !item.tagNames.isEmpty {
                HStack {
                    Text("标签:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack(spacing: 6) {
                        ForEach(item.tagNames, id: \.self) { tagName in
                            Text(tagName)
                                .font(.caption2)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.accentColor.opacity(0.2))
                                .foregroundColor(.accentColor)
                                .cornerRadius(8)
                        }
                    }
                    
                    Spacer()
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    @ViewBuilder
    private var contentView: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                switch item.contentTypeEnum {
                case .text:
                    textContentView
                case .image:
                    imageContentView
                case .file:
                    fileContentView
                }
            }
            .padding()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var textContentView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("文本内容")
                    .font(.headline)
                
                Spacer()
                
                Button("复制文本") {
                    if let content = item.content {
                        copyToClipboard(content)
                    }
                }
                .buttonStyle(.bordered)
            }
            
            if let content = item.content {
                Text(content)
                    .font(.body)
                    .textSelection(.enabled)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            } else {
                Text("无文本内容")
                    .foregroundColor(.secondary)
                    .italic()
            }
        }
    }
    
    private var imageContentView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("图片预览")
                    .font(.headline)
                
                Spacer()
                
                if let filePath = item.filePath {
                    Button("在Finder中显示") {
                        NSWorkspace.shared.selectFile(filePath, inFileViewerRootedAtPath: "")
                    }
                    .buttonStyle(.bordered)
                    
                    Button("复制图片") {
                        copyImageToClipboard()
                    }
                    .buttonStyle(.bordered)
                }
            }
            
            if let filePath = item.filePath,
               let image = NSImage(contentsOfFile: filePath) {
                VStack(spacing: 8) {
                    Image(nsImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 400)
                        .cornerRadius(8)
                        .shadow(radius: 2)
                    
                    Text("图片尺寸: \(Int(image.size.width)) × \(Int(image.size.height))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            } else {
                VStack(spacing: 8) {
                    Image(systemName: "photo")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    
                    Text("无法加载图片")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(height: 200)
            }
        }
    }
    
    private var fileContentView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("文件信息")
                    .font(.headline)
                
                Spacer()
                
                if let filePath = item.filePath {
                    Button("在Finder中显示") {
                        NSWorkspace.shared.selectFile(filePath, inFileViewerRootedAtPath: "")
                    }
                    .buttonStyle(.bordered)
                    
                    Button("打开文件") {
                        NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
            
            VStack(alignment: .leading, spacing: 8) {
                if let fileName = item.fileName {
                    InfoRow(label: "文件名", value: fileName)
                }
                
                InfoRow(label: "文件大小", value: item.formattedFileSize)
                
                if let mimeType = item.mimeType {
                    InfoRow(label: "文件类型", value: mimeType)
                }
                
                if let createdAt = item.createdAt {
                    InfoRow(label: "创建时间", value: createdAt.formatted(date: .abbreviated, time: .shortened))
                }
                
                if let updatedAt = item.updatedAt {
                    InfoRow(label: "修改时间", value: updatedAt.formatted(date: .abbreviated, time: .shortened))
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
            
            // 如果是文本文件，尝试显示内容预览
            if let filePath = item.filePath,
               isTextFile(filePath) {
                textFilePreview(filePath)
            }
        }
    }
    
    private var actionButtons: some View {
        HStack(spacing: 12) {
            Button("删除") {
                deleteItem()
            }
            .buttonStyle(.bordered)
            .foregroundColor(.red)
            
            Spacer()
            
            Button("编辑") {
                // TODO: 实现编辑功能
            }
            .buttonStyle(.bordered)
            
            Button("导出") {
                exportItem()
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    // MARK: - Helper Views
    private struct InfoRow: View {
        let label: String
        let value: String
        
        var body: some View {
            HStack {
                Text(label + ":")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(width: 80, alignment: .leading)
                
                Text(value)
                    .font(.caption)
                    .textSelection(.enabled)
                
                Spacer()
            }
        }
    }
    
    private func textFilePreview(_ filePath: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("文件预览")
                .font(.subheadline)
                .fontWeight(.semibold)
            
            if let content = try? String(contentsOfFile: filePath, encoding: .utf8) {
                ScrollView {
                    Text(content.prefix(2000)) // 限制预览长度
                        .font(.system(.caption, design: .monospaced))
                        .textSelection(.enabled)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(8)
                        .background(Color.black.opacity(0.05))
                        .cornerRadius(6)
                }
                .frame(maxHeight: 200)
                
                if content.count > 2000 {
                    Text("... (内容已截断，显示前2000个字符)")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .italic()
                }
                
                Button("复制全部内容") {
                    copyToClipboard(content)
                }
                .buttonStyle(.bordered)
            } else {
                Text("无法读取文件内容")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .italic()
            }
        }
        .padding(.top)
    }
    
    // MARK: - Helper Methods
    private func copyToClipboard(_ text: String) {
        NSPasteboard.general.clearContents()
        NSPasteboard.general.setString(text, forType: .string)
        copySuccess = true
    }
    
    private func copyImageToClipboard() {
        guard let filePath = item.filePath,
              let image = NSImage(contentsOfFile: filePath) else { return }
        
        NSPasteboard.general.clearContents()
        NSPasteboard.general.writeObjects([image])
        copySuccess = true
    }
    
    private func isTextFile(_ filePath: String) -> Bool {
        let textExtensions = ["txt", "md", "json", "xml", "html", "css", "js", "py", "swift", "java", "cpp", "c", "h"]
        let fileExtension = URL(fileURLWithPath: filePath).pathExtension.lowercased()
        return textExtensions.contains(fileExtension)
    }
    
    private func deleteItem() {
        Task {
            do {
                try await contentService.deleteContent(item)
                await MainActor.run {
                    dismiss()
                }
            } catch {
                NSLog("删除失败: \(error)")
            }
        }
    }
    
    private func exportItem() {
        // TODO: 实现单个项目导出功能
        let panel = NSSavePanel()
        panel.nameFieldStringValue = item.fileName ?? "导出文件"
        
        if panel.runModal() == .OK, let url = panel.url {
            // 实现导出逻辑
            if let filePath = item.filePath {
                do {
                    try FileManager.default.copyItem(at: URL(fileURLWithPath: filePath), to: url)
                    copySuccess = true
                } catch {
                    NSLog("导出失败: \(error)")
                }
            }
        }
    }
}

//#Preview {
//    ContentDetailView(item: ContentItem())
//        .environmentObject(ContentService())
//}