import SwiftUI
import CoreData

// MARK: - Batch Info View
struct BatchInfoView: View {
    let batch: NSManagedObject
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("批次信息")
                .font(.headline)
                .foregroundColor(.secondary)
            
            VStack(alignment: .leading, spacing: 6) {
                // 项目数量
                let itemCount = (batch.value(forKey: "contentItems") as? NSSet)?.count ?? 0
                InfoRow(
                    icon: "doc.text",
                    label: "项目数量",
                    value: "\(itemCount) 个"
                )
                
                // 创建时间
                if let createdAt = batch.value(forKey: "createdAt") as? Date {
                    InfoRow(
                        icon: "clock",
                        label: "创建时间",
                        value: createdAt.formatted(date: .abbreviated, time: .omitted)
                    )
                }
                
                // 最后更新时间
                if let updatedAt = batch.value(forKey: "updatedAt") as? Date {
                    InfoRow(
                        icon: "arrow.clockwise",
                        label: "最后更新",
                        value: updatedAt.formatted(date: .abbreviated, time: .omitted)
                    )
                }
                
                // 批次大小（估算）
                let totalSize = calculateBatchSize()
                if totalSize > 0 {
                    InfoRow(
                        icon: "externaldrive",
                        label: "总大小",
                        value: ByteCountFormatter.string(fromByteCount: totalSize, countStyle: .file)
                    )
                }
            }
            .padding(8)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
    }
    
    private struct InfoRow: View {
        let icon: String
        let label: String
        let value: String
        
        var body: some View {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .foregroundColor(.secondary)
                    .font(.caption)
                    .frame(width: 12)
                
                Text(label)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text(value)
                    .font(.caption2)
                    .foregroundColor(.primary)
            }
        }
    }
    
    private func calculateBatchSize() -> Int64 {
        guard let items = batch.value(forKey: "contentItems") as? NSSet else { return 0 }
        
        var totalSize: Int64 = 0
        for item in items {
            if let contentItem = item as? ContentItem {
                totalSize += contentItem.fileSize
            }
        }
        
        return totalSize
    }
}

//#Preview {
//    BatchInfoView(batch: NSManagedObject())
//}