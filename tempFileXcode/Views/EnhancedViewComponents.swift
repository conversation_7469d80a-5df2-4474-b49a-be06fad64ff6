import SwiftUI
import CoreData
import UniformTypeIdentifiers
import QuickLook

// MARK: - 分栏视图文件行
struct EnhancedColumnFileRow: View {
    let item: ContentItem
    let isSelected: Bool
    let onSelectionChange: (Bool) -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        HStack(spacing: 12) {
            // 文件类型图标
            Image(systemName: getFileIcon())
                .foregroundColor(getFileIconColor())
                .font(.system(size: 16))
                .frame(width: 20, height: 20)
            
            // 文件信息
            VStack(alignment: .leading, spacing: 2) {
                Text(item.displayTitle)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                    .truncationMode(.middle)
                
                HStack(spacing: 8) {
                    Text(getFileTypeDescription())
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    if item.contentTypeEnum != .text {
                        Text("•")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        Text(item.formattedFileSize)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // 时间信息
            Text(item.formattedCreatedDate)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(isSelected ? Color.accentColor.opacity(0.15) : (isHovered ? Color.gray.opacity(0.1) : Color.clear))
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .strokeBorder(
                            isSelected ? Color.accentColor : Color.clear,
                            lineWidth: isSelected ? 2 : 0
                        )
                )
        )
        .contentShape(Rectangle())
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .simultaneousGesture(
            TapGesture()
                .onEnded { _ in
                    handleSingleClick()
                }
        )
    }
    
    private func handleSingleClick() {
        let modifierFlags = NSEvent.modifierFlags
        if modifierFlags.contains(.command) {
            onSelectionChange(!isSelected)
        } else if modifierFlags.contains(.shift) {
            onSelectionChange(true)
        } else {
            onSelectionChange(true)
        }
    }
    
    private func getFileIcon() -> String {
        switch item.contentTypeEnum {
        case .text:
            return "doc.text"
        case .image:
            return "photo"
        case .file:
            guard let fileName = item.fileName else { return "doc.fill" }
            let fileExtension = (fileName as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "xlsx", "xls":
                return "tablecells.fill"
            case "docx", "doc":
                return "doc.text.fill"
            case "pptx", "ppt":
                return "play.rectangle.fill"
            case "pdf":
                return "doc.richtext.fill"
            case "zip", "rar", "7z":
                return "archivebox.fill"
            case "mp4", "mov", "avi":
                return "play.rectangle.fill"
            case "mp3", "wav", "m4a":
                return "music.note"
            case "jpg", "jpeg", "png", "gif", "bmp":
                return "photo.fill"
            default:
                return "doc.fill"
            }
        }
    }
    
    private func getFileIconColor() -> Color {
        switch item.contentTypeEnum {
        case .text:
            return .blue
        case .image:
            return .purple
        case .file:
            guard let fileName = item.fileName else { return .accentColor }
            let fileExtension = (fileName as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "xlsx", "xls":
                return .green
            case "docx", "doc":
                return .blue
            case "pptx", "ppt":
                return .orange
            case "pdf":
                return .red
            case "zip", "rar", "7z":
                return .purple
            case "mp4", "mov", "avi":
                return .pink
            case "mp3", "wav", "m4a":
                return .cyan
            case "jpg", "jpeg", "png", "gif", "bmp":
                return .mint
            default:
                return .accentColor
            }
        }
    }
    
    private func getFileTypeDescription() -> String {
        switch item.contentTypeEnum {
        case .text:
            return "文本"
        case .image:
            return "图片"
        case .file:
            guard let fileName = item.fileName else { return "文件" }
            let fileExtension = (fileName as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "xlsx", "xls":
                return "Excel表格"
            case "docx", "doc":
                return "Word文档"
            case "pptx", "ppt":
                return "PowerPoint"
            case "pdf":
                return "PDF文档"
            case "zip", "rar", "7z":
                return "压缩文件"
            case "mp4", "mov", "avi":
                return "视频文件"
            case "mp3", "wav", "m4a":
                return "音频文件"
            case "jpg", "jpeg", "png", "gif", "bmp":
                return "图片文件"
            default:
                return fileExtension.uppercased() + "文件"
            }
        }
    }
}

// MARK: - 分栏视图内容预览
struct EnhancedColumnContentPreview: View {
    let item: ContentItem
    
    @State private var quickLookURL: URL?
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 头部信息
                headerView
                
                Divider()
                
                // 内容预览
                contentPreview
                
                Spacer()
            }
            .padding(20)
        }
        .background(Color(NSColor.textBackgroundColor))
        .quickLookPreview($quickLookURL)
    }
    
    private var headerView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: getFileIcon())
                    .foregroundColor(getFileIconColor())
                    .font(.system(size: 32))
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(item.displayTitle)
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text(getFileTypeDescription())
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 预览按钮
                Button(action: showQuickLook) {
                    HStack(spacing: 6) {
                        Image(systemName: "eye.fill")
                        Text("预览")
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.accentColor)
                    )
                }
                .buttonStyle(.plain)
            }
            
            // 文件信息
            HStack(spacing: 20) {
                if item.contentTypeEnum != .text {
                    Label(item.formattedFileSize, systemImage: "doc")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Label(item.formattedCreatedDate, systemImage: "clock")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if let filePath = item.filePath {
                    Label(URL(fileURLWithPath: filePath).deletingLastPathComponent().lastPathComponent, systemImage: "folder")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    @ViewBuilder
    private var contentPreview: some View {
        switch item.contentTypeEnum {
        case .text:
            textContentPreview
        case .image:
            imageContentPreview
        case .file:
            fileContentPreview
        }
    }
    
    private var textContentPreview: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("文本内容")
                .font(.headline)
                .fontWeight(.semibold)
            
            if let content = item.content {
                ScrollView {
                    Text(content)
                        .font(.body)
                        .lineLimit(nil) // 显示全部内容，不截断
                        .textSelection(.enabled) // 允许文本选择
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(16)
                }
                .frame(minHeight: 200, maxHeight: 500)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(NSColor.controlBackgroundColor))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .strokeBorder(Color.gray.opacity(0.2), lineWidth: 1)
                        )
                )
                
                // 复制按钮
                HStack {
                    Spacer()
                    Button(action: copyText) {
                        HStack(spacing: 6) {
                            Image(systemName: "doc.on.doc")
                            Text("复制文本")
                        }
                        .foregroundColor(.accentColor)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color.accentColor.opacity(0.1))
                        )
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }
    
    private var imageContentPreview: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("图片预览")
                .font(.headline)
                .fontWeight(.semibold)
            
            if let filePath = item.filePath,
               let image = NSImage(contentsOfFile: filePath) {
                Image(nsImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxHeight: 400)
                    .cornerRadius(12)
                    .clipped()
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .strokeBorder(Color.gray.opacity(0.2), lineWidth: 1)
                    )
            } else {
                VStack(spacing: 16) {
                    Image(systemName: "photo.badge.exclamationmark")
                        .font(.system(size: 48))
                        .foregroundColor(.orange)
                    
                    Text("图片无法加载")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(height: 200)
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.1))
                )
            }
        }
    }
    
    private var fileContentPreview: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("文件预览")
                .font(.headline)
                .fontWeight(.semibold)
            
            // 增强的文件预览
            VStack(spacing: 20) {
                // 大文件图标
                Image(systemName: getFileIcon())
                    .font(.system(size: 80))
                    .foregroundColor(getFileIconColor())
                
                VStack(spacing: 8) {
                    if let fileName = item.fileName {
                        Text(fileName)
                            .font(.title3)
                            .fontWeight(.medium)
                            .multilineTextAlignment(.center)
                    }
                    
                    Text(getFileTypeDescription())
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                // 文件操作按钮
                HStack(spacing: 12) {
                    Button(action: showQuickLook) {
                        HStack(spacing: 6) {
                            Image(systemName: "eye")
                            Text("快速预览")
                        }
                        .foregroundColor(.accentColor)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.accentColor.opacity(0.1))
                        )
                    }
                    .buttonStyle(.plain)
                    
                    Button(action: openFile) {
                        HStack(spacing: 6) {
                            Image(systemName: "arrow.up.right.square")
                            Text("打开文件")
                        }
                        .foregroundColor(.accentColor)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.accentColor.opacity(0.1))
                        )
                    }
                    .buttonStyle(.plain)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(32)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(NSColor.controlBackgroundColor))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .strokeBorder(Color.gray.opacity(0.2), lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - 辅助方法
    private func getFileIcon() -> String {
        switch item.contentTypeEnum {
        case .text:
            return "doc.text"
        case .image:
            return "photo"
        case .file:
            guard let fileName = item.fileName else { return "doc.fill" }
            let fileExtension = (fileName as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "xlsx", "xls":
                return "tablecells.fill"
            case "docx", "doc":
                return "doc.text.fill"
            case "pptx", "ppt":
                return "play.rectangle.fill"
            case "pdf":
                return "doc.richtext.fill"
            case "zip", "rar", "7z":
                return "archivebox.fill"
            case "mp4", "mov", "avi":
                return "play.rectangle.fill"
            case "mp3", "wav", "m4a":
                return "music.note"
            case "jpg", "jpeg", "png", "gif", "bmp":
                return "photo.fill"
            default:
                return "doc.fill"
            }
        }
    }
    
    private func getFileIconColor() -> Color {
        switch item.contentTypeEnum {
        case .text:
            return .blue
        case .image:
            return .purple
        case .file:
            guard let fileName = item.fileName else { return .accentColor }
            let fileExtension = (fileName as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "xlsx", "xls":
                return .green
            case "docx", "doc":
                return .blue
            case "pptx", "ppt":
                return .orange
            case "pdf":
                return .red
            case "zip", "rar", "7z":
                return .purple
            case "mp4", "mov", "avi":
                return .pink
            case "mp3", "wav", "m4a":
                return .cyan
            case "jpg", "jpeg", "png", "gif", "bmp":
                return .mint
            default:
                return .accentColor
            }
        }
    }
    
    private func getFileTypeDescription() -> String {
        switch item.contentTypeEnum {
        case .text:
            return "文本内容"
        case .image:
            return "图片文件"
        case .file:
            guard let fileName = item.fileName else { return "文件" }
            let fileExtension = (fileName as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "xlsx", "xls":
                return "Microsoft Excel 表格"
            case "docx", "doc":
                return "Microsoft Word 文档"
            case "pptx", "ppt":
                return "Microsoft PowerPoint 演示文稿"
            case "pdf":
                return "PDF 文档"
            case "zip", "rar", "7z":
                return "压缩文件"
            case "mp4", "mov", "avi":
                return "视频文件"
            case "mp3", "wav", "m4a":
                return "音频文件"
            case "jpg", "jpeg", "png", "gif", "bmp":
                return "图片文件"
            default:
                return fileExtension.uppercased() + " 文件"
            }
        }
    }
    
    private func showQuickLook() {
        if let filePath = item.filePath {
            quickLookURL = URL(fileURLWithPath: filePath)
        }
    }
    
    private func openFile() {
        if let filePath = item.filePath {
            NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
        }
    }
    
    private func copyText() {
        if let content = item.content {
            NSPasteboard.general.clearContents()
            NSPasteboard.general.setString(content, forType: .string)
        }
    }
}

// MARK: - 画廊视图内容预览
struct EnhancedGalleryContentPreview: View {
    let item: ContentItem
    
    @State private var quickLookURL: URL?
    
    var body: some View {
        HStack(spacing: 0) {
            // 左侧：大预览区域
            mainPreviewArea
                .frame(maxWidth: .infinity)
            
            Divider()
            
            // 右侧：详细信息面板
            detailsPanel
                .frame(width: 300)
        }
        .background(Color(NSColor.textBackgroundColor))
        .quickLookPreview($quickLookURL)
    }
    
    private var mainPreviewArea: some View {
        VStack(spacing: 0) {
            // 内容预览
            contentPreview
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            
            // 底部操作栏
            actionBar
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(Color(NSColor.controlBackgroundColor))
        }
    }
    
    @ViewBuilder
    private var contentPreview: some View {
        switch item.contentTypeEnum {
        case .text:
            textPreview
        case .image:
            imagePreview
        case .file:
            filePreview
        }
    }
    
    private var textPreview: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 标题
                HStack {
                    Image(systemName: "doc.text")
                        .foregroundColor(.blue)
                        .font(.system(size: 24))
                    
                    Text("文本内容")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
                
                // 文本内容 - 完整显示，不截断
                if let content = item.content {
                    ScrollView {
                        Text(content)
                            .font(.body)
                            .lineLimit(nil) // 显示全部内容，不截断
                            .textSelection(.enabled) // 允许文本选择
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(20)
                    }
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(NSColor.controlBackgroundColor))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .strokeBorder(Color.gray.opacity(0.2), lineWidth: 1)
                            )
                    )
                    .padding(.horizontal, 20)
                }
                
                Spacer()
            }
        }
    }
    
    private var imagePreview: some View {
        VStack(spacing: 16) {
            if let filePath = item.filePath,
               let image = NSImage(contentsOfFile: filePath) {
                
                // 图片预览
                Image(nsImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .cornerRadius(12)
                    .clipped()
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .strokeBorder(Color.gray.opacity(0.2), lineWidth: 1)
                    )
                    .padding(20)
                
            } else {
                // 图片加载失败
                VStack(spacing: 20) {
                    Image(systemName: "photo.badge.exclamationmark")
                        .font(.system(size: 64))
                        .foregroundColor(.orange)
                    
                    Text("图片无法加载")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    
                    if let fileName = item.fileName {
                        Text(fileName)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.gray.opacity(0.1))
                )
                .padding(40)
            }
        }
    }
    
    private var filePreview: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // 文件图标和信息
            VStack(spacing: 20) {
                // 大文件图标
                Image(systemName: getFileIcon())
                    .font(.system(size: 100))
                    .foregroundColor(getFileIconColor())
                
                VStack(spacing: 8) {
                    if let fileName = item.fileName {
                        Text(fileName)
                            .font(.title)
                            .fontWeight(.semibold)
                            .multilineTextAlignment(.center)
                    }
                    
                    Text(getFileTypeDescription())
                        .font(.title3)
                        .foregroundColor(.secondary)
                    
                    Text(item.formattedFileSize)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                // 预览提示
                VStack(spacing: 8) {
                    Text("支持的文件预览")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text("点击下方的预览按钮查看文件内容")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 16)
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(40)
    }
    
    private var actionBar: some View {
        HStack(spacing: 16) {
            // 文件类型标签
            HStack(spacing: 6) {
                Image(systemName: getFileIcon())
                    .foregroundColor(getFileIconColor())
                    .font(.system(size: 14))
                
                Text(getFileTypeDescription())
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(getFileIconColor().opacity(0.1))
            )
            
            Spacer()
            
            // 操作按钮
            HStack(spacing: 12) {
                // 快速预览按钮
                Button(action: showQuickLook) {
                    HStack(spacing: 6) {
                        Image(systemName: "eye.fill")
                        Text("快速预览")
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.accentColor)
                    )
                }
                .buttonStyle(.plain)
                
                // 打开文件按钮
                if item.contentTypeEnum == .file {
                    Button(action: openFile) {
                        HStack(spacing: 6) {
                            Image(systemName: "arrow.up.right.square")
                            Text("打开")
                        }
                        .foregroundColor(.accentColor)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.accentColor.opacity(0.1))
                        )
                    }
                    .buttonStyle(.plain)
                }
                
                // 复制按钮
                if item.contentTypeEnum == .text {
                    Button(action: copyText) {
                        HStack(spacing: 6) {
                            Image(systemName: "doc.on.doc")
                            Text("复制")
                        }
                        .foregroundColor(.accentColor)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.accentColor.opacity(0.1))
                        )
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }
    
    private var detailsPanel: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 基本信息
                VStack(alignment: .leading, spacing: 12) {
                    Text("基本信息")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        EnhancedDetailRow(label: "名称", value: item.displayTitle, icon: "textformat")
                        EnhancedDetailRow(label: "类型", value: getFileTypeDescription(), icon: "tag")
                        
                        if item.contentTypeEnum != .text {
                            EnhancedDetailRow(label: "大小", value: item.formattedFileSize, icon: "doc")
                        }
                        
                        EnhancedDetailRow(label: "创建时间", value: item.formattedCreatedDate, icon: "clock")
                        
                        if let filePath = item.filePath {
                            EnhancedDetailRow(
                                label: "位置", 
                                value: URL(fileURLWithPath: filePath).deletingLastPathComponent().lastPathComponent, 
                                icon: "folder"
                            )
                        }
                    }
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(NSColor.controlBackgroundColor))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .strokeBorder(Color.gray.opacity(0.2), lineWidth: 1)
                        )
                )
                
                Spacer()
            }
            .padding(20)
        }
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    // MARK: - 辅助方法
    private func getFileIcon() -> String {
        switch item.contentTypeEnum {
        case .text:
            return "doc.text"
        case .image:
            return "photo"
        case .file:
            guard let fileName = item.fileName else { return "doc.fill" }
            let fileExtension = (fileName as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "xlsx", "xls":
                return "tablecells.fill"
            case "docx", "doc":
                return "doc.text.fill"
            case "pptx", "ppt":
                return "play.rectangle.fill"
            case "pdf":
                return "doc.richtext.fill"
            case "zip", "rar", "7z":
                return "archivebox.fill"
            case "mp4", "mov", "avi":
                return "play.rectangle.fill"
            case "mp3", "wav", "m4a":
                return "music.note"
            case "jpg", "jpeg", "png", "gif", "bmp":
                return "photo.fill"
            default:
                return "doc.fill"
            }
        }
    }
    
    private func getFileIconColor() -> Color {
        switch item.contentTypeEnum {
        case .text:
            return .blue
        case .image:
            return .purple
        case .file:
            guard let fileName = item.fileName else { return .accentColor }
            let fileExtension = (fileName as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "xlsx", "xls":
                return .green
            case "docx", "doc":
                return .blue
            case "pptx", "ppt":
                return .orange
            case "pdf":
                return .red
            case "zip", "rar", "7z":
                return .purple
            case "mp4", "mov", "avi":
                return .pink
            case "mp3", "wav", "m4a":
                return .cyan
            case "jpg", "jpeg", "png", "gif", "bmp":
                return .mint
            default:
                return .accentColor
            }
        }
    }
    
    private func getFileTypeDescription() -> String {
        switch item.contentTypeEnum {
        case .text:
            return "文本内容"
        case .image:
            return "图片文件"
        case .file:
            guard let fileName = item.fileName else { return "文件" }
            let fileExtension = (fileName as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "xlsx", "xls":
                return "Excel 表格"
            case "docx", "doc":
                return "Word 文档"
            case "pptx", "ppt":
                return "PowerPoint 演示文稿"
            case "pdf":
                return "PDF 文档"
            case "zip", "rar", "7z":
                return "压缩文件"
            case "mp4", "mov", "avi":
                return "视频文件"
            case "mp3", "wav", "m4a":
                return "音频文件"
            case "jpg", "jpeg", "png", "gif", "bmp":
                return "图片文件"
            default:
                return fileExtension.uppercased() + " 文件"
            }
        }
    }
    
    private func showQuickLook() {
        if let filePath = item.filePath {
            quickLookURL = URL(fileURLWithPath: filePath)
        }
    }
    
    private func openFile() {
        if let filePath = item.filePath {
            NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
        }
    }
    
    private func copyText() {
        if let content = item.content {
            NSPasteboard.general.clearContents()
            NSPasteboard.general.setString(content, forType: .string)
        }
    }
}

// MARK: - 画廊视图文件项
struct EnhancedGalleryFileItem: View {
    let item: ContentItem
    let isSelected: Bool
    let onSelectionChange: (Bool) -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        VStack(spacing: 8) {
            // 文件图标或缩略图
            fileIconOrThumbnail
                .frame(width: 60, height: 60)
            
            // 文件名
            Text(item.displayTitle)
                .font(.caption)
                .fontWeight(.medium)
                .lineLimit(2)
                .multilineTextAlignment(.center)
                .frame(height: 32)
            
            // 文件类型
            Text(getFileTypeDescription())
                .font(.caption2)
                .foregroundColor(.secondary)
                .lineLimit(1)
        }
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? Color.accentColor.opacity(0.15) : (isHovered ? Color.gray.opacity(0.1) : Color.clear))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(
                            isSelected ? Color.accentColor : Color.clear,
                            lineWidth: isSelected ? 2 : 0
                        )
                )
        )
        .contentShape(Rectangle())
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .simultaneousGesture(
            TapGesture()
                .onEnded { _ in
                    handleSingleClick()
                }
        )
    }
    
    @ViewBuilder
    private var fileIconOrThumbnail: some View {
        if item.contentTypeEnum == .image, 
           let filePath = item.filePath,
           let image = NSImage(contentsOfFile: filePath) {
            // 图片缩略图
            Image(nsImage: image)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 60, height: 60)
                .cornerRadius(8)
                .clipped()
        } else {
            // 文件图标
            Image(systemName: getFileIcon())
                .foregroundColor(getFileIconColor())
                .font(.system(size: 32))
                .frame(width: 60, height: 60)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(getFileIconColor().opacity(0.1))
                )
        }
    }
    
    private func handleSingleClick() {
        let modifierFlags = NSEvent.modifierFlags
        if modifierFlags.contains(.command) {
            onSelectionChange(!isSelected)
        } else if modifierFlags.contains(.shift) {
            onSelectionChange(true)
        } else {
            onSelectionChange(true)
        }
    }
    
    private func getFileIcon() -> String {
        switch item.contentTypeEnum {
        case .text:
            return "doc.text"
        case .image:
            return "photo"
        case .file:
            guard let fileName = item.fileName else { return "doc.fill" }
            let fileExtension = (fileName as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "xlsx", "xls":
                return "tablecells.fill"
            case "docx", "doc":
                return "doc.text.fill"
            case "pptx", "ppt":
                return "play.rectangle.fill"
            case "pdf":
                return "doc.richtext.fill"
            case "zip", "rar", "7z":
                return "archivebox.fill"
            case "mp4", "mov", "avi":
                return "play.rectangle.fill"
            case "mp3", "wav", "m4a":
                return "music.note"
            case "jpg", "jpeg", "png", "gif", "bmp":
                return "photo.fill"
            default:
                return "doc.fill"
            }
        }
    }
    
    private func getFileIconColor() -> Color {
        switch item.contentTypeEnum {
        case .text:
            return .blue
        case .image:
            return .purple
        case .file:
            guard let fileName = item.fileName else { return .accentColor }
            let fileExtension = (fileName as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "xlsx", "xls":
                return .green
            case "docx", "doc":
                return .blue
            case "pptx", "ppt":
                return .orange
            case "pdf":
                return .red
            case "zip", "rar", "7z":
                return .purple
            case "mp4", "mov", "avi":
                return .pink
            case "mp3", "wav", "m4a":
                return .cyan
            case "jpg", "jpeg", "png", "gif", "bmp":
                return .mint
            default:
                return .accentColor
            }
        }
    }
    
    private func getFileTypeDescription() -> String {
        switch item.contentTypeEnum {
        case .text:
            return "文本"
        case .image:
            return "图片"
        case .file:
            guard let fileName = item.fileName else { return "文件" }
            let fileExtension = (fileName as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "xlsx", "xls":
                return "Excel"
            case "docx", "doc":
                return "Word"
            case "pptx", "ppt":
                return "PowerPoint"
            case "pdf":
                return "PDF"
            case "zip", "rar", "7z":
                return "压缩包"
            case "mp4", "mov", "avi":
                return "视频"
            case "mp3", "wav", "m4a":
                return "音频"
            case "jpg", "jpeg", "png", "gif", "bmp":
                return "图片"
            default:
                return fileExtension.uppercased()
            }
        }
    }
}

// MARK: - 详情行组件
struct EnhancedDetailRow: View {
    let label: String
    let value: String
    let icon: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.secondary)
                .font(.system(size: 14))
                .frame(width: 16)
            
            Text(label)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .frame(width: 80, alignment: .leading)
            
            Text(value)
                .font(.subheadline)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
    }
}