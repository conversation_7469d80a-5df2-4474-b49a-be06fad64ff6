import SwiftUI
import AppKit
import UniformTypeIdentifiers
import Combine

// MARK: - 拖拽覆盖窗口
class DragDropOverlayWindow: NSWindow {
    private var hostingView: NSHostingView<DragDropOverlayView>?
    
    init(contentService: ContentService, batchService: BatchService, dragDropManager: DragDropWindowManager? = nil) {
        let contentView = DragDropOverlayView(
            contentService: contentService,
            batchService: batchService,
            dragDropManager: dragDropManager
        )
        
        super.init(
            contentRect: NSRect(x: 0, y: 0, width: 400, height: 300),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )
        
        setupWindow(contentView: contentView)
    }
    
    private func setupWindow(contentView: DragDropOverlayView) {
        isReleasedWhenClosed = false
        level = .floating
        backgroundColor = NSColor.clear
        isOpaque = false
        hasShadow = true
        ignoresMouseEvents = false
        acceptsMouseMovedEvents = true
        
        // 允许窗口拖动
        isMovableByWindowBackground = true
        
        // 设置窗口位置到屏幕左侧中间
        if let screen = NSScreen.main {
            let screenFrame = screen.visibleFrame
            let windowWidth: CGFloat = 400
            let windowHeight: CGFloat = 300
            let x = screenFrame.minX + 20 // 距离左边缘20像素
            let y = screenFrame.midY - windowHeight / 2
            
            setFrame(NSRect(x: x, y: y, width: windowWidth, height: windowHeight), display: true)
        }
        
        // 设置内容视图
        hostingView = NSHostingView(rootView: contentView)
        self.contentView = hostingView
    }
    
    override var canBecomeKey: Bool {
        return true
    }
    
    override var canBecomeMain: Bool {
        return true
    }
    
    func showWindow() {
        orderFront(nil)
        makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }
    
    func hideWindow() {
        orderOut(nil)
    }
}

// MARK: - 拖拽覆盖视图
struct DragDropOverlayView: View {
    let contentService: ContentService
    let batchService: BatchService
    weak var dragDropManager: DragDropWindowManager?
    
    @State private var isTargeted = false
    @State private var draggedFiles: [URL] = []
    @State private var showingBatchSelector = false
    @State private var selectedBatch: NSManagedObject?
    @State private var isProcessing = false
    @State private var processingMessage = ""
    // 移除复杂的权限状态标志
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏（可拖动区域）
            HStack {
                Image(systemName: "doc.badge.plus")
                    .foregroundColor(.accentColor)
                
                Text("拖拽文件到此处")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button(action: closeWindow) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                        .font(.system(size: 16))
                }
                .buttonStyle(.plain)
                .help("关闭窗口")
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(NSColor.controlBackgroundColor).opacity(0.8))
            
            // 主要内容区域
            VStack(spacing: 20) {
            
            // 拖拽区域
            dragDropArea
            
            // 处理状态显示
            if isProcessing {
                processingStatusView
            }
            
            // 批次选择（手动模式时显示）
            if !draggedFiles.isEmpty && !isProcessing && batchService.activeBatch == nil {
                batchSelectionArea
            }
            
            // 操作按钮（手动模式时显示）
            if !draggedFiles.isEmpty && !isProcessing && batchService.activeBatch == nil {
                actionButtons
            }
            
            // 自动模式提示
            if !draggedFiles.isEmpty && !isProcessing && batchService.activeBatch != nil {
                autoModeInfo
            }
            
            // 权限说明
            if draggedFiles.isEmpty && !isProcessing {
                permissionInfoView
            }
            }
            .padding(20)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.2), radius: 12, x: 0, y: 6)
        )
        .onAppear {
            print("🔄 DragDropOverlayView onAppear 被调用")
            print("🔍 当前状态 - isProcessing: \(isProcessing), draggedFiles.count: \(draggedFiles.count)")
            
            // 只在没有正在处理的情况下重置状态
            if !isProcessing {
                print("🔄 重置拖拽状态")
                draggedFiles.removeAll()
                processingMessage = ""
            }
            
            // 默认选择活跃批次，如果没有活跃批次则选择当前批次
            selectedBatch = batchService.activeBatch ?? batchService.currentBatch
        }
    }
    
    private var dragDropArea: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [
                        isTargeted ? Color.blue.opacity(0.4) : Color.gray.opacity(0.08),
                        isTargeted ? Color.blue.opacity(0.2) : Color.gray.opacity(0.03)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .strokeBorder(
                        isTargeted ? Color.blue : Color.gray.opacity(0.4),
                        style: StrokeStyle(
                            lineWidth: isTargeted ? 3 : 2,
                            dash: isTargeted ? [] : [10, 5]
                        )
                    )
                    .animation(.easeInOut(duration: 0.3), value: isTargeted)
            )
            .overlay(
                VStack(spacing: 16) {
                    // 图标和动画
                    ZStack {
                        if isTargeted {
                            Circle()
                                .fill(Color.white.opacity(0.2))
                                .frame(width: 80, height: 80)
                                .scaleEffect(isTargeted ? 1.0 : 0.8)
                                .animation(.easeInOut(duration: 0.3), value: isTargeted)
                        }

                        Image(systemName: getDropZoneIcon())
                            .font(.system(size: 48, weight: .medium))
                            .foregroundColor(getDropZoneIconColor())
                            .scaleEffect(isTargeted ? 1.2 : 1.0)
                            .animation(.spring(response: 0.4, dampingFraction: 0.6), value: isTargeted)
                    }

                    // 文本内容
                    VStack(spacing: 6) {
                        Text(getDropZoneTitle())
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(getDropZoneTitleColor())
                            .animation(.easeInOut(duration: 0.2), value: isTargeted)

                        if !isTargeted && draggedFiles.isEmpty {
                            Text("支持多文件拖拽 • 智能权限管理")
                                .font(.caption)
                                .foregroundColor(.secondary.opacity(0.8))
                        } else if !draggedFiles.isEmpty {
                            HStack(spacing: 4) {
                                Image(systemName: "doc.fill")
                                    .font(.system(size: 10))
                                Text("\(draggedFiles.count) 个文件已准备")
                            }
                            .font(.caption)
                            .foregroundColor(.green)
                        }
                    }
                }
            )
            .frame(height: 140)
            .onDrop(of: [UTType.fileURL.identifier, UTType.item.identifier, "public.file-url", "public.item"], isTargeted: $isTargeted) { providers in
                print("拖拽检测到 \(providers.count) 个项目")
                NSLog("🚀 拖拽检测到 \(providers.count) 个项目")

                // 立即显示视觉反馈
                DispatchQueue.main.async {
                    self.processingMessage = "🎯 检测到拖拽，正在处理..."
                    self.isProcessing = true
                }

                handleDropWithPasteboard(providers: providers)
                return true
            }
    }

    // MARK: - 拖拽区域辅助方法
    private func getDropZoneIcon() -> String {
        if !draggedFiles.isEmpty {
            return "checkmark.circle.fill"
        } else if isTargeted {
            return "arrow.down.circle.fill"
        } else {
            return "plus.circle.dashed"
        }
    }

    private func getDropZoneIconColor() -> Color {
        if !draggedFiles.isEmpty {
            return .green
        } else if isTargeted {
            return .white
        } else {
            return .blue
        }
    }

    private func getDropZoneTitle() -> String {
        if !draggedFiles.isEmpty {
            return "文件已选择"
        } else if isTargeted {
            return "释放以添加文件"
        } else {
            return "拖拽文件到此处"
        }
    }

    private func getDropZoneTitleColor() -> Color {
        if !draggedFiles.isEmpty {
            return .green
        } else if isTargeted {
            return .white
        } else {
            return .primary
        }
    }

    private var processingStatusView: some View {
        VStack(spacing: 16) {
            // 动画进度指示器
            ZStack {
                Circle()
                    .stroke(Color.blue.opacity(0.2), lineWidth: 3)
                    .frame(width: 40, height: 40)

                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                    .scaleEffect(1.0)
            }

            // 状态消息
            VStack(spacing: 4) {
                Text(getProcessingTitle())
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(processingMessage)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }

            // 进度提示
            if !draggedFiles.isEmpty {
                HStack {
                    Image(systemName: "doc.fill")
                        .foregroundColor(.blue)
                        .font(.system(size: 12))

                    Text("\(draggedFiles.count) 个文件待处理")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(20)
        .frame(minHeight: 120)
    }

    private func getProcessingTitle() -> String {
        if processingMessage.contains("权限") {
            return "🔐 权限处理中"
        } else if processingMessage.contains("添加") {
            return "📁 文件处理中"
        } else if processingMessage.contains("完成") {
            return "✅ 处理完成"
        } else {
            return "⚡ 处理中"
        }
    }
    
    private var autoModeInfo: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "star.fill")
                    .foregroundColor(.orange)
                    .font(.system(size: 16))
                
                Text("自动添加到活跃批次")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            
            if let activeBatch = batchService.activeBatch {
                let batchName = activeBatch.value(forKey: "name") as? String ?? "活跃批次"
                Text("目标批次: \(batchName)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.orange.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(Color.orange.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    private var batchSelectionArea: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("选择目标批次")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Menu {
                ForEach(batchService.batches, id: \.objectID) { batch in
                    let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
                    let isCurrentBatch = batch == selectedBatch
                    let isActiveBatch = batch == batchService.activeBatch
                    
                    Button(action: {
                        selectedBatch = batch
                    }) {
                        HStack {
                            Text(batchName)
                            if isActiveBatch {
                                Image(systemName: "star.fill")
                                    .foregroundColor(.orange)
                            }
                            if isCurrentBatch {
                                Image(systemName: "checkmark")
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                }
            } label: {
                HStack {
                    Text(selectedBatch?.value(forKey: "name") as? String ?? "选择批次")
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.down")
                        .foregroundColor(.secondary)
                        .font(.system(size: 12))
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(NSColor.controlBackgroundColor))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                )
            }
            .menuStyle(.borderlessButton)
        }
    }
    
    private var actionButtons: some View {
        HStack(spacing: 12) {
            Button("取消") {
                // 清除所有状态
                draggedFiles.removeAll()
                isProcessing = false
                processingMessage = ""
                closeWindow()
            }
            .buttonStyle(.bordered)
            
            // 如果没有拖拽文件，显示选择文件按钮
            if draggedFiles.isEmpty {
                Button("选择文件") {
                    selectFiles()
                }
                .buttonStyle(.borderedProminent)
                .disabled(isProcessing)
            } else {
                Button("添加到批次") {
                    addFilesToBatch()
                }
                .buttonStyle(.borderedProminent)
                .disabled(selectedBatch == nil || isProcessing)
            }
        }
    }
    
    private var permissionInfoView: some View {
        VStack(spacing: 12) {
            // 标题部分
            HStack {
                Image(systemName: "shield.checkered")
                    .foregroundColor(.blue)
                    .font(.system(size: 16, weight: .medium))

                Text("智能权限管理")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.system(size: 12))
            }

            // 功能说明
            VStack(spacing: 6) {
                HStack(spacing: 8) {
                    Image(systemName: "1.circle.fill")
                        .foregroundColor(.blue)
                        .font(.system(size: 12))

                    Text("拖拽文件到此处")
                        .font(.caption2)
                        .foregroundColor(.primary)

                    Spacer()
                }

                HStack(spacing: 8) {
                    Image(systemName: "2.circle.fill")
                        .foregroundColor(.blue)
                        .font(.system(size: 12))

                    Text("自动检测权限状态")
                        .font(.caption2)
                        .foregroundColor(.primary)

                    Spacer()
                }

                HStack(spacing: 8) {
                    Image(systemName: "3.circle.fill")
                        .foregroundColor(.blue)
                        .font(.system(size: 12))

                    Text("智能引导授权（仅需一次）")
                        .font(.caption2)
                        .foregroundColor(.primary)

                    Spacer()
                }
            }

            // 底部提示
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.orange)
                    .font(.system(size: 10))

                Text("支持多文件拖拽，自动记住权限设置")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)

                Spacer()
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.blue.opacity(0.08),
                            Color.blue.opacity(0.03)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .strokeBorder(Color.blue.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 专业的拖拽处理方法（使用NSPasteboard + 权限管理器）
    private func handleDropWithPasteboard(providers: [NSItemProvider]) {
        print("🚀 开始专业拖拽处理，提供者数量: \(providers.count)")
        NSLog("🚀 开始专业拖拽处理，提供者数量: \(providers.count)")
        print("🔍 当前状态 - isProcessing: \(isProcessing), draggedFiles.count: \(draggedFiles.count)")
        print("🔍 当前活跃批次: \(batchService.activeBatch?.value(forKey: "name") as? String ?? "无")")
        NSLog("🔍 当前活跃批次: \(batchService.activeBatch?.value(forKey: "name") as? String ?? "无")")

        // 更新UI状态
        DispatchQueue.main.async {
            self.processingMessage = "🔍 正在分析拖拽的文件..."
        }

        // 如果正在处理，忽略新的拖拽
        guard !isProcessing else {
            print("⚠️ 正在处理中，忽略新的拖拽操作")
            DispatchQueue.main.async {
                self.processingMessage = "⚠️ 正在处理中，请稍候..."
            }
            return
        }

        draggedFiles.removeAll()

        // 使用拖拽剪贴板获取安全作用域资源
        let dragPasteboard = NSPasteboard(name: .drag)
        print("📋 拖拽剪贴板类型: \(dragPasteboard.types ?? [])")

        // 尝试从拖拽剪贴板获取文件URL
        if let fileURLs = dragPasteboard.readObjects(forClasses: [NSURL.self], options: nil) as? [URL] {
            print("✅ 从拖拽剪贴板获取到 \(fileURLs.count) 个URL")
            for (index, url) in fileURLs.enumerated() {
                print("📄 文件 \(index + 1): \(url.lastPathComponent) - \(url.path)")
            }

            // 首先尝试使用已保存的权限恢复文件访问
            var authorizedURLs: [URL] = []
            var needsAuthorizationURLs: [URL] = []

            // 对于拖拽的文件，使用增强的权限检查
            for url in fileURLs {
                print("🔍 检查拖拽文件权限: \(url.lastPathComponent)")

                // 首先尝试从书签恢复权限
                if let bookmarkURL = PermissionManager.shared.restoreFromBookmark(for: url.path) {
                    print("📖 找到书签，尝试恢复权限")
                    let needsSecurityScope = bookmarkURL.startAccessingSecurityScopedResource()
                    defer {
                        if needsSecurityScope {
                            bookmarkURL.stopAccessingSecurityScopedResource()
                        }
                    }
                    
                    do {
                        _ = try Data(contentsOf: bookmarkURL, options: [.mappedIfSafe])
                        authorizedURLs.append(bookmarkURL)
                        print("✅ 从书签恢复权限成功: \(url.lastPathComponent)")
                        continue
                    } catch {
                        print("❌ 书签权限已失效: \(url.lastPathComponent) - \(error)")
                    }
                }

                // 尝试拖拽文件的安全作用域访问
                print("🔍 尝试拖拽文件的安全作用域访问: \(url.lastPathComponent)")
                let needsSecurityScope = url.startAccessingSecurityScopedResource()
                defer {
                    if needsSecurityScope {
                        url.stopAccessingSecurityScopedResource()
                    }
                }
                
                do {
                    // 使用更严格的访问测试
                    let testData = try Data(contentsOf: url, options: [.mappedIfSafe])
                    authorizedURLs.append(url)
                    print("✅ 拖拽文件访问成功: \(url.lastPathComponent) (大小: \(testData.count) bytes)")
                } catch {
                    needsAuthorizationURLs.append(url)
                    print("❌ 拖拽文件需要授权: \(url.lastPathComponent) - \(error)")
                }
            }

            print("📊 权限检查结果 - 已授权: \(authorizedURLs.count), 需要授权: \(needsAuthorizationURLs.count)")

            // 如果所有文件都已有权限，直接处理
            if needsAuthorizationURLs.isEmpty && !authorizedURLs.isEmpty {
                print("🎉 所有文件都已有权限，直接处理")
                processAuthorizedFiles(authorizedURLs)
                return
            }

            // 如果有部分文件需要授权
            if !needsAuthorizationURLs.isEmpty {
                print("🔐 有 \(needsAuthorizationURLs.count) 个文件需要授权，启动权限引导流程")
                PermissionManager.shared.showPermissionGuide(for: needsAuthorizationURLs) { newlyAuthorizedURLs in
                    if let newURLs = newlyAuthorizedURLs {
                        print("✅ 权限引导完成，获得 \(newURLs.count) 个新授权文件")
                        // 合并已有权限的文件和新授权的文件
                        let allAuthorizedURLs = authorizedURLs + newURLs
                        self.processAuthorizedFiles(allAuthorizedURLs)
                    } else {
                        print("❌ 权限引导取消")
                        // 如果用户取消了授权，但还有已授权的文件，询问是否继续
                        if !authorizedURLs.isEmpty {
                            self.showPartialAuthorizationDialog(authorizedCount: authorizedURLs.count, totalCount: fileURLs.count) { shouldContinue in
                                if shouldContinue {
                                    self.processAuthorizedFiles(authorizedURLs)
                                } else {
                                    self.showAuthorizationCancelledMessage()
                                }
                            }
                        } else {
                            self.showAuthorizationCancelledMessage()
                        }
                    }
                }
            } else {
                print("❌ 没有任何有效文件")
                showNoValidFilesMessage()
            }
        } else {
            print("❌ 无法从拖拽剪贴板获取文件URL，回退到原始方法")
            handleDrop(providers: providers)
        }
    }
    
    // MARK: - 处理已授权的文件
    private func processAuthorizedFiles(_ urls: [URL]) {
        print("🔐 开始处理已授权的文件: \(urls.count) 个")
        print("🔍 当前状态 - isProcessing: \(isProcessing), draggedFiles.count: \(draggedFiles.count)")
        print("🔍 当前活跃批次: \(batchService.activeBatch?.value(forKey: "name") as? String ?? "无")")

        // 使用权限管理器处理授权文件（创建书签）
        print("📝 调用权限管理器处理授权文件")
        let processedURLs = PermissionManager.shared.processAuthorizedFiles(urls)
        print("📝 权限管理器返回 \(processedURLs.count) 个处理后的URL")

        var validFiles: [URL] = []

        for (index, url) in processedURLs.enumerated() {
            print("🔍 处理已授权文件 \(index + 1): \(url.lastPathComponent)")
            print("🔍 URL路径: \(url.path)")
            print("🔍 URL是否为文件URL: \(url.isFileURL)")

            if url.isFileURL {
                // 对于拖拽的文件，不要在这里开始安全作用域访问
                // 而是在实际使用时再开始，避免过早释放
                let fileExists = FileManager.default.fileExists(atPath: url.path)
                print("📁 已授权文件存在: \(fileExists)")

                if fileExists {
                    validFiles.append(url)
                    print("✅ 添加已授权文件: \(url.lastPathComponent)")
                } else {
                    print("❌ 已授权文件不存在: \(url.path)")
                }
            } else {
                print("❌ URL不是文件URL: \(url)")
            }
        }

        draggedFiles = validFiles
        print("🏁 最终获取到 \(draggedFiles.count) 个已授权文件")

        if !draggedFiles.isEmpty {
            print("✅ 有有效文件，继续处理")

            // 重置自动隐藏计时器
            if let windowManager = dragDropManager {
                windowManager.keepWindowVisible()
                print("⏰ 重置窗口自动隐藏计时器")
            }

            // 如果有活跃批次，自动添加文件
            if let activeBatch = batchService.activeBatch {
                let batchName = activeBatch.value(forKey: "name") as? String ?? "活跃批次"
                print("🎯 检测到活跃批次: \(batchName)，准备自动添加已授权文件")
                print("🎯 draggedFiles.count: \(draggedFiles.count), validFiles.count: \(validFiles.count)")
                selectedBatch = activeBatch

                // 立即自动添加到活跃批次
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    print("🚀 开始执行自动添加已授权文件到活跃批次")
                    print("🚀 当前 draggedFiles.count: \(self.draggedFiles.count)")
                    self.addFilesToActiveBatch()
                }
            } else {
                print("⚠️ 没有活跃批次，显示手动选择界面")
                selectedBatch = batchService.currentBatch
            }
        } else {
            print("❌ 没有获取到任何有效的已授权文件")
            showNoValidFilesMessage()
        }
    }
    
    // MARK: - 显示授权取消消息
    private func showAuthorizationCancelledMessage() {
        DispatchQueue.main.async {
            self.processingMessage = "❌ 用户取消了文件访问授权"
            self.isProcessing = false
            
            // 清除拖拽文件状态
            self.draggedFiles.removeAll()
            
            // 3秒后自动关闭窗口
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                self.closeWindow()
            }
        }
    }
    
    // MARK: - 显示无有效文件消息
    private func showNoValidFilesMessage() {
        DispatchQueue.main.async {
            self.processingMessage = "❌ 没有找到有效的文件"
            self.isProcessing = false
            
            // 清除拖拽文件状态
            self.draggedFiles.removeAll()
            
            // 3秒后自动关闭窗口
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                self.closeWindow()
            }
        }
    }
    
    // MARK: - 显示部分授权对话框
    private func showPartialAuthorizationDialog(authorizedCount: Int, totalCount: Int, completion: @escaping (Bool) -> Void) {
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "部分文件已授权"
            alert.informativeText = """
            在 \(totalCount) 个文件中，有 \(authorizedCount) 个文件已获得访问权限。
            
            您可以选择：
            • 继续处理已授权的文件
            • 取消本次操作
            """
            alert.alertStyle = .informational
            alert.addButton(withTitle: "继续处理已授权文件")
            alert.addButton(withTitle: "取消")
            
            let response = alert.runModal()
            completion(response == .alertFirstButtonReturn)
        }
    }
    
    // MARK: - 原始拖拽处理方法（作为备用）
    private func handleDrop(providers: [NSItemProvider]) {
        print("🚀 开始处理拖拽，提供者数量: \(providers.count)")
        draggedFiles.removeAll()
        
        Task {
            var collectedFiles: [URL] = []
            
            for (index, provider) in providers.enumerated() {
                print("🔍 处理提供者 \(index + 1)/\(providers.count)")
                print("📋 支持的类型: \(provider.registeredTypeIdentifiers)")
                
                // 尝试不同的类型标识符，按优先级排序
                let typeIdentifiers = [
                    UTType.fileURL.identifier,
                    "public.file-url",
                    UTType.url.identifier,
                    "public.url",
                    UTType.item.identifier
                ]
                
                var fileProcessed = false
                
                for typeId in typeIdentifiers {
                    if provider.hasItemConformingToTypeIdentifier(typeId) {
                        print("✅ 找到支持的类型: \(typeId)")
                        do {
                            let item = try await provider.loadItem(forTypeIdentifier: typeId, options: nil)
                            print("📦 加载的项目类型: \(type(of: item))")
                            
                            // 处理直接的URL对象
                            if let url = item as? URL {
                                print("🎯 直接获取URL: \(url.path)")
                                print("🎯 URL绝对字符串: \(url.absoluteString)")
                                if url.isFileURL {
                                    // 立即开始安全作用域访问，不要立即释放
                                    let needsSecurityScope = url.startAccessingSecurityScopedResource()
                                    print("🎯 开始安全作用域访问: \(needsSecurityScope)")
                                    
                                    let fileExists = FileManager.default.fileExists(atPath: url.path)
                                    print("🎯 文件存在检查: \(fileExists)")
                                    
                                    if fileExists {
                                        // 不要立即释放安全作用域访问，让它保持到文件处理完成
                                        collectedFiles.append(url)
                                        fileProcessed = true
                                        break
                                    } else if needsSecurityScope {
                                        // 只有在文件不存在时才释放
                                        url.stopAccessingSecurityScopedResource()
                                    }
                                }
                            }
                            // 处理Data类型（可能包含URL字符串）
                            else if let data = item as? Data {
                                print("📄 处理Data类型，大小: \(data.count) bytes")
                                
                                // 尝试解析为URL字符串
                                if let urlString = String(data: data, encoding: .utf8) {
                                    print("📝 解析的字符串: \(urlString)")
                                    
                                    // 处理file://协议的URL
                                    if let url = URL(string: urlString), url.isFileURL {
                                        print("🔗 从字符串创建URL: \(url.path)")
                                        let needsSecurityScope = url.startAccessingSecurityScopedResource()
                                        let fileExists = FileManager.default.fileExists(atPath: url.path)
                                        if needsSecurityScope {
                                            url.stopAccessingSecurityScopedResource()
                                        }
                                        
                                        if fileExists {
                                            collectedFiles.append(url)
                                            fileProcessed = true
                                            break
                                        }
                                    }
                                    // 处理直接的文件路径
                                    else if urlString.hasPrefix("/") {
                                        let url = URL(fileURLWithPath: urlString)
                                        print("📁 从路径创建URL: \(url.path)")
                                        let needsSecurityScope = url.startAccessingSecurityScopedResource()
                                        let fileExists = FileManager.default.fileExists(atPath: url.path)
                                        if needsSecurityScope {
                                            url.stopAccessingSecurityScopedResource()
                                        }
                                        
                                        if fileExists {
                                            collectedFiles.append(url)
                                            fileProcessed = true
                                            break
                                        }
                                    }
                                }
                                
                                // 尝试直接从Data创建URL
                                if let url = URL(dataRepresentation: data, relativeTo: nil) {
                                    print("🔄 从Data表示创建URL: \(url.path)")
                                    if url.isFileURL {
                                        let needsSecurityScope = url.startAccessingSecurityScopedResource()
                                        let fileExists = FileManager.default.fileExists(atPath: url.path)
                                        if needsSecurityScope {
                                            url.stopAccessingSecurityScopedResource()
                                        }
                                        
                                        if fileExists {
                                            collectedFiles.append(url)
                                            fileProcessed = true
                                            break
                                        }
                                    }
                                }
                            }
                            // 处理NSString类型
                            else if let urlString = item as? String {
                                print("📝 处理字符串类型: \(urlString)")
                                if let url = URL(string: urlString), url.isFileURL {
                                    let needsSecurityScope = url.startAccessingSecurityScopedResource()
                                    let fileExists = FileManager.default.fileExists(atPath: url.path)
                                    if needsSecurityScope {
                                        url.stopAccessingSecurityScopedResource()
                                    }
                                    
                                    if fileExists {
                                        collectedFiles.append(url)
                                        fileProcessed = true
                                        break
                                    }
                                }
                            }
                            
                        } catch {
                            print("❌ 加载类型 \(typeId) 失败: \(error)")
                        }
                    }
                }
                
                if !fileProcessed {
                    print("⚠️ 提供者 \(index + 1) 未能处理任何文件")
                }
            }
            
            await MainActor.run {
                draggedFiles = collectedFiles
                print("🏁 最终获取到 \(draggedFiles.count) 个文件:")
                for (index, file) in draggedFiles.enumerated() {
                    print("  \(index + 1). \(file.lastPathComponent) (\(file.path))")
                }
                
                if !draggedFiles.isEmpty {
                    // 重置自动隐藏计时器
                    if let windowManager = dragDropManager {
                        windowManager.keepWindowVisible()
                    }
                    
                    // 如果有活跃批次，自动添加文件
                    if let activeBatch = batchService.activeBatch {
                        let batchName = activeBatch.value(forKey: "name") as? String ?? "活跃批次"
                        print("🎯 检测到活跃批次: \(batchName)，准备自动添加文件")
                        selectedBatch = activeBatch
                        
                        // 立即自动添加到活跃批次
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            print("🚀 开始执行自动添加到活跃批次")
                            self.addFilesToActiveBatch()
                        }
                    } else {
                        print("⚠️ 没有活跃批次，显示手动选择界面")
                        // 如果没有活跃批次，显示选择界面
                        selectedBatch = batchService.currentBatch
                    }
                } else {
                    print("❌ 没有获取到任何有效文件")
                }
            }
        }
    }
    
    private func selectFiles() {
        print("🔍 开始手动选择文件")
        
        let openPanel = NSOpenPanel()
        openPanel.title = "选择要添加的文件"
        openPanel.message = "请选择要添加到批次的文件："
        openPanel.prompt = "选择"
        openPanel.allowsMultipleSelection = true
        openPanel.canChooseDirectories = false
        openPanel.canChooseFiles = true
        
        openPanel.begin { response in
            if response == .OK {
                let selectedURLs = openPanel.urls
                print("✅ 用户选择了 \(selectedURLs.count) 个文件")
                
                // 通过 NSOpenPanel 选择的文件已经获得了用户授权
                // 使用 PermissionManager 处理这些已授权的文件
                let processedURLs = PermissionManager.shared.processAuthorizedFiles(selectedURLs)
                
                DispatchQueue.main.async {
                    self.draggedFiles = processedURLs
                    print("📁 已设置授权后的 draggedFiles: \(self.draggedFiles.map { $0.lastPathComponent })")
                }
            } else {
                print("❌ 用户取消了文件选择")
            }
        }
    }
    
    private func addFilesToBatch() {
        guard let batch = selectedBatch else { 
            print("❌ 没有选择批次")
            return 
        }
        
        isProcessing = true
        processingMessage = "正在添加文件到批次..."
        
        let batchName = batch.value(forKey: "name") as? String ?? "批次"
        print("📁 开始手动添加 \(draggedFiles.count) 个文件到批次: \(batchName)")
        
        Task {
            var successCount = 0
            
            for (index, url) in draggedFiles.enumerated() {
                do {
                    print("📄 处理文件 \(index + 1)/\(draggedFiles.count): \(url.lastPathComponent)")
                    
                    // 开始安全作用域访问并保持到处理完成
                    let needsSecurityScope = url.startAccessingSecurityScopedResource()
                    print("🔐 开始文件处理的安全作用域访问: \(needsSecurityScope)")
                    
                    defer {
                        if needsSecurityScope {
                            url.stopAccessingSecurityScopedResource()
                            print("🔐 文件处理完成，释放安全作用域访问")
                        }
                    }
                    
                    let fileExists = FileManager.default.fileExists(atPath: url.path)
                    guard fileExists else {
                        print("❌ 文件不存在: \(url.path)")
                        continue
                    }
                    
                    let contentData = try ContentData.fromFile(at: url)
                    print("✅ 成功创建ContentData: \(String(describing: contentData.title))")
                    
                    // 添加到指定批次
                    let addedContent = try await contentService.addContent(contentData, toBatch: batch)
                    successCount += 1
                    
                    print("✅ 成功添加文件到批次: \(url.lastPathComponent) -> \(addedContent.value(forKey: "title") as? String ?? "未知标题")")
                    
                    // 更新进度消息
                    await MainActor.run {
                        processingMessage = "已添加 \(successCount)/\(draggedFiles.count) 个文件..."
                    }
                    
                } catch {
                    print("❌ 添加文件失败: \(url.lastPathComponent) - \(error)")
                    NSLog("添加文件失败: \(url.lastPathComponent) - \(error)")

                    await MainActor.run {
                        handleFileProcessingError(error: error, fileName: url.lastPathComponent)
                    }
                }
            }
            
            await MainActor.run {
                isProcessing = false
                let finalMessage = successCount > 0 ? 
                    "✅ 成功添加 \(successCount) 个文件到 \(batchName)" : 
                    "❌ 没有文件被成功添加"
                processingMessage = finalMessage
                
                print("🏁 完成手动添加操作，成功: \(successCount)/\(draggedFiles.count)")
                
                // 清除拖拽文件状态
                draggedFiles.removeAll()
                
                // 显示结果后自动关闭
                let delay = successCount > 0 ? 2.0 : 3.0
                DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                    self.closeWindow()
                }
            }
        }
    }
    
    private func addFilesToActiveBatch() {
        print("🚀 addFilesToActiveBatch 开始执行")
        print("🔍 当前 draggedFiles.count: \(draggedFiles.count)")
        
        guard let activeBatch = batchService.activeBatch else {
            print("❌ 没有活跃批次")
            return
        }
        
        guard !draggedFiles.isEmpty else {
            print("❌ 没有要处理的文件")
            return
        }
        
        isProcessing = true
        processingMessage = "正在自动添加文件到活跃批次..."
        
        let batchName = activeBatch.value(forKey: "name") as? String ?? "活跃批次"
        print("🎯 开始自动添加 \(draggedFiles.count) 个文件到活跃批次: \(batchName)")
        
        Task {
            var successCount = 0
            
            for (index, url) in draggedFiles.enumerated() {
                do {
                    print("📄 处理文件 \(index + 1)/\(draggedFiles.count): \(url.lastPathComponent)")
                    
                    // 开始安全作用域访问并保持到处理完成
                    let needsSecurityScope = url.startAccessingSecurityScopedResource()
                    print("🔐 开始文件处理的安全作用域访问: \(needsSecurityScope)")
                    
                    defer {
                        if needsSecurityScope {
                            url.stopAccessingSecurityScopedResource()
                            print("🔐 文件处理完成，释放安全作用域访问")
                        }
                    }
                    
                    let fileExists = FileManager.default.fileExists(atPath: url.path)
                    guard fileExists else {
                        print("❌ 文件不存在: \(url.path)")
                        continue
                    }
                    
                    let contentData = try ContentData.fromFile(at: url)
                    print("✅ 成功创建ContentData: \(String(describing: contentData.title))")
                    
                    // 添加到活跃批次
                    let addedContent = try await contentService.addContent(contentData, toBatch: activeBatch)
                    successCount += 1
                    
                    print("✅ 成功添加文件到活跃批次: \(url.lastPathComponent) -> \(addedContent.value(forKey: "title") as? String ?? "未知标题")")
                    
                    // 更新进度消息
                    await MainActor.run {
                        processingMessage = "已添加 \(successCount)/\(draggedFiles.count) 个文件到 \(batchName)..."
                    }
                    
                } catch {
                    print("❌ 添加文件失败: \(url.lastPathComponent) - \(error)")
                    NSLog("添加文件失败: \(url.lastPathComponent) - \(error)")

                    await MainActor.run {
                        handleFileProcessingError(error: error, fileName: url.lastPathComponent)
                    }
                }
            }
            
            await MainActor.run {
                isProcessing = false
                let finalMessage = successCount > 0 ? 
                    "✅ 成功添加 \(successCount) 个文件到 \(batchName)" : 
                    "❌ 没有文件被成功添加"
                processingMessage = finalMessage
                
                print("🏁 完成自动添加操作，成功: \(successCount)/\(draggedFiles.count)")
                
                // 清除拖拽文件状态
                draggedFiles.removeAll()
                
                // 显示结果后自动关闭
                let delay = successCount > 0 ? 1.5 : 3.0
                DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                    self.closeWindow()
                }
            }
        }
    }
    
    // MARK: - 简化权限处理方法
    private func handlePermissionRequest(for files: [URL]) async {
        print("🔐 简化权限处理: \(files.count) 个文件")
        
        await MainActor.run {
            processingMessage = "正在请求文件访问权限..."
        }
        
        // 简化权限处理，直接显示权限引导
        PermissionManager.shared.showPermissionGuide(for: files) { authorizedURLs in
            Task {
                if let authorizedURLs = authorizedURLs, !authorizedURLs.isEmpty {
                    print("✅ 权限授权成功，继续处理文件")
                    
                    await MainActor.run {
                        draggedFiles = authorizedURLs
                        processingMessage = "权限授权成功，继续添加文件..."
                    }
                    
                    // 简单重试文件添加
                    if let activeBatch = batchService.activeBatch {
                        await MainActor.run {
                            selectedBatch = activeBatch
                        }
                        addFilesToActiveBatch()
                    } else {
                        addFilesToBatch()
                    }
                    
                } else {
                    print("❌ 权限授权失败或取消")
                    await MainActor.run {
                        processingMessage = "权限授权取消"
                        isProcessing = false
                        
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                            closeWindow()
                        }
                    }
                }
            }
        }
    }
    
    private func closeWindow() {
        // 清除所有状态
        draggedFiles.removeAll()
        isProcessing = false
        processingMessage = ""

        dragDropManager?.hideWindow()
    }

    // MARK: - 错误处理
    private func handleFileProcessingError(error: Error, fileName: String) {
        if let contentError = error as? ContentManagerError {
            switch contentError {
            case .permissionDenied(let message):
                print("🔐 权限错误: \(message)")
                processingMessage = "权限不足，跳过文件: \(fileName)"

            case .fileNotFound(let path):
                print("📁 文件未找到: \(path)")
                processingMessage = "文件未找到: \(fileName)"

            case .contentTooLarge(let size):
                print("📏 文件过大: \(size)")
                processingMessage = "文件过大，跳过: \(fileName)"

            case .invalidContent(let message):
                print("📄 内容无效: \(message)")
                processingMessage = "文件内容无效，跳过: \(fileName)"

            default:
                print("❌ 其他错误: \(contentError)")
                processingMessage = "处理失败，跳过: \(fileName)"
            }
        } else {
            print("❌ 未知错误: \(error)")
            processingMessage = "未知错误，跳过: \(fileName)"
        }

        // 短暂显示错误信息后继续
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // 错误信息会在下一个文件处理时被覆盖
        }
    }
}