import SwiftUI

// MARK: - Settings View
struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var hotkeyService: HotkeyService
    @EnvironmentObject private var pasteboardMonitor: PasteboardMonitor
    @EnvironmentObject private var contentService: ContentService
    @EnvironmentObject private var batchService: BatchService
    
    @State private var selectedTab: SettingsTab = .general
    
    enum SettingsTab: String, CaseIterable {
        case general = "通用"
        case hotkeys = "快捷键"
        case smartOrganization = "智能整理"
        case preview = "预览设置"
        case cloudSync = "云同步"
        case storage = "存储"
        case export = "导出"
        case advanced = "高级"
        
        var systemImage: String {
            switch self {
            case .general: return "gear"
            case .hotkeys: return "keyboard"
            case .smartOrganization: return "brain.head.profile"
            case .preview: return "eye"
            case .cloudSync: return "icloud"
            case .storage: return "internaldrive"
            case .export: return "square.and.arrow.up"
            case .advanced: return "wrench.and.screwdriver"
            }
        }
    }
    
    var body: some View {
        NavigationSplitView {
            // Sidebar
            List(SettingsTab.allCases, id: \.self, selection: $selectedTab) { tab in
                Label(tab.rawValue, systemImage: tab.systemImage)
                    .tag(tab)
            }
            .navigationTitle("设置")
            .frame(minWidth: 200)
            
        } detail: {
            // Detail view
            Group {
                switch selectedTab {
                case .general:
                    GeneralSettingsView()
                case .hotkeys:
                    HotkeySettingsView()
                case .smartOrganization:
                    SmartOrganizationView(contentService: contentService)
                case .preview:
                    PreviewSettingsView()
                case .cloudSync:
                    CloudSyncView(contentService: contentService, batchService: batchService)
                case .storage:
                    StorageSettingsView()
                case .export:
                    ExportSettingsView()
                case .advanced:
                    AdvancedSettingsView()
                }
            }
            .frame(minWidth: 500, minHeight: 400)
        }
        .frame(width: 800, height: 600)
    }
}

// MARK: - General Settings
struct GeneralSettingsView: View {
    @AppStorage("defaultStorageMode") private var defaultStorageMode: String = "temporary"
    @AppStorage("autoStartMonitoring") private var autoStartMonitoring: Bool = true
    @AppStorage("showNotifications") private var showNotifications: Bool = true
    @AppStorage("autoCleanupEnabled") private var autoCleanupEnabled: Bool = true
    @AppStorage("cleanupInterval") private var cleanupInterval: Int = 30
    
    var body: some View {
        Form {
            Section("默认设置") {
                Picker("默认存储模式", selection: $defaultStorageMode) {
                    Text("临时存储").tag("temporary")
                    Text("永久存储").tag("permanent")
                }
                .pickerStyle(.segmented)
                
                Toggle("启动时自动开始监听剪贴板", isOn: $autoStartMonitoring)
                Toggle("显示通知", isOn: $showNotifications)
            }
            
            Section("自动清理") {
                Toggle("启用自动清理", isOn: $autoCleanupEnabled)
                
                if autoCleanupEnabled {
                    HStack {
                        Text("清理间隔:")
                        Stepper(value: $cleanupInterval, in: 1...365) {
                            Text("\(cleanupInterval) 天")
                        }
                    }
                }
            }
            
            Section("应用信息") {
                HStack {
                    Text("版本:")
                    Spacer()
                    Text("1.0.0")
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("构建:")
                    Spacer()
                    Text("2024.01.01")
                        .foregroundColor(.secondary)
                }
            }
        }
        .formStyle(.grouped)
        .navigationTitle("通用设置")
    }
}

// MARK: - Hotkey Settings
struct HotkeySettingsView: View {
    @EnvironmentObject private var hotkeyService: HotkeyService
    @State private var showingPermissionAlert = false
    
    var body: some View {
        Form {
            Section("全局快捷键") {
                HStack {
                    Text("快速粘贴:")
                    Spacer()
                    Text("⌘⇧V")
                        .font(.monospaced(.body)())
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.secondary.opacity(0.1))
                        .cornerRadius(4)
                }
                
                HStack {
                    Text("当前快捷键:")
                    Spacer()
                    Text(hotkeyService.currentHotkey)
                        .foregroundColor(.secondary)
                        .font(.monospaced(.body)())
                }

                // 权限状态和请求按钮
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("辅助功能权限:")
                        Text(hotkeyService.isEnabled ? "已授权" : "未授权")
                            .font(.caption)
                            .foregroundColor(hotkeyService.isEnabled ? .green : .orange)
                    }
                    Spacer()
                    if !hotkeyService.isEnabled {
                        Button("请求权限") {
                            _ = hotkeyService.checkAndRequestPermissions()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }

                Toggle("启用全局快捷键", isOn: .constant(hotkeyService.isEnabled))
                    .disabled(!hotkeyService.isEnabled)
            }
            
            Section("权限") {
                HStack {
                    VStack(alignment: .leading) {
                        Text("辅助功能权限")
                            .font(.headline)
                        Text("需要此权限才能使用全局快捷键功能")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    if hotkeyService.isEnabled {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                    } else {
                        Button("授权") {
                            showingPermissionAlert = true
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
            }
            
            Section("说明") {
                Text("全局快捷键允许您在任何应用中快速打开粘贴窗口。请确保选择的快捷键不与其他应用冲突。")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .formStyle(.grouped)
        .navigationTitle("快捷键设置")
        .alert("需要权限", isPresented: $showingPermissionAlert) {
            Button("打开系统偏好设置") {
                let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")!
                NSWorkspace.shared.open(url)
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("请在系统偏好设置 > 安全性与隐私 > 辅助功能中授权此应用使用辅助功能。")
        }
    }
}

// MARK: - Storage Settings
struct StorageSettingsView: View {
    @EnvironmentObject private var contentService: ContentService
    @State private var storageStats: StorageStatistics?
    @State private var isLoadingStats = false
    @State private var showingCleanupConfirmation = false
    
    var body: some View {
        Form {
            Section("存储统计") {
                if let stats = storageStats {
                    StorageStatsView(stats: stats)
                } else if isLoadingStats {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("加载中...")
                    }
                } else {
                    Button("刷新统计") {
                        loadStorageStats()
                    }
                }
            }
            
            Section("存储管理") {
                Button("清理过期内容") {
                    showingCleanupConfirmation = true
                }
                .buttonStyle(.borderedProminent)
                
                Button("清理孤立文件") {
                    cleanupOrphanedFiles()
                }
                
                Button("压缩数据库") {
                    compactDatabase()
                }
            }
            
            Section("存储位置") {
                HStack {
                    Text("应用数据:")
                    Spacer()
                    Text("~/Library/Application Support/TempBox")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Button("在 Finder 中显示") {
                    showInFinder()
                }
            }
        }
        .formStyle(.grouped)
        .navigationTitle("存储设置")
        .onAppear {
            loadStorageStats()
        }
        .alert("确认清理", isPresented: $showingCleanupConfirmation) {
            Button("清理", role: .destructive) {
                cleanupExpiredContent()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("这将删除所有过期的内容，此操作无法撤销。")
        }
    }
    
    private func loadStorageStats() {
        isLoadingStats = true
        Task {
            do {
                let stats = try await contentService.getStorageStatistics()
                await MainActor.run {
                    self.storageStats = stats
                    self.isLoadingStats = false
                }
            } catch {
                await MainActor.run {
                    self.isLoadingStats = false
                }
            }
        }
    }
    
    private func cleanupExpiredContent() {
        Task {
            do {
                try await contentService.cleanupExpiredContent()
                loadStorageStats()
            } catch {
                print("Cleanup failed: \(error)")
            }
        }
    }
    
    private func cleanupOrphanedFiles() {
        Task {
            do {
                try await contentService.cleanupOrphanedFiles()
                loadStorageStats()
            } catch {
                print("Orphaned files cleanup failed: \(error)")
            }
        }
    }
    
    private func compactDatabase() {
        Task {
            do {
                try await PersistenceController.shared.vacuumDatabase()
                loadStorageStats()
            } catch {
                print("Database compaction failed: \(error)")
            }
        }
    }
    
    private func showInFinder() {
        let appSupportURL = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        let contentManagerURL = appSupportURL.appendingPathComponent("TempBox")
        NSWorkspace.shared.selectFile(nil, inFileViewerRootedAtPath: contentManagerURL.path)
    }
}

// MARK: - Storage Stats View
struct StorageStatsView: View {
    let stats: StorageStatistics
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            StatRow(label: "总项目数", value: "\(stats.totalItems)")
            StatRow(label: "文字项目", value: "\(stats.textItems)")
            StatRow(label: "图片项目", value: "\(stats.imageItems)")
            StatRow(label: "文件项目", value: "\(stats.fileItems)")
            StatRow(label: "过期项目", value: "\(stats.expiredItems)")
            StatRow(label: "孤立文件", value: "\(stats.orphanedFiles)")
            StatRow(label: "总大小", value: stats.formattedTotalSize)
        }
    }
}

struct StatRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
            Spacer()
            Text(value)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Export Settings
struct ExportSettingsView: View {
    @AppStorage("defaultExportPath") private var defaultExportPath: String = ""
    @AppStorage("defaultExportFormat") private var defaultExportFormat: String = "zip"
    @AppStorage("includeMetadata") private var includeMetadata: Bool = true
    @AppStorage("compressImages") private var compressImages: Bool = false
    
    var body: some View {
        Form {
            Section("默认导出设置") {
                HStack {
                    Text("默认导出路径:")
                    Spacer()
                    Button(defaultExportPath.isEmpty ? "选择..." : URL(fileURLWithPath: defaultExportPath).lastPathComponent) {
                        selectDefaultExportPath()
                    }
                }
                
                Picker("默认导出格式", selection: $defaultExportFormat) {
                    Text("ZIP 压缩包").tag("zip")
                    Text("Markdown 文档").tag("markdown")
                    Text("单独文件").tag("individual")
                }
                .pickerStyle(.segmented)
            }
            
            Section("导出选项") {
                Toggle("包含元数据", isOn: $includeMetadata)
                Toggle("压缩图片", isOn: $compressImages)
            }
            
            Section("说明") {
                Text("ZIP 格式会将所有内容打包到一个文件中，Markdown 格式会生成可读的文档，单独文件会为每个项目创建独立的文件。")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .formStyle(.grouped)
        .navigationTitle("导出设置")
    }
    
    private func selectDefaultExportPath() {
        let openPanel = NSOpenPanel()
        openPanel.canChooseFiles = false
        openPanel.canChooseDirectories = true
        openPanel.allowsMultipleSelection = false
        openPanel.title = "选择默认导出路径"
        
        if openPanel.runModal() == .OK {
            if let url = openPanel.url {
                defaultExportPath = url.path
            }
        }
    }
}

// MARK: - Preview Settings View
struct PreviewSettingsView: View {
    @AppStorage("enablePreview") private var enablePreview = true
    @AppStorage("previewMaxFileSize") private var previewMaxFileSize = 10.0 // MB
    @AppStorage("generateThumbnails") private var generateThumbnails = true
    @AppStorage("thumbnailSize") private var thumbnailSize = 128.0
    
    var body: some View {
        Form {
            Section("预览功能") {
                Toggle("启用文件预览", isOn: $enablePreview)
                    .help("为支持的文件类型生成预览")
                
                Toggle("生成缩略图", isOn: $generateThumbnails)
                    .help("为图片和PDF文件生成缩略图")
                    .disabled(!enablePreview)
            }
            
            Section("预览限制") {
                VStack(alignment: .leading, spacing: 8) {
                    Text("最大预览文件大小: \(String(format: "%.1f", previewMaxFileSize)) MB")
                    
                    Slider(value: $previewMaxFileSize, in: 1...50, step: 0.5) {
                        Text("文件大小限制")
                    }
                    .disabled(!enablePreview)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("缩略图大小: \(Int(thumbnailSize)) × \(Int(thumbnailSize))")
                    
                    Slider(value: $thumbnailSize, in: 64...512, step: 64) {
                        Text("缩略图尺寸")
                    }
                    .disabled(!enablePreview || !generateThumbnails)
                }
            }
            
            Section("支持的格式") {
                VStack(alignment: .leading, spacing: 12) {
                    Text("当前支持预览的文件格式:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        FormatCategoryView(title: "文本格式", formats: ["TXT", "MD", "JSON", "CSV"])
                        FormatCategoryView(title: "代码格式", formats: ["Swift", "JS", "TS", "Python", "HTML", "CSS"])
                        FormatCategoryView(title: "图片格式", formats: ["JPEG", "PNG", "GIF", "TIFF", "WebP", "SVG"])
                        FormatCategoryView(title: "文档格式", formats: ["PDF", "Word", "Excel", "PowerPoint"])
                    }
                }
            }
        }
        .formStyle(.grouped)
        .navigationTitle("预览设置")
    }
}

struct FormatCategoryView: View {
    let title: String
    let formats: [String]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text(title)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 6) {
                ForEach(formats, id: \.self) { format in
                    Text(format)
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 3)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .cornerRadius(4)
                }
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Advanced Settings
struct AdvancedSettingsView: View {
    @AppStorage("enableDebugLogging") private var enableDebugLogging: Bool = false
    @AppStorage("maxFileSize") private var maxFileSize: Double = 100.0 // MB
    @AppStorage("maxTextLength") private var maxTextLength: Int = 1000000
    @State private var showingResetConfirmation = false
    
    var body: some View {
        Form {
            Section("调试") {
                Toggle("启用调试日志", isOn: $enableDebugLogging)
                
                Button("查看日志") {
                    openConsoleApp()
                }
            }
            
            Section("限制") {
                HStack {
                    Text("最大文件大小:")
                    Spacer()
                    TextField("", value: $maxFileSize, format: .number)
                        .frame(width: 80)
                    Text("MB")
                }
                
                HStack {
                    Text("最大文字长度:")
                    Spacer()
                    TextField("", value: $maxTextLength, format: .number)
                        .frame(width: 100)
                    Text("字符")
                }
            }
            
            Section("重置") {
                Button("重置所有设置") {
                    showingResetConfirmation = true
                }
                .foregroundColor(.red)
            }
        }
        .formStyle(.grouped)
        .navigationTitle("高级设置")
        .alert("确认重置", isPresented: $showingResetConfirmation) {
            Button("重置", role: .destructive) {
                resetAllSettings()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("这将重置所有设置到默认值，此操作无法撤销。")
        }
    }
    
    private func openConsoleApp() {
        if #available(macOS 11.0, *) {
            let url = URL(fileURLWithPath: "/System/Applications/Utilities/Console.app")
            NSWorkspace.shared.openApplication(at: url, configuration: NSWorkspace.OpenConfiguration()) { _, _ in }
        } else {
            NSWorkspace.shared.launchApplication("Console")
        }
    }
    
    private func resetAllSettings() {
        let domain = Bundle.main.bundleIdentifier!
        UserDefaults.standard.removePersistentDomain(forName: domain)
        UserDefaults.standard.synchronize()
    }
}

// MARK: - Settings Window Controller
class SettingsWindowController: NSWindowController {
    
    convenience init() {
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 600),
            styleMask: [.titled, .closable, .resizable],
            backing: .buffered,
            defer: false
        )
        
        self.init(window: window)
        
        window.title = "设置"
        window.center()
        window.setFrameAutosaveName("SettingsWindow")
        
        let settingsView = SettingsView()
        let hostingView = NSHostingView(rootView: settingsView)
        window.contentView = hostingView
    }
    
    func showWindow() {
        window?.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }
}

#Preview {
    SettingsView()
        .environmentObject(HotkeyService())
        .environmentObject(PasteboardMonitor())
        .environmentObject(ContentService())
        .environmentObject(BatchService())
}