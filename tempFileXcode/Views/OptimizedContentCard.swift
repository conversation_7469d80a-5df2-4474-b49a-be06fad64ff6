import SwiftUI
import CoreData
import UniformTypeIdentifiers
import QuickLook
import PDFKit

// MARK: - 优化的内容卡片
struct OptimizedContentCard: View {
    let item: ContentItem
    let onQuickLook: () -> Void
    let isSelected: Bool
    let onSelectionChange: (Bool) -> Void
    
    @EnvironmentObject private var contentService: ContentService
    @State private var showingDetailView = false
    @State private var isHovered = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 顶部：类型图标和标题 
            headerView
            
            // 中间：内容预览
            contentPreview
            
            // 底部：元数据信息
            metadataView
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isSelected ? Color.accentColor.opacity(0.15) : Color(NSColor.controlBackgroundColor))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .strokeBorder(
                            isSelected ? Color.accentColor : (isHovered ? Color.accentColor.opacity(0.5) : Color.gray.opacity(0.2)), 
                            lineWidth: isSelected ? 3 : (isHovered ? 2 : 1)
                        )
                )
                .shadow(
                    color: isSelected ? Color.accentColor.opacity(0.3) : (isHovered ? .black.opacity(0.1) : .black.opacity(0.05)),
                    radius: isSelected ? 10 : (isHovered ? 8 : 4),
                    x: 0,
                    y: isSelected ? 6 : (isHovered ? 4 : 2)
                )
        )
        .scaleEffect(isHovered ? 1.02 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isHovered)
        .onHover { hovering in
            isHovered = hovering
        }
        .sheet(isPresented: $showingDetailView) {
            ContentDetailView(item: item)
        }
        .contextMenu {
            contextMenuItems
        }
        .contentShape(Rectangle()) // 确保整个区域都可以右键
        .simultaneousGesture(
            TapGesture()
                .onEnded { _ in
                    handleSingleClick()
                }
        )
        .onTapGesture(count: 2) {
            // 双击直接打开文件
            openFileWithPreferredApp()
        }
        .focusable()
        .onKeyPress(.space) {
            // 空格键快速预览
            onQuickLook()
            return .handled
        }
    }
    
    // MARK: - 头部视图
    private var headerView: some View {
        HStack(spacing: 12) {
            // 增强的类型图标和标识
            VStack(spacing: 4) {
                // 主图标
                Image(systemName: getEnhancedFileIcon())
                    .foregroundColor(getFileTypeColor())
                    .font(.system(size: 20, weight: .medium))
                    .frame(width: 24, height: 24)
                
                // 文件类型标签
                if let fileExtension = getFileExtension() {
                    Text(fileExtension.uppercased())
                        .font(.system(size: 8, weight: .bold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 4)
                        .padding(.vertical, 1)
                        .background(
                            RoundedRectangle(cornerRadius: 3)
                                .fill(getFileTypeColor())
                        )
                }
            }
            
            // 标题和详细类型信息
            VStack(alignment: .leading, spacing: 2) {
                Text(item.displayTitle)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                HStack(spacing: 8) {
                    Text(getDetailedFileType())
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if let fileSize = getFormattedFileSize(), !fileSize.isEmpty {
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(fileSize)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // 快速查看按钮
            Button(action: onQuickLook) {
                Image(systemName: "eye.fill")
                    .foregroundColor(.accentColor)
                    .font(.system(size: 16))
                    .frame(width: 28, height: 28)
                    .background(
                        Circle()
                            .fill(Color.accentColor.opacity(0.1))
                    )
            }
            .buttonStyle(.plain)
            .help("快速查看")
        }
    }
    
    // MARK: - 文件类型增强方法
    private func getEnhancedFileIcon() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.systemImage
        }
        
        let url = URL(fileURLWithPath: filePath)
        let fileType = FileTypeManager.shared.identifyFileType(for: url)
        let ext = url.pathExtension.lowercased()
        
        // 根据具体文件扩展名返回更精确的图标
        switch fileType {
        case .code:
            switch ext {
            case "swift": return "swift"
            case "py": return "doc.text.fill"
            case "js", "ts": return "doc.text"
            case "html": return "globe"
            case "css", "scss": return "paintbrush"
            case "json": return "curlybraces"
            case "xml": return "chevron.left.forwardslash.chevron.right"
            default: return "chevron.left.forwardslash.chevron.right"
            }
        case .document:
            switch ext {
            case "md", "markdown": return "doc.richtext"
            case "pdf": return "doc.fill"
            case "doc", "docx": return "doc.text"
            default: return "doc.richtext"
            }
        case .image:
            switch ext {
            case "svg": return "photo.artframe"
            case "gif": return "photo.stack"
            default: return "photo"
            }
        case .video:
            return "video.fill"
        case .audio:
            return "music.note"
        case .archive:
            return "archivebox.fill"
        case .data:
            switch ext {
            case "csv": return "tablecells"
            case "xlsx", "xls": return "tablecells.fill"
            case "db", "sqlite": return "cylinder.fill"
            default: return "tablecells"
            }
        default:
            return item.contentTypeEnum.systemImage
        }
    }
    
    private func getFileTypeColor() -> Color {
        guard let filePath = item.filePath else {
            return Color(tagColor: item.contentTypeEnum.color)
        }
        
        let url = URL(fileURLWithPath: filePath)
        let fileType = FileTypeManager.shared.identifyFileType(for: url)
        let ext = url.pathExtension.lowercased()
        
        // 根据文件类型返回更精确的颜色
        switch fileType {
        case .code:
            switch ext {
            case "swift": return .orange
            case "py": return .blue
            case "js", "ts": return .yellow
            case "html": return .red
            case "css", "scss": return .purple
            case "json": return .green
            default: return .green
            }
        case .document:
            switch ext {
            case "md", "markdown": return .blue
            case "pdf": return .red
            default: return .orange
            }
        case .image:
            return .purple
        case .video:
            return .red
        case .audio:
            return .pink
        case .archive:
            return .brown
        case .data:
            return .gray
        default:
            return Color(tagColor: item.contentTypeEnum.color)
        }
    }
    
    private func getFileExtension() -> String? {
        guard let filePath = item.filePath else { return nil }
        let ext = URL(fileURLWithPath: filePath).pathExtension
        return ext.isEmpty ? nil : ext
    }
    
    private func getDetailedFileType() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.displayName
        }
        
        let url = URL(fileURLWithPath: filePath)
        let fileType = FileTypeManager.shared.identifyFileType(for: url)
        let ext = url.pathExtension.lowercased()
        
        // 返回更详细的文件类型描述
        switch fileType {
        case .code:
            switch ext {
            case "swift": return "Swift 源代码"
            case "py": return "Python 脚本"
            case "js": return "JavaScript 文件"
            case "ts": return "TypeScript 文件"
            case "html": return "HTML 文档"
            case "css": return "样式表"
            case "json": return "JSON 数据"
            default: return "代码文件"
            }
        case .document:
            switch ext {
            case "md", "markdown": return "Markdown 文档"
            case "pdf": return "PDF 文档"
            case "doc", "docx": return "Word 文档"
            default: return "文档文件"
            }
        case .image:
            switch ext {
            case "jpg", "jpeg": return "JPEG 图片"
            case "png": return "PNG 图片"
            case "gif": return "GIF 动图"
            case "svg": return "矢量图形"
            default: return "图片文件"
            }
        case .video:
            switch ext {
            case "mp4": return "MP4 视频"
            case "mov": return "QuickTime 视频"
            default: return "视频文件"
            }
        case .audio:
            switch ext {
            case "mp3": return "MP3 音频"
            case "wav": return "WAV 音频"
            default: return "音频文件"
            }
        case .archive:
            switch ext {
            case "zip": return "ZIP 压缩包"
            case "rar": return "RAR 压缩包"
            default: return "压缩文件"
            }
        case .data:
            switch ext {
            case "csv": return "CSV 数据表"
            case "xlsx": return "Excel 表格"
            default: return "数据文件"
            }
        default:
            return item.contentTypeEnum.displayName
        }
    }
    
    private func getFormattedFileSize() -> String? {
        if item.contentTypeEnum == .text {
            return nil // 文本类型不显示文件大小
        }
        return item.formattedFileSize
    }
    
    // MARK: - 内容预览
    @ViewBuilder
    private var contentPreview: some View {
        switch item.contentTypeEnum {
        case .text:
            textPreview
        case .image:
            imagePreview
        case .file:
            filePreview
        }
    }
    
    private var textPreview: some View {
        Group {
            if let content = item.content {
                VStack(alignment: .leading, spacing: 8) {
                    ScrollView {
                        Text(content)
                            .font(.body)
                            .lineLimit(nil) // 显示全部内容，不截断
                            .textSelection(.enabled) // 允许文本选择
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(12)
                    }
                    .frame(minHeight: 200, maxHeight: 600) // 大幅增加最大高度，确保显示更多内容
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(NSColor.textBackgroundColor))
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .strokeBorder(Color.gray.opacity(0.2), lineWidth: 1)
                            )
                    )
                    
                    HStack {
                        // 字符数统计
                        Text("\(content.count) 字符")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        // 复制按钮
                        Button(action: copyText) {
                            HStack(spacing: 6) {
                                Image(systemName: "doc.on.doc")
                                    .font(.system(size: 12))
                                Text("复制文本")
                                    .font(.caption)
                            }
                            .foregroundColor(.accentColor)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(Color.accentColor.opacity(0.1))
                            )
                        }
                        .buttonStyle(.plain)
                    }
                }
            }
        }
    }
    
    private var imagePreview: some View {
        Group {
            if let filePath = item.filePath,
               let image = NSImage(contentsOfFile: filePath) {
                VStack(spacing: 8) {
                    Image(nsImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 120)
                        .cornerRadius(8)
                        .clipped()
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .strokeBorder(Color.gray.opacity(0.2), lineWidth: 1)
                        )
                    
                    if let fileName = item.fileName {
                        Text(fileName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                            .truncationMode(.middle)
                    }
                }
            } else {
                VStack(spacing: 12) {
                    Image(systemName: "photo.badge.exclamationmark")
                        .font(.system(size: 40))
                        .foregroundColor(.orange)
                    
                    Text("图片无法加载")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(height: 120)
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.1))
                )
            }
        }
    }
    
    private var filePreview: some View {
        VStack(spacing: 12) {
            // 尝试内置预览，如果不支持则显示文件图标
            if canShowInlinePreview() {
                inlineFilePreview
            } else {
                // 传统的文件图标显示
                Image(systemName: getFileIcon())
                    .font(.system(size: 48))
                    .foregroundColor(getFileIconColor())
            }
            
            // 文件名
            if let fileName = item.fileName {
                Text(fileName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 8)
            }
            
            // 文件类型和大小
            HStack(spacing: 8) {
                Text(getFileTypeDescription())
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 3)
                            .fill(Color.accentColor.opacity(0.1))
                    )
                
                Text(item.formattedFileSize)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 3)
                            .fill(Color.gray.opacity(0.1))
                    )
            }
            
            // 预览按钮
            HStack(spacing: 8) {
                if canShowInlinePreview() {
                    Button(action: onQuickLook) {
                        HStack(spacing: 4) {
                            Image(systemName: "eye.fill")
                                .font(.system(size: 12))
                            Text("详细预览")
                                .font(.caption)
                        }
                        .foregroundColor(.accentColor)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color.accentColor.opacity(0.1))
                        )
                    }
                    .buttonStyle(.plain)
                } else {
                    Button(action: onQuickLook) {
                        HStack(spacing: 4) {
                            Image(systemName: "eye")
                                .font(.system(size: 12))
                            Text("预览")
                                .font(.caption)
                        }
                        .foregroundColor(.accentColor)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color.accentColor.opacity(0.1))
                        )
                    }
                    .buttonStyle(.plain)
                }
            }
        }
        .frame(minHeight: 140)
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - 内置文件预览
    @ViewBuilder
    private var inlineFilePreview: some View {
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            let ext = url.pathExtension.lowercased()
            
            switch ext {
            case "pdf":
                PDFInlinePreview(url: url)
                    .frame(height: 120)
                    .cornerRadius(8)
            case "txt", "md", "json", "xml", "csv":
                TextFileInlinePreview(url: url)
                    .frame(height: 120)
                    .cornerRadius(8)
            default:
                // 不支持内置预览的文件类型，显示图标
                Image(systemName: getFileIcon())
                    .font(.system(size: 48))
                    .foregroundColor(getFileIconColor())
            }
        } else {
            Image(systemName: getFileIcon())
                .font(.system(size: 48))
                .foregroundColor(getFileIconColor())
        }
    }
    
    private func canShowInlinePreview() -> Bool {
        guard let filePath = item.filePath else { return false }
        let ext = URL(fileURLWithPath: filePath).pathExtension.lowercased()
        return ["pdf", "txt", "md", "json", "xml", "csv"].contains(ext)
    }
    
    // MARK: - 文件类型辅助方法
    private func getFileIcon() -> String {
        guard let fileName = item.fileName else { return "doc.fill" }
        let fileExtension = (fileName as NSString).pathExtension.lowercased()
        
        switch fileExtension {
        case "xlsx", "xls":
            return "tablecells.fill"
        case "docx", "doc":
            return "doc.text.fill"
        case "pptx", "ppt":
            return "play.rectangle.fill"
        case "pdf":
            return "doc.richtext.fill"
        case "zip", "rar", "7z":
            return "archivebox.fill"
        case "mp4", "mov", "avi":
            return "play.rectangle.fill"
        case "mp3", "wav", "m4a":
            return "music.note"
        case "jpg", "jpeg", "png", "gif", "bmp":
            return "photo.fill"
        case "txt", "rtf":
            return "doc.text"
        case "json", "xml":
            return "doc.badge.gearshape"
        case "html", "htm":
            return "globe"
        default:
            return "doc.fill"
        }
    }
    
    private func getFileIconColor() -> Color {
        guard let fileName = item.fileName else { return .accentColor }
        let fileExtension = (fileName as NSString).pathExtension.lowercased()
        
        switch fileExtension {
        case "xlsx", "xls":
            return .green
        case "docx", "doc":
            return .blue
        case "pptx", "ppt":
            return .orange
        case "pdf":
            return .red
        case "zip", "rar", "7z":
            return .purple
        case "mp4", "mov", "avi":
            return .pink
        case "mp3", "wav", "m4a":
            return .cyan
        case "jpg", "jpeg", "png", "gif", "bmp":
            return .mint
        default:
            return .accentColor
        }
    }
    
    private func getFileTypeDescription() -> String {
        guard let fileName = item.fileName else { return "文件" }
        let fileExtension = (fileName as NSString).pathExtension.lowercased()
        
        switch fileExtension {
        case "xlsx", "xls":
            return "Excel表格"
        case "docx", "doc":
            return "Word文档"
        case "pptx", "ppt":
            return "PowerPoint"
        case "pdf":
            return "PDF文档"
        case "zip", "rar", "7z":
            return "压缩文件"
        case "mp4", "mov", "avi":
            return "视频文件"
        case "mp3", "wav", "m4a":
            return "音频文件"
        case "jpg", "jpeg", "png", "gif", "bmp":
            return "图片文件"
        case "txt", "rtf":
            return "文本文件"
        case "json", "xml":
            return "数据文件"
        case "html", "htm":
            return "网页文件"
        default:
            return fileExtension.uppercased() + "文件"
        }
    }
    
    // MARK: - 元数据视图
    private var metadataView: some View {
        VStack(spacing: 8) {
            // 文件路径（如果存在）
            if let filePath = item.filePath {
                HStack {
                    Image(systemName: "folder")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                    
                    Text(filePath)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                        .truncationMode(.middle)
                    
                    Spacer()
                }
            }
            
            // 时间和大小信息
            HStack {
                Text(item.formattedCreatedDate)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if item.contentTypeEnum != .text {
                    Text(item.formattedFileSize)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    // MARK: - 右键菜单
    @ViewBuilder
    private var contextMenuItems: some View {
        Button("查看完整内容") {
            showingDetailView = true
        }
        
        if item.contentTypeEnum == .text {
            Button("复制文本") {
                copyText()
            }
        }
        
        if item.contentTypeEnum == .file || item.contentTypeEnum == .image {
            Button("在Finder中显示") {
                showInFinder()
            }
            
            Button("复制文件") {
                copyFile()
            }
        }
        
        Button("复制路径") {
            copyPath()
        }
        
        Divider()
        
        Button("分享") {
            shareItem()
        }
        
        Divider()
        
        Button("删除", role: .destructive) {
            deleteItem()
        }
    }
    
    // MARK: - 操作方法
    private func copyText() {
        if let content = item.content {
            NSPasteboard.general.clearContents()
            NSPasteboard.general.setString(content, forType: .string)
        }
    }
    
    private func showInFinder() {
        if let filePath = item.filePath {
            NSWorkspace.shared.selectFile(filePath, inFileViewerRootedAtPath: "")
        }
    }
    
    private func copyFile() {
        if let filePath = item.filePath {
            let pasteboard = NSPasteboard.general
            pasteboard.clearContents()
            pasteboard.setString(URL(fileURLWithPath: filePath).absoluteString, forType: .fileURL)
        }
    }
    
    private func copyPath() {
        if let filePath = item.filePath {
            NSPasteboard.general.clearContents()
            NSPasteboard.general.setString(filePath, forType: .string)
        }
    }
    
    private func shareItem() {
        if let filePath = item.filePath {
            let sharingPicker = NSSharingServicePicker(items: [URL(fileURLWithPath: filePath)])
            if let view = NSApp.keyWindow?.contentView {
                sharingPicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
            }
        } else if let content = item.content {
            let sharingPicker = NSSharingServicePicker(items: [content])
            if let view = NSApp.keyWindow?.contentView {
                sharingPicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
            }
        }
    }
    
    private func deleteItem() {
        Task {
            do {
                try await contentService.deleteContent(item)
            } catch {
                NSLog("删除失败: \(error)")
            }
        }
    }
    

    
    private func openFile() {
        if let filePath = item.filePath {
            NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
        } else {
            // 如果没有文件路径，显示详情视图
            showingDetailView = true
        }
    }
    
    private func openFileWithPreferredApp() {
        guard let filePath = item.filePath else {
            showingDetailView = true
            return
        }
        
        let url = URL(fileURLWithPath: filePath)
        let fileType = FileTypeManager.shared.identifyFileType(for: url)
        
        // 根据文件类型选择合适的打开方式
        switch fileType {
        case .document:
            if url.pathExtension.lowercased() == "md" {
                // Markdown文件优先使用专业编辑器
                let markdownApps = FileTypeManager.shared.getRecommendedApps(for: url)
                if let preferredApp = markdownApps.first(where: { $0.bundleIdentifier.contains("typora") || $0.bundleIdentifier.contains("macdown") }) {
                    _ = FileTypeManager.shared.openFile(url, with: preferredApp)
                    return
                }
            }
            fallthrough
        default:
            // 使用系统默认应用
            NSWorkspace.shared.open(url)
        }
    }
    
    // MARK: - 点击处理
    private func handleSingleClick() {
        // 检查修饰键
        let modifierFlags = NSEvent.modifierFlags
        if modifierFlags.contains(.command) {
            // Cmd+点击：切换选择状态
            onSelectionChange(!isSelected)
        } else if modifierFlags.contains(.shift) {
            // Shift+点击：范围选择（需要在上层处理）
            onSelectionChange(true)
        } else {
            // 普通点击：单选
            onSelectionChange(true)
        }
    }
}

// MARK: - 列表行视图
struct OptimizedContentRow: View {
    let item: ContentItem
    let onQuickLook: () -> Void
    let isSelected: Bool
    let onSelectionChange: (Bool) -> Void
    
    @EnvironmentObject private var contentService: ContentService
    @State private var showingDetailView = false
    @State private var isHovered = false
    
    var body: some View {
        HStack(spacing: 16) {
            // 类型图标
            Image(systemName: item.contentTypeEnum.systemImage)
                .foregroundColor(Color(tagColor: item.contentTypeEnum.color))
                .font(.system(size: 18))
                .frame(width: 24, height: 24)
            
            // 主要信息
            VStack(alignment: .leading, spacing: 4) {
                Text(item.displayTitle)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                HStack(spacing: 12) {
                    Text(item.contentTypeEnum.displayName)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text(item.formattedCreatedDate)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    if item.contentTypeEnum != .text {
                        Text(item.formattedFileSize)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // 快速查看按钮
            if isHovered {
                Button(action: onQuickLook) {
                    Image(systemName: "eye.fill")
                        .foregroundColor(.accentColor)
                        .font(.system(size: 14))
                }
                .buttonStyle(.plain)
                .help("快速查看")
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? Color.accentColor.opacity(0.15) : (isHovered ? Color.gray.opacity(0.1) : Color.clear))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(
                            isSelected ? Color.accentColor : Color.clear,
                            lineWidth: isSelected ? 2 : 0
                        )
                )
        )
        .contentShape(Rectangle()) // 确保整个区域都可以右键
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .sheet(isPresented: $showingDetailView) {
            ContentDetailView(item: item)
        }
        .contextMenu {
            contextMenuItems
        }
        .simultaneousGesture(
            TapGesture()
                .onEnded { _ in
                    handleSingleClick()
                }
        )
        .onTapGesture(count: 2) {
            // 双击直接打开文件
            openFile()
        }
        .focusable()
        .onKeyPress(.space) {
            // 空格键快速预览
            onQuickLook()
            return .handled
        }
    }
    
    // MARK: - 右键菜单
    @ViewBuilder
    private var contextMenuItems: some View {
        Button("查看完整内容") {
            showingDetailView = true
        }
        
        if item.contentTypeEnum == .text {
            Button("复制文本") {
                if let content = item.content {
                    NSPasteboard.general.clearContents()
                    NSPasteboard.general.setString(content, forType: .string)
                }
            }
        }
        
        if item.contentTypeEnum == .file || item.contentTypeEnum == .image {
            Button("在Finder中显示") {
                if let filePath = item.filePath {
                    NSWorkspace.shared.selectFile(filePath, inFileViewerRootedAtPath: "")
                }
            }
            
            Button("复制文件") {
                if let filePath = item.filePath {
                    let pasteboard = NSPasteboard.general
                    pasteboard.clearContents()
                    pasteboard.setString(URL(fileURLWithPath: filePath).absoluteString, forType: .fileURL)
                }
            }
        }
        
        Button("复制路径") {
            if let filePath = item.filePath {
                NSPasteboard.general.clearContents()
                NSPasteboard.general.setString(filePath, forType: .string)
            }
        }
        
        Divider()
        
        Button("分享") {
            if let filePath = item.filePath {
                let sharingPicker = NSSharingServicePicker(items: [URL(fileURLWithPath: filePath)])
                if let view = NSApp.keyWindow?.contentView {
                    sharingPicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
                }
            } else if let content = item.content {
                let sharingPicker = NSSharingServicePicker(items: [content])
                if let view = NSApp.keyWindow?.contentView {
                    sharingPicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
                }
            }
        }
        
        Divider()
        
        Button("删除", role: .destructive) {
            Task {
                do {
                    try await contentService.deleteContent(item)
                } catch {
                    NSLog("删除失败: \(error)")
                }
            }
        }
    }
    
    private func openFile() {
        if let filePath = item.filePath {
            NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
        } else {
            // 如果没有文件路径，显示详情视图
            showingDetailView = true
        }
    }
    
    // MARK: - 点击处理
    private func handleSingleClick() {
        // 检查修饰键
        let modifierFlags = NSEvent.modifierFlags
        if modifierFlags.contains(.command) {
            // Cmd+点击：切换选择状态
            onSelectionChange(!isSelected)
        } else if modifierFlags.contains(.shift) {
            // Shift+点击：范围选择（需要在上层处理）
            onSelectionChange(true)
        } else {
            // 普通点击：单选
            onSelectionChange(true)
        }
    }
}

// MARK: - 分栏视图内容卡片
struct CompactContentCard: View {
    let item: ContentItem
    let onQuickLook: () -> Void
    let isSelected: Bool
    let onSelectionChange: (Bool) -> Void
    
    @EnvironmentObject private var contentService: ContentService
    @State private var isHovered = false
    
    var body: some View {
        HStack(spacing: 12) {
            // 左侧：文件信息
            VStack(alignment: .leading, spacing: 8) {
                // 文件图标和名称
                HStack(spacing: 8) {
                    Image(systemName: getFileIcon())
                        .foregroundColor(getFileColor())
                        .font(.system(size: 20))
                        .frame(width: 24, height: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text(item.displayTitle)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .lineLimit(1)
                        
                        Text(getFileTypeDescription())
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                
                // 文件详细信息
                VStack(alignment: .leading, spacing: 2) {
                    Text(item.formattedCreatedDate)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    if item.contentTypeEnum != .text {
                        Text(item.formattedFileSize)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            Divider()
                .frame(height: 60)
            
            // 右侧：内容预览
            contentPreview
                .frame(maxWidth: .infinity)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? Color.accentColor.opacity(0.15) : Color(NSColor.controlBackgroundColor))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(
                            isSelected ? Color.accentColor : (isHovered ? Color.accentColor.opacity(0.5) : Color.gray.opacity(0.2)),
                            lineWidth: isSelected ? 2 : (isHovered ? 1 : 0.5)
                        )
                )
        )
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .simultaneousGesture(
            TapGesture()
                .onEnded { _ in
                    handleSingleClick()
                }
        )
        .onTapGesture(count: 2) {
            if let filePath = item.filePath {
                NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
            }
        }
        .contextMenu {
            contextMenuItems
        }
    }
    
    @ViewBuilder
    private var contentPreview: some View {
        switch item.contentTypeEnum {
        case .text:
            if let content = item.content {
                Text(content)
                    .font(.caption)
                    .lineLimit(3)
                    .padding(8)
                    .frame(maxWidth: .infinity, alignment: .topLeading)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color(NSColor.textBackgroundColor))
                    )
            } else {
                Text("无内容")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
            }
            
        case .image:
            if let filePath = item.filePath,
               let image = NSImage(contentsOfFile: filePath) {
                Image(nsImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(height: 60)
                    .cornerRadius(6)
            } else {
                Image(systemName: "photo")
                    .font(.system(size: 24))
                    .foregroundColor(.secondary)
                    .frame(height: 60)
            }
            
        case .file:
            VStack(spacing: 4) {
                Image(systemName: getFileIcon())
                    .font(.system(size: 24))
                    .foregroundColor(getFileColor())
                
                if let fileName = item.fileName {
                    Text(fileName)
                        .font(.caption2)
                        .lineLimit(2)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(height: 60)
        }
    }
    
    private func getFileIcon() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.systemImage
        }
        
        let ext = URL(fileURLWithPath: filePath).pathExtension.lowercased()
        switch ext {
        case "pdf": return "doc.fill"
        case "doc", "docx": return "doc.text"
        case "xls", "xlsx": return "tablecells"
        case "ppt", "pptx": return "rectangle.3.group.bubble.left"
        case "zip", "rar": return "archivebox"
        case "mp3", "wav": return "music.note"
        case "mp4", "mov": return "video"
        case "jpg", "jpeg", "png", "gif": return "photo"
        default: return item.contentTypeEnum.systemImage
        }
    }
    
    private func getFileColor() -> Color {
        guard let filePath = item.filePath else {
            return Color(tagColor: item.contentTypeEnum.color)
        }
        
        let fileType = FileTypeManager.shared.identifyFileType(for: URL(fileURLWithPath: filePath))
        return fileType.color
    }
    
    private func getFileTypeDescription() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.displayName
        }
        
        let url = URL(fileURLWithPath: filePath)
        let ext = url.pathExtension.uppercased()
        return ext.isEmpty ? item.contentTypeEnum.displayName : "\(ext) 文件"
    }
    
    @ViewBuilder
    private var contextMenuItems: some View {
        Button("打开") {
            if let filePath = item.filePath {
                NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
            }
        }
        
        Button("快速预览") {
            onQuickLook()
        }
        
        Divider()
        
        Button("在Finder中显示") {
            if let filePath = item.filePath {
                NSWorkspace.shared.selectFile(filePath, inFileViewerRootedAtPath: "")
            }
        }
        
        Button("删除", role: .destructive) {
            Task {
                do {
                    try await contentService.deleteContent(item)
                } catch {
                    NSLog("删除失败: \(error)")
                }
            }
        }
    }
    
    // MARK: - 点击处理
    private func handleSingleClick() {
        // 检查修饰键
        let modifierFlags = NSEvent.modifierFlags
        if modifierFlags.contains(.command) {
            // Cmd+点击：切换选择状态
            onSelectionChange(!isSelected)
        } else if modifierFlags.contains(.shift) {
            // Shift+点击：范围选择（需要在上层处理）
            onSelectionChange(true)
        } else {
            // 普通点击：单选
            onSelectionChange(true)
        }
    }
}

// MARK: - 画廊内容卡片（画廊视图用）
struct GalleryContentCard: View {
    let item: ContentItem
    let onQuickLook: () -> Void
    let isSelected: Bool
    let onSelectionChange: (Bool) -> Void
    
    @EnvironmentObject private var contentService: ContentService
    @State private var isHovered = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 预览区域
            ZStack {
                previewContent
                    .frame(height: 150)
                    .frame(maxWidth: .infinity)
                    .background(Color.gray.opacity(0.1))
                    .clipped()
                
                // 悬停时的操作按钮
                if isHovered {
                    VStack {
                        HStack {
                            Spacer()
                            Button(action: onQuickLook) {
                                Image(systemName: "eye.fill")
                                    .foregroundColor(.white)
                                    .font(.system(size: 16))
                                    .frame(width: 32, height: 32)
                                    .background(
                                        Circle()
                                            .fill(Color.black.opacity(0.6))
                                    )
                            }
                            .buttonStyle(.plain)
                        }
                        Spacer()
                    }
                    .padding(8)
                }
            }
            
            // 信息区域
            VStack(alignment: .leading, spacing: 4) {
                Text(item.displayTitle)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                HStack {
                    Text(getFileTypeDescription())
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    if item.contentTypeEnum != .text {
                        Text(item.formattedFileSize)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(12)
            .background(Color(NSColor.controlBackgroundColor))
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(NSColor.controlBackgroundColor))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .strokeBorder(
                            isSelected ? Color.accentColor : (isHovered ? Color.accentColor.opacity(0.5) : Color.gray.opacity(0.2)),
                            lineWidth: isSelected ? 3 : (isHovered ? 2 : 1)
                        )
                )
                .shadow(
                    color: isSelected ? Color.accentColor.opacity(0.3) : (isHovered ? .black.opacity(0.1) : .black.opacity(0.05)),
                    radius: isSelected ? 8 : (isHovered ? 6 : 3),
                    x: 0,
                    y: isSelected ? 4 : (isHovered ? 3 : 1)
                )
        )
        .scaleEffect(isHovered ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isHovered)
        .onHover { hovering in
            isHovered = hovering
        }
        .simultaneousGesture(
            TapGesture()
                .onEnded { _ in
                    handleSingleClick()
                }
        )
        .onTapGesture(count: 2) {
            if let filePath = item.filePath {
                NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
            }
        }
        .contextMenu {
            contextMenuItems
        }
    }
    
    @ViewBuilder
    private var previewContent: some View {
        switch item.contentTypeEnum {
        case .image:
            if let filePath = item.filePath,
               let image = NSImage(contentsOfFile: filePath) {
                Image(nsImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 150)
                    .clipped()
            } else {
                Image(systemName: "photo")
                    .font(.system(size: 48))
                    .foregroundColor(.secondary)
            }
            
        case .text:
            if let content = item.content {
                ScrollView {
                    Text(content)
                        .font(.caption)
                        .padding(8)
                        .frame(maxWidth: .infinity, alignment: .topLeading)
                }
                .background(Color(NSColor.textBackgroundColor))
            } else {
                Image(systemName: "doc.text")
                    .font(.system(size: 48))
                    .foregroundColor(.secondary)
            }
            
        case .file:
            VStack(spacing: 12) {
                Image(systemName: getFileIcon())
                    .font(.system(size: 48))
                    .foregroundColor(getFileColor())
                
                if let fileName = item.fileName {
                    Text(fileName)
                        .font(.caption)
                        .lineLimit(2)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 8)
                }
            }
        }
    }
    
    private func getFileIcon() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.systemImage
        }
        
        let ext = URL(fileURLWithPath: filePath).pathExtension.lowercased()
        switch ext {
        case "pdf": return "doc.fill"
        case "doc", "docx": return "doc.text"
        case "xls", "xlsx": return "tablecells"
        case "ppt", "pptx": return "rectangle.3.group.bubble.left"
        case "zip", "rar": return "archivebox"
        case "mp3", "wav": return "music.note"
        case "mp4", "mov": return "video"
        default: return item.contentTypeEnum.systemImage
        }
    }
    
    private func getFileColor() -> Color {
        guard let filePath = item.filePath else {
            return Color(tagColor: item.contentTypeEnum.color)
        }
        
        let fileType = FileTypeManager.shared.identifyFileType(for: URL(fileURLWithPath: filePath))
        return fileType.color
    }
    
    private func getFileTypeDescription() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.displayName
        }
        
        let url = URL(fileURLWithPath: filePath)
        let ext = url.pathExtension.uppercased()
        return ext.isEmpty ? item.contentTypeEnum.displayName : "\(ext) 文件"
    }
    
    @ViewBuilder
    private var contextMenuItems: some View {
        Button("打开") {
            if let filePath = item.filePath {
                NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
            }
        }
        
        Button("快速预览") {
            onQuickLook()
        }
        
        Divider()
        
        Button("在Finder中显示") {
            if let filePath = item.filePath {
                NSWorkspace.shared.selectFile(filePath, inFileViewerRootedAtPath: "")
            }
        }
        
        Button("删除", role: .destructive) {
            Task {
                do {
                    try await contentService.deleteContent(item)
                } catch {
                    NSLog("删除失败: \(error)")
                }
            }
        }
    }
    
    // MARK: - 点击处理
    private func handleSingleClick() {
        // 检查修饰键
        let modifierFlags = NSEvent.modifierFlags
        if modifierFlags.contains(.command) {
            // Cmd+点击：切换选择状态
            onSelectionChange(!isSelected)
        } else if modifierFlags.contains(.shift) {
            // Shift+点击：范围选择（需要在上层处理）
            onSelectionChange(true)
        } else {
            // 普通点击：单选
            onSelectionChange(true)
        }
    }
}

// MARK: - 分栏视图文件行
struct ColumnFileRow: View {
    let item: ContentItem
    let isSelected: Bool
    let onSelectionChange: (Bool) -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        HStack(spacing: 12) {
            // 勾选框
            Button(action: {
                onSelectionChange(!isSelected)
            }) {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .accentColor : .secondary)
                    .font(.system(size: 16))
            }
            .buttonStyle(.plain)
            .frame(width: 20, height: 20)
            
            // 文件图标
            Image(systemName: getFileIcon())
                .foregroundColor(getFileColor())
                .font(.system(size: 16))
                .frame(width: 20, height: 20)
            
            // 文件名和信息
            VStack(alignment: .leading, spacing: 2) {
                Text(item.displayTitle)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                HStack(spacing: 8) {
                    Text(getFileTypeDescription())
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text("•")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text(item.formattedCreatedDate)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // 文件大小
            if item.contentTypeEnum != .text {
                Text(item.formattedFileSize)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .frame(height: 60) // 固定高度，确保所有列表项高度一致
        .background(
            Rectangle()
                .fill(isSelected ? Color.accentColor.opacity(0.2) : (isHovered ? Color.gray.opacity(0.1) : Color.clear))
        )
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .onTapGesture(count: 2) {
            if let filePath = item.filePath {
                NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
            }
        }
    }
    
    private func getFileIcon() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.systemImage
        }
        
        let ext = URL(fileURLWithPath: filePath).pathExtension.lowercased()
        switch ext {
        case "pdf": return "doc.fill"
        case "doc", "docx": return "doc.text"
        case "xls", "xlsx": return "tablecells"
        case "zip", "rar": return "archivebox"
        case "mp3", "wav": return "music.note"
        case "mp4", "mov": return "video"
        case "jpg", "jpeg", "png", "gif": return "photo"
        default: return item.contentTypeEnum.systemImage
        }
    }
    
    private func getFileColor() -> Color {
        guard let filePath = item.filePath else {
            return Color(tagColor: item.contentTypeEnum.color)
        }
        
        let fileType = FileTypeManager.shared.identifyFileType(for: URL(fileURLWithPath: filePath))
        return fileType.color
    }
    
    private func getFileTypeDescription() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.displayName
        }
        
        let url = URL(fileURLWithPath: filePath)
        let ext = url.pathExtension.uppercased()
        return ext.isEmpty ? item.contentTypeEnum.displayName : "\(ext) 文件"
    }
}

// MARK: - 分栏视图内容预览
struct ColumnContentPreview: View {
    let item: ContentItem
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 文件信息头部
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: getFileIcon())
                            .foregroundColor(getFileColor())
                            .font(.system(size: 32))
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(item.displayTitle)
                                .font(.title2)
                                .fontWeight(.semibold)
                            
                            Text(getDetailedFileType())
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                    
                    // 文件详细信息
                    VStack(alignment: .leading, spacing: 4) {
                        ContentInfoRow(label: "创建时间", value: item.formattedCreatedDate)
                        
                        if item.contentTypeEnum != .text {
                            ContentInfoRow(label: "文件大小", value: item.formattedFileSize)
                        }
                        
                        if let filePath = item.filePath {
                            ContentInfoRow(label: "文件路径", value: filePath)
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(NSColor.controlBackgroundColor))
                )
                
                // 内容预览
                contentPreview
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(NSColor.controlBackgroundColor))
                    )
                
                Spacer()
            }
            .padding()
        }
        .background(Color(NSColor.textBackgroundColor))
    }
    
    @ViewBuilder
    private var contentPreview: some View {
        switch item.contentTypeEnum {
        case .text:
            if let content = item.content {
                VStack(alignment: .leading, spacing: 8) {
                    Text("内容预览")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    ScrollView {
                        Text(content)
                            .font(.body)
                            .frame(maxWidth: .infinity, alignment: .topLeading)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color(NSColor.textBackgroundColor))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .strokeBorder(Color.gray.opacity(0.2), lineWidth: 1)
                                    )
                            )
                    }
                    .frame(minHeight: 200, maxHeight: 400)
                }
            }
            
        case .image:
            if let filePath = item.filePath,
               let image = NSImage(contentsOfFile: filePath) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("图片预览")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Image(nsImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 400)
                        .cornerRadius(8)
                }
            }
            
        case .file:
            if let filePath = item.filePath {
                VStack(alignment: .leading, spacing: 8) {
                    Text("文件预览")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    // 使用QuickLook预览各种文件格式
                    QuickLookPreview(url: URL(fileURLWithPath: filePath))
                        .frame(minHeight: 300, maxHeight: 500)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .strokeBorder(Color.gray.opacity(0.2), lineWidth: 1)
                        )
                }
            } else {
                VStack(alignment: .leading, spacing: 8) {
                    Text("文件信息")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    VStack(spacing: 12) {
                        Image(systemName: getFileIcon())
                            .font(.system(size: 64))
                            .foregroundColor(getFileColor())
                        
                        if let fileName = item.fileName {
                            Text(fileName)
                                .font(.title3)
                                .fontWeight(.medium)
                                .multilineTextAlignment(.center)
                        }
                        
                        Text("文件不可用")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.1))
                    )
                }
            }
        }
    }
    
    private func getFileIcon() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.systemImage
        }
        
        let ext = URL(fileURLWithPath: filePath).pathExtension.lowercased()
        switch ext {
        case "pdf": return "doc.fill"
        case "doc", "docx": return "doc.text"
        case "xls", "xlsx": return "tablecells"
        case "zip", "rar": return "archivebox"
        case "mp3", "wav": return "music.note"
        case "mp4", "mov": return "video"
        case "jpg", "jpeg", "png", "gif": return "photo"
        default: return item.contentTypeEnum.systemImage
        }
    }
    
    private func getFileColor() -> Color {
        guard let filePath = item.filePath else {
            return Color(tagColor: item.contentTypeEnum.color)
        }
        
        let fileType = FileTypeManager.shared.identifyFileType(for: URL(fileURLWithPath: filePath))
        return fileType.color
    }
    
    private func getDetailedFileType() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.displayName
        }
        
        let url = URL(fileURLWithPath: filePath)
        let fileType = FileTypeManager.shared.identifyFileType(for: url)
        let ext = url.pathExtension.lowercased()
        
        switch fileType {
        case .code:
            switch ext {
            case "swift": return "Swift 源代码文件"
            case "py": return "Python 脚本文件"
            case "js": return "JavaScript 文件"
            case "ts": return "TypeScript 文件"
            case "html": return "HTML 网页文件"
            case "css": return "CSS 样式表文件"
            case "json": return "JSON 数据文件"
            default: return "代码文件"
            }
        case .document:
            switch ext {
            case "md", "markdown": return "Markdown 文档文件"
            case "pdf": return "PDF 文档文件"
            case "doc", "docx": return "Microsoft Word 文档"
            default: return "文档文件"
            }
        case .image:
            switch ext {
            case "jpg", "jpeg": return "JPEG 图片文件"
            case "png": return "PNG 图片文件"
            case "gif": return "GIF 动画文件"
            case "svg": return "SVG 矢量图形文件"
            default: return "图片文件"
            }
        case .video:
            switch ext {
            case "mp4": return "MP4 视频文件"
            case "mov": return "QuickTime 视频文件"
            default: return "视频文件"
            }
        case .audio:
            switch ext {
            case "mp3": return "MP3 音频文件"
            case "wav": return "WAV 音频文件"
            default: return "音频文件"
            }
        case .archive:
            switch ext {
            case "zip": return "ZIP 压缩文件"
            case "rar": return "RAR 压缩文件"
            default: return "压缩文件"
            }
        case .data:
            switch ext {
            case "csv": return "CSV 数据表文件"
            case "xlsx": return "Microsoft Excel 表格"
            default: return "数据文件"
            }
        default:
            return item.contentTypeEnum.displayName
        }
    }
}

// MARK: - 信息行组件
struct ContentInfoRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 80, alignment: .leading)
            
            Text(value)
                .font(.caption)
                .lineLimit(1)
                .truncationMode(.middle)
            
            Spacer()
        }
    }
}

// MARK: - 画廊视图内容预览
struct GalleryContentPreview: View {
    let item: ContentItem
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 文件信息头部
                HStack {
                    Image(systemName: getFileIcon())
                        .foregroundColor(getFileColor())
                        .font(.system(size: 32))
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(item.displayTitle)
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        Text(getDetailedFileType())
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                }
                .padding()
                
                // 内容预览区域
                contentPreview
                    .frame(maxWidth: .infinity)
                    .padding(.horizontal)
                
                Spacer()
            }
        }
        .background(Color(NSColor.textBackgroundColor))
    }
    
    @ViewBuilder
    private var contentPreview: some View {
        switch item.contentTypeEnum {
        case .text:
            if let content = item.content {
                VStack(alignment: .leading, spacing: 8) {
                    Text("内容预览")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    ScrollView {
                        Text(content)
                            .font(.body)
                            .frame(maxWidth: .infinity, alignment: .topLeading)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color(NSColor.controlBackgroundColor))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .strokeBorder(Color.gray.opacity(0.2), lineWidth: 1)
                                    )
                            )
                    }
                    .frame(maxHeight: 300)
                }
            }
            
        case .image:
            if let filePath = item.filePath,
               let image = NSImage(contentsOfFile: filePath) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("图片预览")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Image(nsImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 300)
                        .cornerRadius(8)
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                }
            }
            
        case .file:
            if let filePath = item.filePath {
                VStack(alignment: .leading, spacing: 8) {
                    Text("文件预览")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    // 使用QuickLook预览
                    QuickLookPreview(url: URL(fileURLWithPath: filePath))
                        .frame(maxHeight: 300)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .strokeBorder(Color.gray.opacity(0.2), lineWidth: 1)
                        )
                }
            }
        }
    }
    
    private func getFileIcon() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.systemImage
        }
        
        let ext = URL(fileURLWithPath: filePath).pathExtension.lowercased()
        switch ext {
        case "pdf": return "doc.fill"
        case "doc", "docx": return "doc.text"
        case "xls", "xlsx": return "tablecells"
        case "ppt", "pptx": return "rectangle.3.group.bubble.left"
        case "zip", "rar": return "archivebox"
        case "mp3", "wav": return "music.note"
        case "mp4", "mov": return "video"
        case "jpg", "jpeg", "png", "gif": return "photo"
        default: return item.contentTypeEnum.systemImage
        }
    }
    
    private func getFileColor() -> Color {
        guard let filePath = item.filePath else {
            return Color(tagColor: item.contentTypeEnum.color)
        }
        
        let fileType = FileTypeManager.shared.identifyFileType(for: URL(fileURLWithPath: filePath))
        return fileType.color
    }
    
    private func getDetailedFileType() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.displayName
        }
        
        let url = URL(fileURLWithPath: filePath)
        let fileType = FileTypeManager.shared.identifyFileType(for: url)
        let ext = url.pathExtension.lowercased()
        
        switch fileType {
        case .document:
            switch ext {
            case "md", "markdown": return "Markdown 文档文件"
            case "pdf": return "PDF 文档文件"
            case "doc", "docx": return "Microsoft Word 文档"
            default: return "文档文件"
            }
        case .data:
            switch ext {
            case "xlsx", "xls": return "Microsoft Excel 表格"
            case "ppt", "pptx": return "Microsoft PowerPoint 演示文稿"
            default: return "数据文件"
            }
        default:
            return item.contentTypeEnum.displayName
        }
    }
}

// MARK: - 画廊文件项目
struct GalleryFileItem: View {
    let item: ContentItem
    let isSelected: Bool
    let onSelectionChange: (Bool) -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        VStack(spacing: 8) {
            // 文件缩略图
            ZStack {
                thumbnailContent
                    .frame(width: 80, height: 60)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(6)
                
                // 选中状态指示器
                if isSelected {
                    RoundedRectangle(cornerRadius: 6)
                        .strokeBorder(Color.accentColor, lineWidth: 3)
                        .frame(width: 80, height: 60)
                }
            }
            
            // 文件名
            Text(item.displayTitle)
                .font(.caption)
                .lineLimit(2)
                .multilineTextAlignment(.center)
                .frame(height: 32)
            
            // 文件信息
            VStack(spacing: 2) {
                Text(getFileTypeDescription())
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                if item.contentTypeEnum != .text {
                    Text(item.formattedFileSize)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? Color.accentColor.opacity(0.15) : (isHovered ? Color.gray.opacity(0.1) : Color.clear))
        )
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .onTapGesture {
            onSelectionChange(true)
        }
        .onTapGesture(count: 2) {
            if let filePath = item.filePath {
                NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
            }
        }
    }
    
    @ViewBuilder
    private var thumbnailContent: some View {
        switch item.contentTypeEnum {
        case .image:
            if let filePath = item.filePath,
               let image = NSImage(contentsOfFile: filePath) {
                Image(nsImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 80, height: 60)
                    .clipped()
            } else {
                Image(systemName: "photo")
                    .font(.system(size: 24))
                    .foregroundColor(.secondary)
            }
            
        case .text:
            Image(systemName: "doc.text")
                .font(.system(size: 24))
                .foregroundColor(.blue)
            
        case .file:
            Image(systemName: getFileIcon())
                .font(.system(size: 24))
                .foregroundColor(getFileColor())
        }
    }
    
    private func getFileIcon() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.systemImage
        }
        
        let ext = URL(fileURLWithPath: filePath).pathExtension.lowercased()
        switch ext {
        case "pdf": return "doc.fill"
        case "doc", "docx": return "doc.text"
        case "xls", "xlsx": return "tablecells"
        case "ppt", "pptx": return "rectangle.3.group.bubble.left"
        case "zip", "rar": return "archivebox"
        case "mp3", "wav": return "music.note"
        case "mp4", "mov": return "video"
        default: return item.contentTypeEnum.systemImage
        }
    }
    
    private func getFileColor() -> Color {
        guard let filePath = item.filePath else {
            return Color(tagColor: item.contentTypeEnum.color)
        }
        
        let fileType = FileTypeManager.shared.identifyFileType(for: URL(fileURLWithPath: filePath))
        return fileType.color
    }
    
    private func getFileTypeDescription() -> String {
        guard let filePath = item.filePath else {
            return item.contentTypeEnum.displayName
        }
        
        let url = URL(fileURLWithPath: filePath)
        let ext = url.pathExtension.uppercased()
        return ext.isEmpty ? item.contentTypeEnum.displayName : "\(ext)"
    }
}

// MARK: - QuickLook预览组件
struct QuickLookPreview: NSViewRepresentable {
    let url: URL
    
    func makeNSView(context: Context) -> NSView {
        let view = NSView()
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        
        let label = NSTextField(labelWithString: "预览: \(url.lastPathComponent)")
        label.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(label)
        
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: view.centerYAnchor)
        ])
        
        return view
    }
    
    func updateNSView(_ nsView: NSView, context: Context) {
        // 更新预览内容
    }
}
// MARK: - PDF内置预览组件
struct PDFInlinePreview: View {
    let url: URL
    
    var body: some View {
        VStack(spacing: 8) {
            if let pdfDocument = PDFDocument(url: url),
               let firstPage = pdfDocument.page(at: 0) {
                
                let pageRect = firstPage.bounds(for: .mediaBox)
                let thumbnail = firstPage.thumbnail(of: CGSize(width: 200, height: 120), for: .mediaBox)
                
                Image(nsImage: thumbnail)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 100)
                        .background(Color.white)
                        .cornerRadius(4)
                        .overlay(
                            RoundedRectangle(cornerRadius: 4)
                                .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1)
                        )
            } else {
                pdfPlaceholder
            }
            
            Text("PDF 文档预览")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
    
    private var pdfPlaceholder: some View {
        VStack(spacing: 4) {
            Image(systemName: "doc.richtext.fill")
                .font(.system(size: 32))
                .foregroundColor(.red)
            
            Text("PDF")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(height: 100)
        .frame(maxWidth: .infinity)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(4)
    }
}

// MARK: - 文本文件内置预览组件
struct TextFileInlinePreview: View {
    let url: URL
    @State private var content: String = ""
    
    var body: some View {
        VStack(spacing: 8) {
            ScrollView {
                Text(content.isEmpty ? "加载中..." : content)
                    .font(.caption)
                    .lineLimit(8)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(8)
            }
            .frame(height: 100)
            .background(
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color(NSColor.textBackgroundColor))
                    .overlay(
                        RoundedRectangle(cornerRadius: 4)
                            .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1)
                    )
            )
            
            Text(getFileTypeLabel())
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .onAppear {
            loadTextContent()
        }
    }
    
    private func loadTextContent() {
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let text = try String(contentsOf: url, encoding: .utf8)
                DispatchQueue.main.async {
                    self.content = String(text.prefix(500)) // 限制预览长度
                }
            } catch {
                DispatchQueue.main.async {
                    self.content = "无法读取文件内容"
                }
            }
        }
    }
    
    private func getFileTypeLabel() -> String {
        let ext = url.pathExtension.lowercased()
        switch ext {
        case "txt": return "文本文件预览"
        case "md": return "Markdown 文档预览"
        case "json": return "JSON 数据预览"
        case "xml": return "XML 文档预览"
        case "csv": return "CSV 数据预览"
        default: return "文本文件预览"
        }
    }
}

// 需要导入PDFKit
import PDFKit