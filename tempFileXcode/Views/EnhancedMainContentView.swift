import SwiftUI
import CoreData

/// 增强版主内容视图 - 整合所有新功能
struct EnhancedMainContentView: View {
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var contentService: ContentService
    @EnvironmentObject private var searchService: SearchService
    @EnvironmentObject private var exportService: ExportService
    @Environment(\.managedObjectContext) private var viewContext
    
    // 新功能管理器
    @StateObject private var viewModeManager = ViewModeManager()
    @StateObject private var selectionManager = SelectionManager()
    @StateObject private var systemPermissionManager = SystemPermissionManager.shared
    
    @State private var settingsWindowController: SettingsWindowController?
    @State private var showingExportSheet = false
    @State private var showingPermissionSettings = false
    @State private var searchText = ""
    
    // 获取所有内容项
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)],
        animation: .default)
    private var allContentItems: FetchedResults<ContentItem>
    
    // 获取当前批次的内容
    private var currentBatchItems: [ContentItem] {
        return Array(allContentItems)
    }
    
    // 过滤后的内容
    private var filteredItems: [ContentItem] {
        if searchText.isEmpty {
            return currentBatchItems
        } else {
            return currentBatchItems.filter { item in
                item.displayTitle.localizedCaseInsensitiveContains(searchText) ||
                (item.content?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }
    }
    
    var body: some View {
        NavigationSplitView {
            // 侧边栏
            EnhancedSidebarView(
                selectionManager: selectionManager,
                onPermissionSettings: { showingPermissionSettings = true }
            )
            .navigationSplitViewColumnWidth(min: 200, ideal: 250, max: 300)
        } detail: {
            // 主内容区域
            VStack(spacing: 0) {
                // 顶部工具栏
                topToolbar
                
                Divider()
                
                // 搜索栏
                searchBar
                
                // 多选工具栏
                MultiSelectionToolbar(
                    selectionManager: selectionManager,
                    onDelete: handleDeleteSelected,
                    onExport: handleExportSelected,
                    onCopyPaths: handleCopyPaths
                )
                
                // 主内容视图
                mainContentArea
            }
        }
        .sheet(isPresented: $showingExportSheet) {
            ExportSheet(selectedItems: selectionManager.getSelectedItems())
        }
        .sheet(isPresented: $showingPermissionSettings) {
            PermissionSettingsView()
        }
        .onAppear {
            selectionManager.setAllItems(currentBatchItems)
        }
        .onChange(of: currentBatchItems) { _, newItems in
            selectionManager.setAllItems(newItems)
        }
    }
    
    // MARK: - 顶部工具栏
    @ViewBuilder
    private var topToolbar: some View {
        HStack {
            // 左侧：批次信息
            VStack(alignment: .leading, spacing: 2) {
                Text("内容管理")
                    .font(.headline)
                    
                Text("\(filteredItems.count) 项内容")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 中间：视图模式选择器
            ViewModeSelector(viewModeManager: viewModeManager)
            
            Spacer()
            
            // 右侧：操作按钮
            HStack(spacing: 8) {
                // 权限状态指示器
                permissionStatusIndicator
                
                // 添加内容按钮
                Button(action: addNewContent) {
                    Image(systemName: "plus")
                }
                .help("添加新内容 (⌘N)")
                
                // 导出按钮
                Button(action: { showingExportSheet = true }) {
                    Image(systemName: "square.and.arrow.up")
                }
                .help("导出内容 (⌘E)")
                .disabled(filteredItems.isEmpty)
                
                // 设置按钮
                Button(action: showSettings) {
                    Image(systemName: "gear")
                }
                .help("设置 (⌘,)")
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    // MARK: - 权限状态指示器
    @ViewBuilder
    private var permissionStatusIndicator: some View {
        let status = systemPermissionManager.getPermissionStatus()
        
        Button(action: { showingPermissionSettings = true }) {
            HStack(spacing: 4) {
                Image(systemName: status.hasFullAccess ? "checkmark.shield" : "exclamationmark.shield")
                    .foregroundColor(status.hasFullAccess ? .green : .orange)
                
                if !status.hasFullAccess && status.trustedFolders.isEmpty {
                    Text("权限")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
            }
        }
        .buttonStyle(.plain)
        .help(status.description)
    }
    
    // MARK: - 搜索栏
    @ViewBuilder
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索内容...", text: $searchText)
                .textFieldStyle(.plain)
            
            if !searchText.isEmpty {
                Button(action: { searchText = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
                .buttonStyle(.plain)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color(NSColor.textBackgroundColor))
        )
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
    }
    
    // MARK: - 主内容区域
    @ViewBuilder
    private var mainContentArea: some View {
        if filteredItems.isEmpty {
            // 空状态视图
            emptyStateView
        } else {
            // 内容视图
            UnifiedContentView(
                items: filteredItems,
                viewModeManager: viewModeManager,
                selectionManager: selectionManager,
                onItemSelect: handleItemSelect,
                onItemEdit: handleItemEdit,
                onItemQuickLook: handleItemQuickLook
            )
            .background(SelectionKeyboardHandler(selectionManager: selectionManager))
        }
    }
    
    // MARK: - 空状态视图
    @ViewBuilder
    private var emptyStateView: some View {
        ContentUnavailableView {
            Label("没有内容", systemImage: "tray")
        } description: {
            if searchText.isEmpty {
                Text("拖拽文件到此处或点击添加按钮来添加内容")
            } else {
                Text("没有找到匹配 \"\(searchText)\" 的内容")
            }
        } actions: {
            if searchText.isEmpty {
                Button("添加内容", action: addNewContent)
                    .buttonStyle(.borderedProminent)
            } else {
                Button("清除搜索") {
                    searchText = ""
                }
                .buttonStyle(.bordered)
            }
        }
    }
    
    // MARK: - 事件处理
    
    private func handleItemSelect(_ item: ContentItem) {
        selectionManager.selectSingle(item.id?.uuidString ?? "")
    }
    
    private func handleItemEdit(_ item: ContentItem) {
        // TODO: 实现编辑功能
        print("编辑项目: \(item.displayTitle)")
    }
    
    private func handleItemQuickLook(_ item: ContentItem) {
        // TODO: 实现快速预览功能
        print("快速预览: \(item.displayTitle)")
        
        // 如果有文件路径，使用Quick Look预览
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            NSWorkspace.shared.open(url)
        }
    }
    
    private func handleDeleteSelected() {
        let selectedItems = selectionManager.getSelectedItems()
        // TODO: 实现删除功能
        print("删除 \(selectedItems.count) 个项目")
        selectionManager.deselectAll()
    }
    
    private func handleExportSelected() {
        showingExportSheet = true
    }
    
    private func handleCopyPaths() {
        // 已在 SelectionManager 中实现
    }
    
    private func addNewContent() {
        // TODO: 实现添加内容功能
        print("添加新内容")
    }
    
    private func showSettings() {
        if settingsWindowController == nil {
            settingsWindowController = SettingsWindowController()
        }
        settingsWindowController?.showWindow(nil)
        settingsWindowController?.window?.makeKeyAndOrderFront(nil)
    }
}

// MARK: - 增强版侧边栏
struct EnhancedSidebarView: View {
    @EnvironmentObject private var contentService: ContentService
    @EnvironmentObject private var batchService: BatchService
    @ObservedObject var selectionManager: SelectionManager
    let onPermissionSettings: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // 批次列表
            BatchSidebarView()
            
            Divider()
            
            // 权限和设置区域
            VStack(spacing: 8) {
                // 权限状态
                permissionStatusSection
                
                // 快速操作
                quickActionsSection
            }
            .padding(12)
            .background(Color(NSColor.controlBackgroundColor))
        }
    }
    
    @ViewBuilder
    private var permissionStatusSection: some View {
        let systemManager = SystemPermissionManager.shared
        let status = systemManager.getPermissionStatus()
        
        VStack(alignment: .leading, spacing: 6) {
            HStack {
                Image(systemName: status.hasFullAccess ? "checkmark.shield.fill" : "exclamationmark.shield.fill")
                    .foregroundColor(status.hasFullAccess ? .green : .orange)
                
                Text("文件权限")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
            }
            
            Text(status.description)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
            
            if !status.hasFullAccess {
                Button("设置权限") {
                    onPermissionSettings()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
            }
        }
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(status.hasFullAccess ? Color.green.opacity(0.1) : Color.orange.opacity(0.1))
        )
    }
    
    @ViewBuilder
    private var quickActionsSection: some View {
        VStack(spacing: 4) {
            Text("快速操作")
                .font(.subheadline)
                .fontWeight(.medium)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 2) {
                Button(action: { /* TODO */ }) {
                    HStack(spacing: 8) {
                        Image(systemName: "plus.circle")
                            .foregroundColor(.accentColor)
                            .frame(width: 16)
                        
                        Text("添加内容")
                            .font(.system(size: 12))
                        
                        Spacer()
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                }
                .buttonStyle(.plain)
                
                Button(action: { /* TODO */ }) {
                    HStack(spacing: 8) {
                        Image(systemName: "folder.badge.plus")
                            .foregroundColor(.accentColor)
                            .frame(width: 16)
                        
                        Text("新建批次")
                            .font(.system(size: 12))
                        
                        Spacer()
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                }
                .buttonStyle(.plain)
                
                Button(action: { /* TODO */ }) {
                    HStack(spacing: 8) {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(.accentColor)
                            .frame(width: 16)
                        
                        Text("导出数据")
                            .font(.system(size: 12))
                        
                        Spacer()
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                }
                .buttonStyle(.plain)
            }
        }
    }
}

// MARK: - 快速操作按钮 (使用 EnhancedContentCard 中定义的版本)

// MARK: - 权限设置视图
struct PermissionSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var systemManager = SystemPermissionManager.shared
    
    var body: some View {
        VStack(spacing: 20) {
            // 标题
            HStack {
                Image(systemName: "shield.checkered")
                    .font(.title2)
                    .foregroundColor(.accentColor)
                
                Text("文件访问权限设置")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("关闭") {
                    dismiss()
                }
            }
            
            Divider()
            
            // 当前状态
            let status = systemManager.getPermissionStatus()
            
            VStack(alignment: .leading, spacing: 12) {
                Text("当前状态")
                    .font(.headline)
                
                HStack {
                    Image(systemName: status.hasFullAccess ? "checkmark.circle.fill" : "exclamationmark.circle.fill")
                        .foregroundColor(status.hasFullAccess ? .green : .orange)
                    
                    Text(status.description)
                        .font(.subheadline)
                    
                    Spacer()
                }
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(status.hasFullAccess ? Color.green.opacity(0.1) : Color.orange.opacity(0.1))
                )
                
                if !status.trustedFolders.isEmpty {
                    Text("已授权文件夹:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    ForEach(status.trustedFolders, id: \.self) { folder in
                        HStack {
                            Image(systemName: "folder.fill")
                                .foregroundColor(.blue)
                            
                            Text(folder)
                                .font(.system(size: 11, design: .monospaced))
                            
                            Spacer()
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.blue.opacity(0.1))
                        )
                    }
                }
            }
            
            Divider()
            
            // 操作按钮
            VStack(spacing: 12) {
                Button("设置系统权限") {
                    systemManager.showSystemPermissionGuide { _ in
                        // 权限设置完成后的处理
                    }
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)
                
                Button("授权文件夹") {
                    systemManager.showSystemPermissionGuide { _ in
                        // 文件夹授权完成后的处理
                    }
                }
                .buttonStyle(.bordered)
                .controlSize(.large)
            }
            
            Spacer()
        }
        .padding(20)
        .frame(width: 500, height: 400)
    }
}

// MARK: - 键盘快捷键支持
extension EnhancedMainContentView {
    private func setupKeyboardShortcuts() {
        // TODO: 实现键盘快捷键
        // ⌘N - 新建内容
        // ⌘E - 导出
        // ⌘, - 设置
        // ⌘A - 全选
        // Delete - 删除选中
        // Space - 快速预览
    }
}