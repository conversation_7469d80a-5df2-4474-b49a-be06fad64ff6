import SwiftUI
import UniformTypeIdentifiers

// MARK: - Content Drop Handler
struct ContentDropHandler: ViewModifier {
    let contentService: ContentService
    let pasteboardMonitor: PasteboardMonitor
    let batchService: BatchService?
    
    @State private var isTargeted = false
    
    init(contentService: ContentService, pasteboardMonitor: PasteboardMonitor, batchService: BatchService? = nil) {
        self.contentService = contentService
        self.pasteboardMonitor = pasteboardMonitor
        self.batchService = batchService
    }
    
    func body(content: Content) -> some View {
        content
            .onDrop(of: [UTType.fileURL.identifier, UTType.text.identifier, UTType.image.identifier], 
                   isTargeted: $isTargeted) { providers in
                NSLog("拖拽事件触发，提供者数量: \(providers.count)")
                showDragDropWindow()
                return true
            }
            .overlay(
                dropOverlay
                    .opacity(isTargeted ? 1 : 0)
                    .animation(.easeInOut(duration: 0.2), value: isTargeted)
                    .allowsHitTesting(false) // 确保覆盖层不阻塞交互
            )
    }
    
    private var dropOverlay: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color.accentColor.opacity(0.1))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .strokeBorder(Color.accentColor, lineWidth: 2, antialiased: true)
            )
            .overlay(
                VStack(spacing: 8) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 48))
                        .foregroundColor(.accentColor)
                    
                    Text("松开鼠标添加内容")
                        .font(.headline)
                        .foregroundColor(.accentColor)
                }
            )
    }
    
    private func handleDrop(providers: [NSItemProvider]) {
        NSLog("开始处理拖拽内容，提供者数量: \(providers.count)")
        
        Task {
            for provider in providers {
                NSLog("处理提供者，支持的类型: \(provider.registeredTypeIdentifiers)")
                
                // 处理文件URL
                if provider.hasItemConformingToTypeIdentifier(UTType.fileURL.identifier) {
                    await handleFileURL(provider: provider)
                }
                // 处理纯文本
                else if provider.hasItemConformingToTypeIdentifier(UTType.text.identifier) {
                    await handleText(provider: provider)
                }
                // 处理图片
                else if provider.hasItemConformingToTypeIdentifier(UTType.image.identifier) {
                    await handleImage(provider: provider)
                }
            }
        }
    }
    
    private func handleFileURL(provider: NSItemProvider) async {
        do {
            let item = try await provider.loadItem(forTypeIdentifier: UTType.fileURL.identifier, options: nil)
            if let url = item as? URL {
                NSLog("成功获取文件URL: \(url.path)")
                
                do {
                    let contentData = try ContentData.fromFile(at: url)
                    let batch = batchService?.currentBatch
                    _ = try await contentService.addContent(contentData, toBatch: batch)
                    NSLog("成功添加文件: \(url.lastPathComponent)")
                } catch {
                    NSLog("添加文件失败: \(error)")
                }
            }
        } catch {
            NSLog("加载文件URL失败: \(error)")
        }
    }
    
    private func handleText(provider: NSItemProvider) async {
        do {
            let item = try await provider.loadItem(forTypeIdentifier: UTType.text.identifier, options: nil)
            if let text = item as? String {
                NSLog("成功获取文本内容，长度: \(text.count)")
                
                let contentData = ContentData.fromText(text, title: nil)
                let batch = batchService?.currentBatch
                _ = try await contentService.addContent(contentData, toBatch: batch)
                NSLog("成功添加文本内容")
            }
        } catch {
            NSLog("加载文本失败: \(error)")
        }
    }
    
    private func handleImage(provider: NSItemProvider) async {
        do {
            let item = try await provider.loadItem(forTypeIdentifier: UTType.image.identifier, options: nil)
            if let image = item as? NSImage {
                NSLog("成功获取图片")
                
                // 将图片保存到临时文件
                if let tiffData = image.tiffRepresentation,
                   let bitmapRep = NSBitmapImageRep(data: tiffData),
                   let pngData = bitmapRep.representation(using: .png, properties: [:]) {
                    
                    let tempURL = FileManager.default.temporaryDirectory
                        .appendingPathComponent(UUID().uuidString)
                        .appendingPathExtension("png")
                    
                    try pngData.write(to: tempURL)
                    
                    let contentData = try ContentData.fromFile(at: tempURL)
                    let batch = batchService?.currentBatch
                    _ = try await contentService.addContent(contentData, toBatch: batch)
                    NSLog("成功添加图片内容")
                }
            }
        } catch {
            NSLog("加载图片失败: \(error)")
        }
    }
    
    private func showDragDropWindow() {
        // 创建并显示拖拽窗口
        if let batchService = batchService {
            let dragDropManager = DragDropWindowManager(
                contentService: contentService,
                batchService: batchService
            )
            dragDropManager.showWindow()
        }
    }
}

// MARK: - View Extension
extension View {
    func contentDropHandler(
        contentService: ContentService,
        pasteboardMonitor: PasteboardMonitor,
        batchService: BatchService? = nil
    ) -> some View {
        self.modifier(ContentDropHandler(
            contentService: contentService,
            pasteboardMonitor: pasteboardMonitor,
            batchService: batchService
        ))
    }
}