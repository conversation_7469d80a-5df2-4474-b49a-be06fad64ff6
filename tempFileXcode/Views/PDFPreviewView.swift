import SwiftUI
import PDFKit
import AppKit
import Foundation

// MARK: - PDF原生预览视图
struct NativePDFPreviewView: NSViewRepresentable {
    let pdfData: Data
    let showControls: Bool
    
    init(pdfData: Data, showControls: Bool = true) {
        self.pdfData = pdfData
        self.showControls = showControls
    }
    
    func makeNSView(context: Context) -> PDFView {
        let pdfView = PDFView()
        setupPDFView(pdfView)
        return pdfView
    }
    
    func updateNSView(_ pdfView: PDFView, context: Context) {
        if let document = PDFDocument(data: pdfData) {
            pdfView.document = document
        }
    }
    
    private func setupPDFView(_ pdfView: PDFView) {
        // 基本设置
        pdfView.autoScales = true
        pdfView.displayMode = .singlePage
        pdfView.displayDirection = .vertical
        
        // 根据是否显示控件调整设置
        if showControls {
            // 完整预览模式 - 显示所有控件
            pdfView.displaysPageBreaks = true
            pdfView.pageBreakMargins = NSEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        } else {
            // 紧凑预览模式 - 隐藏控件，适合缩略图
            pdfView.displaysPageBreaks = false
        }
        
        // 设置背景颜色
        pdfView.backgroundColor = NSColor.controlBackgroundColor
        
        // 加载PDF文档
        if let document = PDFDocument(data: pdfData) {
            pdfView.document = document
            
            // 如果是紧凑模式且有多页，只显示第一页
            if !showControls && document.pageCount > 0 {
                pdfView.go(to: document.page(at: 0)!)
            }
        }
    }
}

// MARK: - 内嵌PDF预览组件
struct InlinePDFPreview: View {
    let filePath: String
    let maxHeight: CGFloat
    let showControls: Bool
    
    @State private var pdfData: Data?
    @State private var isLoading = true
    @State private var hasError = false
    
    init(filePath: String, maxHeight: CGFloat = 300, showControls: Bool = true) {
        self.filePath = filePath
        self.maxHeight = maxHeight
        self.showControls = showControls
    }
    
    var body: some View {
        Group {
            if isLoading {
                loadingView
            } else if hasError || pdfData == nil {
                errorView
            } else {
                pdfPreview
            }
        }
        .frame(maxHeight: maxHeight)
        .cornerRadius(8)
        .onAppear {
            loadPDF()
        }
    }
    
    @ViewBuilder
    private var loadingView: some View {
        VStack(spacing: 12) {
            ProgressView()
                .scaleEffect(0.8)
            
            Text("正在加载PDF...")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    @ViewBuilder
    private var errorView: some View {
        VStack(spacing: 12) {
            Image(systemName: "doc.text.fill")
                .font(.system(size: 32))
                .foregroundColor(.secondary)
            
            Text("无法加载PDF文件")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("文件可能已损坏或不存在")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    @ViewBuilder
    private var pdfPreview: some View {
        if let data = pdfData {
            NativePDFPreviewView(pdfData: data, showControls: showControls)
                .overlay(
                    // 添加边框
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(Color.secondary.opacity(0.2), lineWidth: 1)
                )
        }
    }
    
    private func loadPDF() {
        Task {
            do {
                let url = URL(fileURLWithPath: filePath)
                let data = try Data(contentsOf: url)
                
                // 验证是否为有效的PDF文件
                if PDFDocument(data: data) != nil {
                    await MainActor.run {
                        self.pdfData = data
                        self.isLoading = false
                        self.hasError = false
                    }
                } else {
                    await MainActor.run {
                        self.hasError = true
                        self.isLoading = false
                    }
                }
            } catch {
                await MainActor.run {
                    self.hasError = true
                    self.isLoading = false
                }
            }
        }
    }
}

// MARK: - PDF缩略图预览
struct PDFThumbnailView: View {
    let filePath: String
    let size: CGSize
    
    @State private var thumbnailImage: NSImage?
    @State private var isLoading = true
    
    init(filePath: String, size: CGSize = CGSize(width: 80, height: 100)) {
        self.filePath = filePath
        self.size = size
    }
    
    var body: some View {
        Group {
            if isLoading {
                ProgressView()
                    .scaleEffect(0.5)
                    .frame(width: size.width, height: size.height)
            } else if let image = thumbnailImage {
                Image(nsImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: size.width, height: size.height)
            } else {
                pdfPlaceholder
            }
        }
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(4)
        .onAppear {
            generateThumbnail()
        }
    }
    
    @ViewBuilder
    private var pdfPlaceholder: some View {
        VStack(spacing: 4) {
            Image(systemName: "doc.richtext.fill")
                .font(.system(size: 24))
                .foregroundColor(.orange)
            
            Text("PDF")
                .font(.system(size: 8, weight: .bold))
                .foregroundColor(.orange)
        }
        .frame(width: size.width, height: size.height)
    }
    
    private func generateThumbnail() {
        Task {
            do {
                let url = URL(fileURLWithPath: filePath)
                let data = try Data(contentsOf: url)
                
                if let pdfDocument = PDFDocument(data: data),
                   let firstPage = pdfDocument.page(at: 0) {
                    
                    let pageRect = firstPage.bounds(for: .mediaBox)
                    let thumbnailSize = calculateThumbnailSize(pageRect: pageRect, targetSize: size)
                    
                    let image = firstPage.thumbnail(of: thumbnailSize, for: .mediaBox)
                    
                    await MainActor.run {
                        self.thumbnailImage = image
                        self.isLoading = false
                    }
                } else {
                    await MainActor.run {
                        self.isLoading = false
                    }
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                }
            }
        }
    }
    
    private func calculateThumbnailSize(pageRect: CGRect, targetSize: CGSize) -> CGSize {
        let aspectRatio = pageRect.width / pageRect.height
        
        if aspectRatio > targetSize.width / targetSize.height {
            // 页面更宽，以宽度为准
            return CGSize(width: targetSize.width, height: targetSize.width / aspectRatio)
        } else {
            // 页面更高，以高度为准
            return CGSize(width: targetSize.height * aspectRatio, height: targetSize.height)
        }
    }
}

// MARK: - 通用文档预览组件
struct DocumentPreviewView: View {
    let item: ContentItem
    let maxHeight: CGFloat
    let showControls: Bool
    
    init(item: ContentItem, maxHeight: CGFloat = 300, showControls: Bool = true) {
        self.item = item
        self.maxHeight = maxHeight
        self.showControls = showControls
    }
    
    var body: some View {
        Group {
            if let filePath = item.filePath {
                let fileExtension = URL(fileURLWithPath: filePath).pathExtension.lowercased()
                
                switch fileExtension {
                case "pdf":
                    InlinePDFPreview(
                        filePath: filePath, 
                        maxHeight: maxHeight,
                        showControls: showControls
                    )
                default:
                    // 其他文档类型的占位符
                    documentPlaceholder
                }
            } else {
                noFileView
            }
        }
    }
    
    @ViewBuilder
    private var documentPlaceholder: some View {
        VStack(spacing: 12) {
            Image(systemName: "doc.richtext")
                .font(.system(size: 32))
                .foregroundColor(.secondary)
            
            Text("文档预览")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("此文件类型暂不支持内嵌预览")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: maxHeight)
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }
    
    @ViewBuilder
    private var noFileView: some View {
        VStack(spacing: 12) {
            Image(systemName: "questionmark.folder")
                .font(.system(size: 32))
                .foregroundColor(.secondary)
            
            Text("无文件信息")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: maxHeight)
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }
}