import SwiftUI

// MARK: - Batch Settings Popover
struct BatchSettingsPopover: View {
    @EnvironmentObject private var batchService: BatchService
    @Environment(\.dismiss) private var dismiss
    
    @State private var batchName: String = ""
    @State private var batchDescription: String = ""
    @State private var showingDeleteAlert = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("批次设置")
                .font(.headline)
                .padding(.bottom, 4)
            
            if let currentBatch = batchService.currentBatch {
                VStack(alignment: .leading, spacing: 12) {
                    // 批次名称
                    VStack(alignment: .leading, spacing: 4) {
                        Text("批次名称")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        TextField("输入批次名称", text: $batchName)
                            .textFieldStyle(.roundedBorder)
                    }
                    
                    // 批次描述
                    VStack(alignment: .leading, spacing: 4) {
                        Text("批次描述")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        TextField("输入批次描述（可选）", text: $batchDescription, axis: .vertical)
                            .textFieldStyle(.roundedBorder)
                            .lineLimit(3...5)
                    }
                    
                    // 批次统计
                    VStack(alignment: .leading, spacing: 4) {
                        Text("批次统计")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        let itemCount = currentBatch.value(forKey: "contentItems") as? NSSet
                        Text("包含 \(itemCount?.count ?? 0) 个项目")
                            .font(.caption)
                            .foregroundColor(.primary)
                        
                        if let createdAt = currentBatch.value(forKey: "createdAt") as? Date {
                            Text("创建于 \(createdAt.formatted(date: .abbreviated, time: .shortened))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Divider()
                    
                    // 操作按钮
                    HStack(spacing: 8) {
                        Button("删除批次") {
                            showingDeleteAlert = true
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.red)
                        
                        Spacer()
                        
                        Button("取消") {
                            dismiss()
                        }
                        .buttonStyle(.bordered)
                        
                        Button("保存") {
                            saveBatchSettings()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
                .onAppear {
                    loadBatchSettings()
                }
                .alert("删除批次", isPresented: $showingDeleteAlert) {
                    Button("取消", role: .cancel) { }
                    Button("删除", role: .destructive) {
                        deleteBatch()
                    }
                } message: {
                    Text("确定要删除这个批次吗？此操作不可撤销。")
                }
            } else {
                Text("没有选中的批次")
                    .foregroundColor(.secondary)
                    .font(.subheadline)
            }
        }
        .padding()
        .frame(minWidth: 300)
    }
    
    private func loadBatchSettings() {
        guard let currentBatch = batchService.currentBatch else { return }
        
        batchName = currentBatch.value(forKey: "name") as? String ?? ""
        batchDescription = currentBatch.value(forKey: "batchDescription") as? String ?? ""
    }
    
    private func saveBatchSettings() {
        guard let currentBatch = batchService.currentBatch else { return }
        
        currentBatch.setValue(batchName.isEmpty ? "未命名批次" : batchName, forKey: "name")
        currentBatch.setValue(batchDescription, forKey: "batchDescription")
        currentBatch.setValue(Date(), forKey: "updatedAt")
        
        do {
            try PersistenceController.shared.container.viewContext.save()
            dismiss()
        } catch {
            NSLog("保存批次设置失败: \(error)")
        }
    }
    
    private func deleteBatch() {
        guard let currentBatch = batchService.currentBatch else { return }
        
        Task {
            do {
                try batchService.deleteBatch(currentBatch)
                await MainActor.run {
                    dismiss()
                }
            } catch {
                NSLog("删除批次失败: \(error)")
            }
        }
    }
}

//#Preview {
//    BatchSettingsPopover()
//        .environmentObject(BatchService())
//}