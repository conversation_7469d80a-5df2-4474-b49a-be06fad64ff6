import SwiftUI
import AppKit
import Combine
import UniformTypeIdentifiers

// MARK: - Quick Paste Window
class QuickPasteWindow: NSWindow {
    private var hostingView: NSHostingView<QuickPasteContentView>?
    private var quickPasteContentView: QuickPasteContentView?
    
    init(contentService: ContentService, pasteboardMonitor: PasteboardMonitor, batchService: BatchService) {
        let contentView = QuickPasteContentView(
            contentService: contentService,
            pasteboardMonitor: pasteboardMonitor,
            batchService: batchService
        )
        
        super.init(
            contentRect: NSRect(x: 0, y: 0, width: 600, height: 280),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )
        
        self.quickPasteContentView = contentView
        setupWindow(contentView: contentView)
    }
    
    private func setupWindow(contentView: QuickPasteContentView) {
        isReleasedWhenClosed = false
        level = .floating
        
        // 设置窗口位置到屏幕顶部中央
        if let screen = NSScreen.main {
            let screenFrame = screen.visibleFrame
            let windowWidth: CGFloat = 600
            let windowHeight: CGFloat = 280
            let x = screenFrame.midX - windowWidth / 2
            let y = screenFrame.maxY - windowHeight - 20 // 距离顶部20像素
            
            setFrame(NSRect(x: x, y: y, width: windowWidth, height: windowHeight), display: true)
        }
        
        // 设置内容视图
        hostingView = NSHostingView(rootView: contentView)
        self.contentView = hostingView
        
        // 设置窗口外观
        backgroundColor = NSColor.clear
        isOpaque = false
        hasShadow = true
        
        // 确保窗口可以接收事件
        acceptsMouseMovedEvents = true
        ignoresMouseEvents = false
    }
    
    func showWindow() {
        orderFront(nil)
        makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
        
        // 确保窗口获得焦点
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.makeKey()
        }
    }
    
    func hideWindow() {
        orderOut(nil)
        // 通知内容视图窗口即将关闭
        quickPasteContentView?.windowWillClose()
    }
    
    func forceClose() {
        quickPasteContentView?.windowWillClose()
        close()
    }
    
    override var canBecomeKey: Bool {
        return true
    }
    
    override var canBecomeMain: Bool {
        return true
    }
}

// MARK: - Quick Paste Content View
struct QuickPasteContentView: View {
    let contentService: ContentService
    let pasteboardMonitor: PasteboardMonitor
    @ObservedObject var batchService: BatchService
    
    // 高性能架构：分离显示和存储
    @State private var displayText = "" // UI显示的文本（可能是摘要）
    @State private var actualText = ""  // 实际存储的完整文本
    @State private var isLongText = false // 标记是否为长文本
    @State private var textCharacterCount = 0 // 缓存字符数量
    
    @State private var imageData: Data?
    @State private var imagePreview: NSImage?
    @State private var isProcessing = false
    @State private var showingSuccess = false
    @State private var successMessage = ""
    @State private var remainingSeconds = 10
    @State private var timerActive = true

    var body: some View {
        VStack(spacing: 16) {
            headerView
            contentAreaView
            bottomBarView
        }
        .padding(20)
        .background(backgroundView)
        .onAppear {
            startAutoCloseTimer()
            loadClipboardContent()
        }
        .onReceive(Timer.publish(every: 1, on: .main, in: .common).autoconnect()) { _ in
            if timerActive {
                if remainingSeconds > 0 {
                    remainingSeconds -= 1
                    print("Timer countdown: \(remainingSeconds) seconds remaining")
                } else {
                    // 当倒计时到0时，立即关闭窗口
                    print("Timer reached 0, closing window")
                    timerActive = false
                    closeWindow()
                }
            }
        }
        .alert("操作结果", isPresented: $showingSuccess) {
            Button("确定") { 
                // 成功添加内容后自动关闭窗口
                if successMessage.contains("成功添加") {
                    closeWindow()
                }
            }
        } message: {
            Text(successMessage)
        }
    }
    
    // MARK: - View Components
    private var headerView: some View {
        HStack {
            Image(systemName: "plus.app.fill")
                .foregroundColor(.accentColor)
                .font(.system(size: 20, weight: .medium))
            
            Text("快速添加内容")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Spacer()
            
            currentBatchLabel
        }
    }
    
    private var currentBatchLabel: some View {
        Group {
            // 优先显示活跃批次，如果没有则显示当前批次
            if let activeBatch = batchService.activeBatch {
                let batchName = activeBatch.value(forKey: "name") as? String ?? "未命名批次"
                Label(batchName, systemImage: "folder.fill")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.accentColor.opacity(0.1))
                    )
            } else if let currentBatch = batchService.currentBatch {
                let batchName = currentBatch.value(forKey: "name") as? String ?? "未命名批次"
                Label(batchName, systemImage: "folder.fill")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.accentColor.opacity(0.1))
                    )
            }
        }
    }
    
    private var contentAreaView: some View {
        HStack(spacing: 16) {
            textInputView
            imagePreviewView
            actionButtonsView
        }
    }
    
    private var textInputView: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("文本内容")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                // 高效显示文本信息
                if textCharacterCount > 0 {
                    Spacer()
                    HStack(spacing: 4) {
                        Text("\(textCharacterCount) 字符")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        if isLongText {
                            Text("· 长文本")
                                .font(.caption2)
                                .foregroundColor(.orange)
                        }
                    }
                }
            }
            
            // 高性能文本输入框：只绑定显示文本
            TextField("在此输入或粘贴文字内容...", text: $displayText, axis: .vertical)
                .textFieldStyle(.plain)
                .lineLimit(2...4)
                .padding(10)
                .background(textFieldBackground)
                .onSubmit {
                    addContent()
                }
                .onChange(of: displayText) { oldValue, newValue in
                    // 高效处理文本变更
                    handleTextChange(from: oldValue, to: newValue)
                }
                .onDrop(of: [UTType.text.identifier, UTType.image.identifier], isTargeted: .constant(false)) { providers in
                    handleDrop(providers: providers)
                    return true
                }
            
            // 长文本提示
            if isLongText {
                HStack {
                    Image(systemName: "info.circle")
                        .foregroundColor(.orange)
                        .font(.caption2)
                    Text("检测到长文本，已优化显示性能")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 2)
            }
        }
        .frame(minWidth: 200)
    }
    
    // MARK: - 高效文本变更处理
    private func handleTextChange(from oldValue: String, to newValue: String) {
        // 防抖动处理
        Task {
            try? await Task.sleep(nanoseconds: 100_000_000) // 100ms防抖动
            
            await MainActor.run {
                // 更新实际文本
                if !newValue.contains("剩余") {
                    // 用户直接输入的文本
                    actualText = newValue
                    textCharacterCount = newValue.count
                    isLongText = newValue.count > 1000
                }
                
                resetAutoCloseTimer()
            }
        }
    }
    
    private var textFieldBackground: some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(Color(NSColor.textBackgroundColor))
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .strokeBorder(
                        displayText.isEmpty ? Color.gray.opacity(0.3) : Color.accentColor.opacity(0.5),
                        lineWidth: 1.5
                    )
            )
    }
    
    private var imagePreviewView: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("图片内容")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            imagePreviewContent
                .onDrop(of: [UTType.image.identifier], isTargeted: .constant(false)) { providers in
                    handleImageDrop(providers: providers)
                    return true
                }
        }
        .frame(width: 120)
    }
    
    private var imagePreviewContent: some View {
        Group {
            if let image = imagePreview {
                Image(nsImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxWidth: 120, maxHeight: 80)
                    .background(Color(NSColor.textBackgroundColor))
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .strokeBorder(Color.accentColor.opacity(0.5), lineWidth: 1.5)
                    )
            } else {
                imagePlaceholder
            }
        }
    }
    
    private var imagePlaceholder: some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(Color(NSColor.textBackgroundColor))
            .frame(width: 120, height: 80)
            .overlay(
                VStack(spacing: 4) {
                    Image(systemName: "photo")
                        .font(.system(size: 20))
                        .foregroundColor(.secondary)
                    Text("粘贴图片")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1.5)
            )
    }
    
    private var actionButtonsView: some View {
        VStack(spacing: 8) {
            pasteButton
            
            if imagePreview != nil {
                clearImageButton
            }
            
            addContentButton
        }
        .frame(width: 80)
    }
    
    private var pasteButton: some View {
        ActionButton(
            title: "粘贴",
            action: pasteAction,
            style: .secondary
        )
        .font(.system(size: 12))
    }
    
    private var clearImageButton: some View {
        ActionButton(
            title: "清除图片",
            action: clearImageAction,
            style: .secondary
        )
        .font(.system(size: 10))
    }
    
    private var addContentButton: some View {
        let buttonTitle = isProcessing ? "添加中..." : "添加"
        let buttonEnabled = hasValidContent() && !isProcessing
        
        return ActionButton(
            title: buttonTitle,
            action: addContentAction,
            style: .primary,
            isEnabled: buttonEnabled
        )
        .font(.system(size: 12))
    }
    
    private func pasteAction() {
        pasteFromClipboard()
        resetAutoCloseTimer()
    }
    
    private func clearImageAction() {
        clearImage()
        resetAutoCloseTimer()
    }
    
    private func addContentAction() {
        addContent()
        resetAutoCloseTimer()
    }
    
    private var bottomBarView: some View {
        HStack {
            timerDisplay
            Spacer()
            controlButtons
        }
    }
    
    private var timerDisplay: some View {
        HStack(spacing: 6) {
            Image(systemName: timerActive ? "timer" : "timer.square")
                .foregroundColor(timerActive ? .orange : .gray)
                .font(.system(size: 12))
            
            Text(timerActive ? "自动关闭：\(remainingSeconds)s" : "已停止自动关闭")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private var controlButtons: some View {
        HStack(spacing: 8) {
            keepOpenButton

            Button("关闭") {
                closeWindow()
            }
            .font(.caption)
            .buttonStyle(.borderless)
            .foregroundColor(.secondary)
            .keyboardShortcut(.escape)
        }
    }

    private var keepOpenButton: some View {
        Group {
            if timerActive {
                Button("保持打开") {
                    stopAutoCloseTimer()
                }
            } else {
                Button("已保持打开") { }
                    .disabled(true)
            }
        }
        .font(.caption)
        .buttonStyle(.borderless)
        .foregroundColor(.accentColor)
    }
    
    private var backgroundView: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(.regularMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .strokeBorder(
                        LinearGradient(
                            colors: [.accentColor.opacity(0.3), .clear],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .shadow(color: .black.opacity(0.15), radius: 20, x: 0, y: 8)
    }
    
    // MARK: - 新增辅助方法
    private func hasValidContent() -> Bool {
        let hasText = !actualText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        let hasImage = imageData != nil
        return hasText || hasImage
    }
    
    private func clearImage() {
        imageData = nil
        imagePreview = nil
    }
    
    private func loadClipboardContent() {
        Task {
            do {
                if let content = try pasteboardMonitor.getCurrentPasteboardContent() {
                    // 在后台线程处理文本
                    await processTextContentAsync(content)
                }
            } catch {
                print("Failed to load clipboard content: \(error)")
            }
        }
    }
    
    // MARK: - 高性能异步文本处理
    private func processTextContentAsync(_ content: PasteboardContent) async {
        switch content.type {
        case .text:
            if let text = content.text, !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                // 在后台线程处理长文本
                let processedResult = await Task.detached {
                    let charCount = text.count
                    let isLong = charCount > 1000 // 超过1000字符认为是长文本
                    
                    let displayText: String
                    if isLong {
                        // 生成高效摘要
                        let summaryLength = min(200, charCount)
                        let endIndex = text.index(text.startIndex, offsetBy: summaryLength)
                        displayText = String(text[..<endIndex]) + "...[剩余\(charCount - summaryLength)字符]"
                    } else {
                        displayText = text
                    }
                    
                    return (displayText: displayText, actualText: text, isLong: isLong, charCount: charCount)
                }.value
                
                // 在主线程更新UI
                await MainActor.run {
                    self.displayText = processedResult.displayText
                    self.actualText = processedResult.actualText
                    self.isLongText = processedResult.isLong
                    self.textCharacterCount = processedResult.charCount
                    print("Auto-loaded text content (\(processedResult.charCount) characters, long: \(processedResult.isLong))")
                }
            }
        case .image:
            if let data = content.data, let image = NSImage(data: data) {
                await MainActor.run {
                    self.imageData = data
                    self.imagePreview = image
                    print("Auto-loaded image content")
                }
            }
        case .file:
            // 文件类型不自动加载到快速添加窗口
            break
        }
    }
    
    private func addContent() {
        if !actualText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            addTextContent()
        } else if let data = imageData {
            addImageContent(data: data)
        }
    }
    
    private func addImageContent(data: Data) {
        isProcessing = true
        
        Task {
            do {
                let contentData = ContentData.fromImage(data, fileName: "粘贴的图片")
                // 添加到活跃批次或当前批次
                let targetBatch = batchService.activeBatch ?? batchService.currentBatch
                _ = try await contentService.addContent(contentData, toBatch: targetBatch)
                
                await MainActor.run {
                    successMessage = "成功添加图片内容"
                    showingSuccess = true
                    clearImage()
                    isProcessing = false
                    
                    // 成功添加后延迟关闭窗口
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        self.closeWindow()
                    }
                }
            } catch {
                await MainActor.run {
                    successMessage = "添加图片失败: \(error.localizedDescription)"
                    showingSuccess = true
                    isProcessing = false
                }
            }
        }
    }
    
    private func handleImageDrop(providers: [NSItemProvider]) {
        Task {
            for provider in providers {
                if provider.hasItemConformingToTypeIdentifier(UTType.image.identifier) {
                    do {
                        let data = try await provider.loadItem(forTypeIdentifier: UTType.image.identifier, options: nil) as? Data
                        if let data = data, let image = NSImage(data: data) {
                            await MainActor.run {
                                self.imageData = data
                                self.imagePreview = image
                                resetAutoCloseTimer()
                            }
                        }
                    } catch {
                        print("Failed to load image from drop: \(error)")
                    }
                }
            }
        }
    }
    
    private func handleDrop(providers: [NSItemProvider]) {
        Task {
            for provider in providers {
                // 处理文字
                if provider.hasItemConformingToTypeIdentifier(UTType.text.identifier) {
                    do {
                        let text = try await provider.loadItem(forTypeIdentifier: UTType.text.identifier, options: nil) as? String
                        if let text = text {
                            // 创建临时的PasteboardContent进行高效处理
                            let tempContent = PasteboardContent(
                                type: .text,
                                data: nil,
                                text: text,
                                fileName: nil,
                                mimeType: "text/plain",
                                fileSize: Int64(text.utf8.count)
                            )
                            await processTextContentAsync(tempContent)
                            await MainActor.run {
                                resetAutoCloseTimer()
                            }
                        }
                    } catch {
                        print("Failed to load text from drop: \(error)")
                    }
                }
                // 处理图片
                else if provider.hasItemConformingToTypeIdentifier(UTType.image.identifier) {
                    do {
                        let data = try await provider.loadItem(forTypeIdentifier: UTType.image.identifier, options: nil) as? Data
                        if let data = data, let image = NSImage(data: data) {
                            await MainActor.run {
                                self.imageData = data
                                self.imagePreview = image
                                resetAutoCloseTimer()
                            }
                        }
                    } catch {
                        print("Failed to load image from drop: \(error)")
                    }
                }
            }
        }
    }
    
    private func addTextContent() {
        guard !actualText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        isProcessing = true
        
        Task {
            do {
                // 高性能处理：在后台线程生成ContentData
                let contentData = await Task.detached {
                    return await ContentData.fromText(self.actualText, title: nil)
                }.value
                
                // 添加到活跃批次或当前批次
                let targetBatch = batchService.activeBatch ?? batchService.currentBatch
                _ = try await contentService.addContent(contentData, toBatch: targetBatch)
                
                await MainActor.run {
                    successMessage = "成功添加文字内容\(isLongText ? "(长文本)" : "")"
                    showingSuccess = true
                    
                    // 清空所有状态
                    displayText = ""
                    actualText = ""
                    textCharacterCount = 0
                    isLongText = false
                    isProcessing = false
                    
                    // 成功添加后延迟关闭窗口
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        self.closeWindow()
                    }
                }
            } catch {
                await MainActor.run {
                    successMessage = "添加文字失败: \(error.localizedDescription)"
                    showingSuccess = true
                    isProcessing = false
                }
            }
        }
    }
    
    private func pasteFromClipboard() {
        Task {
            do {
                if let content = try pasteboardMonitor.getCurrentPasteboardContent() {
                    switch content.type {
                    case .text:
                        if content.text != nil {
                            // 高性能异步处理文本
                            await processTextContentAsync(content)
                        }
                    case .image:
                        if let data = content.data, let image = NSImage(data: data) {
                            await MainActor.run {
                                self.imageData = data
                                self.imagePreview = image
                            }
                        }
                    case .file:
                        await MainActor.run {
                            successMessage = "文件类型请使用拖拽功能添加"
                            showingSuccess = true
                        }
                    }
                } else {
                    await MainActor.run {
                        successMessage = "剪贴板中没有可用内容"
                        showingSuccess = true
                    }
                }
            } catch {
                await MainActor.run {
                    successMessage = "粘贴失败: \(error.localizedDescription)"
                    showingSuccess = true
                }
            }
        }
    }
    
    // MARK: - 定时器相关方法
    private func startAutoCloseTimer() {
        remainingSeconds = 10
        timerActive = true
        print("Auto-close timer started: \(remainingSeconds) seconds")
    }
    
    private func stopAutoCloseTimer() {
        timerActive = false
        // 不要重置remainingSeconds，保持当前值用于显示
        print("Auto-close timer stopped")
    }
    
    private func resetAutoCloseTimer() {
        remainingSeconds = 10
        timerActive = true
        print("Auto-close timer reset: \(remainingSeconds) seconds")
    }
    
    private func closeWindow() {
        stopAutoCloseTimer()
        print("Closing quick paste window")
        
        // 通知管理器窗口已关闭
        DispatchQueue.main.async {
            // 查找并关闭窗口
            for window in NSApp.windows {
                if let quickWindow = window as? QuickPasteWindow {
                    quickWindow.forceClose()
                    break
                }
            }
        }
    }
    
    // 窗口即将关闭时的清理方法
    func windowWillClose() {
        stopAutoCloseTimer()
        print("Quick paste window will close")
    }
}

// MARK: - Quick Paste Manager
@MainActor
class QuickPasteManager: ObservableObject {
    @Published var isWindowVisible: Bool = false

    private var quickPasteWindow: QuickPasteWindow?
    private let contentService: ContentService
    private let pasteboardMonitor: PasteboardMonitor
    private let batchService: BatchService

    init(contentService: ContentService, pasteboardMonitor: PasteboardMonitor, batchService: BatchService) {
        self.contentService = contentService
        self.pasteboardMonitor = pasteboardMonitor
        self.batchService = batchService
    }

    func showQuickPasteWindow() {
        // 每次显示时都创建新的窗口实例，确保内容能正确加载
        quickPasteWindow = QuickPasteWindow(
            contentService: contentService,
            pasteboardMonitor: pasteboardMonitor,
            batchService: batchService
        )

        quickPasteWindow?.showWindow()
        isWindowVisible = true
    }

    func hideQuickPasteWindow() {
        quickPasteWindow?.hideWindow()
        quickPasteWindow = nil // 释放窗口实例
        isWindowVisible = false
    }

    func toggleQuickPasteWindow() {
        if let window = quickPasteWindow, window.isVisible {
            hideQuickPasteWindow()
        } else {
            showQuickPasteWindow()
        }
    }
}

#Preview {
    QuickPasteContentView(
        contentService: ContentService(),
        pasteboardMonitor: PasteboardMonitor(),
        batchService: BatchService()
    )
    .frame(width: 600, height: 280)
}