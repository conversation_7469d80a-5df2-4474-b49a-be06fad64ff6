import SwiftUI
import CoreData

struct MainContentView: View {
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var contentService: ContentService
    @EnvironmentObject private var searchService: SearchService
    @EnvironmentObject private var exportService: ExportService
    @Environment(\.managedObjectContext) private var viewContext
    
    @StateObject private var viewModeManager = ViewModeManager()
    @State private var settingsWindowController: SettingsWindowController?
    @State private var selectedItems: Set<ContentItem> = []
    @State private var showingExportSheet = false
    
    var body: some View {
        NavigationSplitView {
            SidebarView(viewModeManager: viewModeManager)
                .navigationSplitViewColumnWidth(min: 200, ideal: 250, max: 300)
        } detail: {
            ContentListView(selectedItems: $selectedItems)
        }
        .toolbar {
            ToolbarContent()
        }
        .sheet(isPresented: $showingExportSheet) {
            ExportSheet(selectedItems: Array(selectedItems))
        }
    }
    
    @ToolbarContentBuilder
    private func ToolbarContent() -> some ToolbarContent {
        ToolbarItemGroup(placement: .primaryAction) {
            Button(action: addNewContent) {
                Image(systemName: "plus")
            }
            .help("添加新内容")
            
            Button(action: { showingExportSheet = true }) {
                Image(systemName: "square.and.arrow.up")
            }
            .help("导出内容")
            .disabled(selectedItems.isEmpty)
            
            Button(action: showSettings) {
                Image(systemName: "gear")
            }
            .help("设置")
        }
    }
    
    private func addNewContent() {
        // 创建一个简单的文本内容
        Task {
            do {
                let contentData = ContentData(
                    id: UUID(),
                    title: "新建文本",
                    content: "在这里输入文本内容...",
                    contentType: .text,
                    data: nil,
                    filePath: nil,
                    fileName: nil,
                    fileSize: 0,
                    mimeType: nil,
                    notes: nil,
                    tags: [],
                    createdAt: Date(),
                    updatedAt: Date(),
                    expiresAt: nil,
                    isPermanent: false
                )
                _ = try await contentService.addContent(contentData)
            } catch {
                NSLog("添加内容失败: \(error)")
            }
        }
    }
    
    private func showSettings() {
        if settingsWindowController == nil {
            settingsWindowController = SettingsWindowController()
        }
        settingsWindowController?.showWindow()
    }
}

struct SidebarView: View {
    @EnvironmentObject private var appState: AppState
    @ObservedObject var viewModeManager: ViewModeManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Search Section
            VStack(alignment: .leading, spacing: 8) {
                Text("搜索")
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                SearchField(text: $appState.searchText)
            }
            
            // View Mode Section
            VStack(alignment: .leading, spacing: 8) {
                Text("视图模式")
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                ViewModeSelector(viewModeManager: viewModeManager)
            }
            
            // Tags Section
            VStack(alignment: .leading, spacing: 8) {
                Text("标签")
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                TagFilterView(selectedTags: $appState.selectedTags)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
}

struct SearchField: View {
    @Binding var text: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索内容...", text: $text)
                .textFieldStyle(.roundedBorder)
        }
    }
}

// ViewModeSelector 已在 ViewModeManager.swift 中定义

struct TagFilterView: View {
    @Binding var selectedTags: Set<String>
    
    // 从Core Data获取标签
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Tag.name, ascending: true)],
        animation: .default
    )
    private var availableTagEntities: FetchedResults<Tag>
    
    private var availableTags: [String] {
        return availableTagEntities.compactMap { $0.name }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            ForEach(availableTags, id: \.self) { tag in
                Button(action: {
                    if selectedTags.contains(tag) {
                        selectedTags.remove(tag)
                    } else {
                        selectedTags.insert(tag)
                    }
                }) {
                    HStack {
                        Image(systemName: selectedTags.contains(tag) ? "checkmark.circle.fill" : "circle")
                        Text(tag)
                        Spacer()
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                }
                .buttonStyle(.plain)
                .foregroundColor(selectedTags.contains(tag) ? .accentColor : .primary)
            }
        }
    }
}

struct ContentListView: View {
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var contentService: ContentService
    @EnvironmentObject private var searchService: SearchService
    @EnvironmentObject private var pasteboardMonitor: PasteboardMonitor
    @Binding var selectedItems: Set<ContentItem>
    
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)],
        animation: .default
    )
    private var contentItems: FetchedResults<ContentItem>
    
    @State private var filteredItems: [ContentItem] = []
    @State private var showingEditSheet = false
    @State private var editingItem: ContentItem?
    
    var body: some View {
        VStack {
            if filteredItems.isEmpty && !appState.searchText.isEmpty {
                EmptySearchView()
            } else if filteredItems.isEmpty {
                EmptyContentView()
            } else {
                contentListView
            }
        }
        .navigationTitle("TempBox")
        .onChange(of: appState.searchText) { _, newValue in
            updateFilteredItems()
        }
        .onChange(of: appState.selectedTags) { _, _ in
            updateFilteredItems()
        }
        .onChange(of: contentItems.count) { _, _ in
            updateFilteredItems()
        }
        .onAppear {
            updateFilteredItems()
        }
        .sheet(isPresented: $showingEditSheet) {
            if let item = editingItem {
                ContentEditView(item: item)
            }
        }
        .contentDropHandler(
            contentService: contentService,
            pasteboardMonitor: pasteboardMonitor
        )
    }
    
    private var contentListView: some View {
        List(groupedItems, id: \.key, selection: $selectedItems) { group in
            if appState.selectedViewMode == .type {
                Section(group.key) {
                    ForEach(group.value, id: \.id) { item in
                        ContentItemRow(item: item) {
                            editingItem = item
                            showingEditSheet = true
                        }
                        .tag(item)
                        .contextMenu {
                            ContentItemContextMenu(item: item) {
                                editingItem = item
                                showingEditSheet = true
                            }
                        }
                    }
                }
            } else {
                ForEach(group.value, id: \.id) { item in
                    ContentItemRow(item: item) {
                        editingItem = item
                        showingEditSheet = true
                    }
                    .tag(item)
                    .contextMenu {
                        ContentItemContextMenu(item: item) {
                            editingItem = item
                            showingEditSheet = true
                        }
                    }
                }
            }
        }
        .listStyle(.inset)
    }
    
    private var groupedItems: [(key: String, value: [ContentItem])] {
        switch appState.selectedViewMode {
        case .timeline:
            return [("", filteredItems)]
        case .type:
            let grouped = Dictionary(grouping: filteredItems) { $0.contentTypeEnum.displayName }
            return grouped.sorted { $0.key < $1.key }
        }
    }
    
    private func updateFilteredItems() {
        let items = Array(contentItems)
        
        if appState.searchText.isEmpty && appState.selectedTags.isEmpty {
            filteredItems = items
        } else {
            filteredItems = searchService.searchContent(query: appState.searchText, in: items)
            
            if !appState.selectedTags.isEmpty {
                filteredItems = filteredItems.filter { item in
                    let itemTags = Set(item.tagNames)
                    return !appState.selectedTags.isDisjoint(with: itemTags)
                }
            }
        }
    }
}

struct ContentItemRow: View {
    let item: ContentItem
    let onEdit: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // Content type icon
            Image(systemName: item.contentTypeEnum.systemImage)
                .foregroundColor(Color(tagColor: item.contentTypeEnum.color))
                .frame(width: 20, height: 20)
            
            VStack(alignment: .leading, spacing: 4) {
                // Title
                Text(item.displayTitle)
                    .font(.headline)
                    .lineLimit(1)
                
                // Content preview
                if let content = item.content, !content.isEmpty {
                    Text(content)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                // Metadata
                HStack {
                    Text(item.createdAt ?? Date(), style: .relative)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    if !item.tagNames.isEmpty {
                        Spacer()
                        HStack(spacing: 4) {
                            ForEach(item.tagNames.prefix(3), id: \.self) { tagName in
                                Text(tagName)
                                    .font(.caption2)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.accentColor.opacity(0.2))
                                    .cornerRadius(8)
                            }
                            
                            if item.tagNames.count > 3 {
                                Text("+\(item.tagNames.count - 3)")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text(item.formattedFileSize)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                if let expiresAt = item.expiresAt {
                    Text("过期: \(expiresAt, style: .relative)")
                        .font(.caption2)
                        .foregroundColor(item.isExpired ? .red : .orange)
                }
            }
        }
        .padding(.vertical, 4)
    }
}

struct EmptyContentView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "tray")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("暂无内容")
                .font(.title2)
                .foregroundColor(.secondary)
            
            Text("使用快捷键或拖拽文件来添加内容")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct EmptySearchView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("未找到匹配内容")
                .font(.title2)
                .foregroundColor(.secondary)
            
            Text("尝试使用不同的搜索词或标签")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    MainContentView()
        .environmentObject(AppState())
        .environmentObject(ContentService())
        .environmentObject(SearchService())
        .environmentObject(ExportService(contentService: ContentService()))
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}

// MARK: - Content Item Context Menu
struct ContentItemContextMenu: View {
    let item: ContentItem
    let onEdit: () -> Void
    @EnvironmentObject private var contentService: ContentService
    @EnvironmentObject private var exportService: ExportService
    
    var body: some View {
        Button("编辑") {
            onEdit()
        }
        
        Button("导出") {
            exportSingleItem()
        }
        
        Divider()
        
        Button("删除", role: .destructive) {
            deleteItem()
        }
    }
    
    private func exportSingleItem() {
        guard let url = exportService.showSingleItemExportDialog(for: item) else {
            return
        }
        
        Task {
            do {
                try await exportService.exportSingleItem(item, to: url)
            } catch {
                NSLog("Export failed: \(error)")
            }
        }
    }
    
    private func deleteItem() {
        Task {
            do {
                try await contentService.deleteContent(item)
            } catch {
                NSLog("Delete failed: \(error)")
            }
        }
    }
}