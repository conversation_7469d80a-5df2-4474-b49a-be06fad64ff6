import SwiftUI
import AppKit
import UniformTypeIdentifiers

// MARK: - Export Sheet
struct ExportSheet: View {
    let selectedItems: [ContentItem]
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var batchService: BatchService
    @EnvironmentObject private var exportService: ExportService
    
    @State private var exportFormat: ExportFormat = .json
    @State private var includeFiles = true
    @State private var includeMetadata = true
    @State private var isExporting = false
    @State private var exportProgress = 0.0
    
    enum ExportFormat: String, CaseIterable {
        case json = "JSON"
        case csv = "CSV"
        case txt = "文本"
        case zip = "ZIP压缩包"
        
        var fileExtension: String {
            switch self {
            case .json: return "json"
            case .csv: return "csv"
            case .txt: return "txt"
            case .zip: return "zip"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("导出内容")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                // 导出范围
                VStack(alignment: .leading, spacing: 8) {
                    Text("导出范围")
                        .font(.headline)
                    
                    if selectedItems.isEmpty {
                        Text("导出当前批次的所有内容")
                            .foregroundColor(.secondary)
                    } else {
                        Text("导出选中的 \(selectedItems.count) 个项目")
                            .foregroundColor(.secondary)
                    }
                }
                
                // 导出格式
                VStack(alignment: .leading, spacing: 8) {
                    Text("导出格式")
                        .font(.headline)
                    
                    Picker("格式", selection: $exportFormat) {
                        ForEach(ExportFormat.allCases, id: \.self) { format in
                            Text(format.rawValue).tag(format)
                        }
                    }
                    .pickerStyle(.segmented)
                }
                
                // 导出选项
                VStack(alignment: .leading, spacing: 8) {
                    Text("导出选项")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Toggle("包含文件", isOn: $includeFiles)
                        Toggle("包含元数据", isOn: $includeMetadata)
                    }
                }
                
                if isExporting {
                    VStack(spacing: 8) {
                        ProgressView("正在导出...", value: exportProgress, total: 1.0)
                            .progressViewStyle(LinearProgressViewStyle())
                        
                        Text("\(Int(exportProgress * 100))%")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 操作按钮
                HStack(spacing: 12) {
                    Button("取消") {
                        dismiss()
                    }
                    .keyboardShortcut(.escape)
                    
                    Spacer()
                    
                    Button("导出") {
                        startExport()
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(isExporting)
                    .keyboardShortcut(.return)
                }
            }
            .padding()
            .navigationTitle("导出内容")
        }
        .frame(width: 500, height: 400)
    }
    
    private func startExport() {
        let panel = NSSavePanel()
        panel.nameFieldStringValue = generateFileName()
        panel.allowedContentTypes = [.init(filenameExtension: exportFormat.fileExtension)!]
        
        if panel.runModal() == .OK, let url = panel.url {
            isExporting = true
            
            Task {
                do {
                    let itemsToExport = selectedItems.isEmpty ? getAllBatchItems() : selectedItems
                    
                    switch exportFormat {
                    case .json:
                        try await exportService.exportAsMarkdown(itemsToExport, to: url)
                    case .csv:
                        try await exportService.exportAsMarkdown(itemsToExport, to: url)
                    case .txt:
                        try await exportService.exportAsMarkdown(itemsToExport, to: url)
                    case .zip:
                        try await exportService.exportAsZip(itemsToExport, to: url)
                    }
                    
                    await MainActor.run {
                        isExporting = false
                        dismiss()
                        
                        // 显示成功通知 (使用现代API)
                        if #available(macOS 11.0, *) {
                            // 使用UserNotifications框架
                            print("导出完成: \(url.lastPathComponent)")
                        }
                    }
                } catch {
                    await MainActor.run {
                        isExporting = false
                        NSLog("导出失败: \(error)")
                    }
                }
            }
        }
    }
    
    private func generateFileName() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        let timestamp = formatter.string(from: Date())
        
        if selectedItems.isEmpty {
            let batchName = batchService.currentBatch?.value(forKey: "name") as? String ?? "批次"
            return "\(batchName)_\(timestamp).\(exportFormat.fileExtension)"
        } else {
            return "选中内容_\(timestamp).\(exportFormat.fileExtension)"
        }
    }
    
    private func getAllBatchItems() -> [ContentItem] {
        guard let currentBatch = batchService.currentBatch,
              let items = currentBatch.value(forKey: "contentItems") as? NSSet else {
            return []
        }
        
        return items.compactMap { $0 as? ContentItem }
    }
}

#Preview {
    let contentService = ContentService()
    return ExportSheet(selectedItems: [])
        .environmentObject(BatchService())
        .environmentObject(ExportService(contentService: contentService))
}