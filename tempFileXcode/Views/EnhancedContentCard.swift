import SwiftUI
import AppKit

// MARK: - 增强版内容卡片（支持多选、文件类型区分、美观选中样式）
struct EnhancedContentCard: View {
    let item: ContentItem
    let isSelected: Bool
    let isMultiSelectMode: Bool
    let onSelect: () -> Void
    let onToggleSelect: () -> Void
    let onEdit: () -> Void
    let onQuickLook: () -> Void
    
    @EnvironmentObject private var contentService: ContentService
    @State private var isHovered = false
    @State private var dragOffset = CGSize.zero
    
    // 文件类型管理器
    private let fileTypeManager = FileTypeManager.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 顶部栏：选择框和快速操作
            topBar
            
            // 主要内容区域
            mainContent
            
            // 底部信息栏
            bottomInfo
        }
        .background(cardBackground)
        .overlay(selectionOverlay)
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .offset(dragOffset)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isSelected)
        .animation(.easeInOut(duration: 0.2), value: isHovered)
        .onHover { hovering in
            isHovered = hovering
        }
        .onTapGesture {
            if isMultiSelectMode {
                onToggleSelect()
            } else {
                onSelect()
            }
        }
        .onTapGesture(count: 2) {
            openFileWithRecommendedApp()
        }
        .contextMenu {
            contextMenuItems
        }
        .gesture(
            DragGesture()
                .onChanged { value in
                    dragOffset = CGSize(width: value.translation.width * 0.1, height: value.translation.height * 0.1)
                }
                .onEnded { _ in
                    withAnimation(.spring()) {
                        dragOffset = .zero
                    }
                }
        )
    }
    
    // MARK: - 顶部栏
    @ViewBuilder
    private var topBar: some View {
        HStack {
            // 多选模式下的选择框
            if isMultiSelectMode {
                Button(action: onToggleSelect) {
                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(isSelected ? .accentColor : .secondary)
                        .font(.system(size: 16))
                }
                .buttonStyle(.plain)
                .transition(.scale.combined(with: .opacity))
            }
            
            // 文件类型图标
            fileTypeIcon
            
            Spacer()
            
            // 快速操作按钮
            if isHovered || isSelected {
                quickActionButtons
                    .transition(.move(edge: .trailing).combined(with: .opacity))
            }
        }
        .padding(.horizontal, 12)
        .padding(.top, 12)
    }
    
    // MARK: - 文件类型图标
    @ViewBuilder
    private var fileTypeIcon: some View {
        let fileCategory = getFileCategory()
        
        ZStack {
            // 背景圆圈
            Circle()
                .fill(fileCategory.color.opacity(0.15))
                .frame(width: 32, height: 32)
            
            // 图标
            Image(systemName: fileCategory.systemImage)
                .foregroundColor(fileCategory.color)
                .font(.system(size: 16, weight: .medium))
        }
        .overlay(
            // 文件扩展名标签（小文件）
            Group {
                if !getFileExtension().isEmpty {
                    Text(getFileExtension().uppercased())
                        .font(.system(size: 8, weight: .bold))
                        .foregroundColor(fileCategory.color)
                        .padding(.horizontal, 4)
                        .padding(.vertical, 1)
                        .background(
                            Capsule()
                                .fill(fileCategory.color.opacity(0.2))
                        )
                        .offset(x: 12, y: -12)
                } else {
                    EmptyView()
                }
            }
        )
    }
    
    // MARK: - 快速操作按钮
    @ViewBuilder
    private var quickActionButtons: some View {
        HStack(spacing: 6) {
            // 快速预览
            QuickActionButton(icon: "eye", title: "预览", action: onQuickLook)
            .help("空格预览 (Space)")
            
            // 编辑
            QuickActionButton(icon: "pencil", title: "编辑", action: onEdit)
            .help("编辑")
            
            // 用推荐应用打开
            QuickActionButton(icon: "arrow.up.right.square", title: "打开", action: openFileWithRecommendedApp)
            .help("用推荐应用打开")
            
            // 在Finder中显示
            QuickActionButton(icon: "folder", title: "显示", action: showInFinder)
            .help("在Finder中显示")
        }
    }
    
    // MARK: - 主要内容区域
    @ViewBuilder
    private var mainContent: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 内容预览
            contentPreview
                .frame(maxHeight: 120)
                .clipped()
            
            // 标题
            Text(item.displayTitle)
                .font(.system(size: 14, weight: .medium))
                .lineLimit(2)
                .multilineTextAlignment(.leading)
                .foregroundColor(.primary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
    }
    
    // MARK: - 内容预览
    @ViewBuilder
    private var contentPreview: some View {
        let fileCategory = getFileCategory()
        
        switch fileCategory {
        case .text, .code, .document:
            textPreview
        case .image:
            imagePreview
        case .data:
            dataPreview
        default:
            genericPreview
        }
    }
    
    @ViewBuilder
    private var textPreview: some View {
        if let content = item.content, !content.isEmpty {
            ScrollView {
                Text(content)
                    .font(.system(size: 11, design: .monospaced))
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(8)
            }
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color(NSColor.textBackgroundColor))
            )
        } else {
            emptyContentPlaceholder
        }
    }
    
    @ViewBuilder
    private var imagePreview: some View {
        if let data = try? item.loadFileData(), let nsImage = NSImage(data: data) {
            Image(nsImage: nsImage)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(maxHeight: 100)
                .cornerRadius(6)
        } else {
            imagePlaceholder
        }
    }
    
    @ViewBuilder
    private var dataPreview: some View {
        VStack(spacing: 4) {
            Image(systemName: "tablecells")
                .font(.system(size: 24))
                .foregroundColor(.secondary)
            
            Text("数据文件")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: 80)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color(NSColor.controlBackgroundColor))
        )
    }
    
    @ViewBuilder
    private var genericPreview: some View {
        VStack(spacing: 4) {
            Image(systemName: getFileCategory().systemImage)
                .font(.system(size: 24))
                .foregroundColor(getFileCategory().color)
            
            Text(getFileCategory().displayName)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: 80)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(getFileCategory().color.opacity(0.1))
        )
    }
    
    @ViewBuilder
    private var emptyContentPlaceholder: some View {
        VStack(spacing: 4) {
            Image(systemName: "doc.text")
                .font(.system(size: 20))
                .foregroundColor(.secondary)
            
            Text("无预览内容")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: 80)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color(NSColor.controlBackgroundColor))
        )
    }
    
    @ViewBuilder
    private var imagePlaceholder: some View {
        VStack(spacing: 4) {
            Image(systemName: "photo")
                .font(.system(size: 20))
                .foregroundColor(.secondary)
            
            Text("图片文件")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: 80)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.purple.opacity(0.1))
        )
    }
    
    // MARK: - 底部信息栏
    @ViewBuilder
    private var bottomInfo: some View {
        HStack {
            // 文件类型标签
            Text(getFileCategory().displayName)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(getFileCategory().color)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(
                    Capsule()
                        .fill(getFileCategory().color.opacity(0.15))
                )
            
            Spacer()
            
            // 文件大小
            Text(item.formattedFileSize)
                .font(.system(size: 10))
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 12)
        .padding(.bottom, 8)
        
        // 创建时间
        HStack {
            Image(systemName: "clock")
                .font(.system(size: 9))
                .foregroundColor(.secondary)
            
            Text(item.formattedCreatedDate)
                .font(.system(size: 10))
                .foregroundColor(.secondary)
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.bottom, 12)
    }
    
    // MARK: - 卡片背景
    @ViewBuilder
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(
                isSelected ? 
                Color.accentColor.opacity(0.1) : 
                Color(NSColor.controlBackgroundColor)
            )
            .shadow(
                color: isSelected ? Color.accentColor.opacity(0.3) : Color.black.opacity(0.1),
                radius: isSelected ? 8 : 2,
                x: 0,
                y: isSelected ? 4 : 1
            )
    }
    
    // MARK: - 选中覆盖层
    @ViewBuilder
    private var selectionOverlay: some View {
        if isSelected {
            RoundedRectangle(cornerRadius: 12)
                .strokeBorder(
                    LinearGradient(
                        colors: [Color.accentColor, Color.accentColor.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 2
                )
        } else if isHovered {
            RoundedRectangle(cornerRadius: 12)
                .strokeBorder(Color.accentColor.opacity(0.3), lineWidth: 1)
        }
    }
    
    // MARK: - 上下文菜单
    @ViewBuilder
    private var contextMenuItems: some View {
        Group {
            Button("快速预览") {
                onQuickLook()
            }
            
            Button("编辑") {
                onEdit()
            }
            
            Divider()
            
            Menu("打开方式") {
                ForEach(getRecommendedApps(), id: \.bundleIdentifier) { app in
                    Button(app.name) {
                        openFileWith(app)
                    }
                }
            }
            
            Divider()
            
            Button("在Finder中显示") {
                showInFinder()
            }
            
            Button("复制文件路径") {
                copyFilePath()
            }
            
            Divider()
            
            Button("删除", role: .destructive) {
                deleteItem()
            }
        }
    }
    
    // MARK: - 辅助方法
    
    private func getFileCategory() -> FileTypeManager.FileCategory {
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            return fileTypeManager.identifyFileType(for: url)
        }
        return .unknown
    }
    
    private func getRecommendedApps() -> [FileTypeManager.AppInfo] {
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            return fileTypeManager.getRecommendedApps(for: url)
        }
        return []
    }
    
    private func openFileWithRecommendedApp() {
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            let apps = fileTypeManager.getRecommendedApps(for: url)
            
            if let defaultApp = apps.first(where: { $0.isDefault }) {
                _ = fileTypeManager.openFile(url, with: defaultApp)
            } else {
                _ = fileTypeManager.openFileWithDefault(url)
            }
        }
    }
    
    private func openFileWith(_ app: FileTypeManager.AppInfo) {
        if let filePath = item.filePath {
            let url = URL(fileURLWithPath: filePath)
            _ = fileTypeManager.openFile(url, with: app)
        }
    }
    
    private func showInFinder() {
        if let filePath = item.filePath {
            NSWorkspace.shared.selectFile(filePath, inFileViewerRootedAtPath: "")
        }
    }
    
    private func copyFilePath() {
        if let filePath = item.filePath {
            NSPasteboard.general.clearContents()
            NSPasteboard.general.setString(filePath, forType: .string)
        }
    }
    
    private func deleteItem() {
        // TODO: 实现删除功能
        print("删除项目: \(item.displayTitle)")
    }
}

// MARK: - 快速操作按钮组件
struct QuickActionButton: View {
    let icon: String
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(.accentColor)
                .frame(width: 20, height: 20)
                .background(
                    Circle()
                        .fill(Color.accentColor.opacity(0.15))
                )
        }
        .buttonStyle(.plain)
        .scaleEffect(1.0)
        .animation(.easeInOut(duration: 0.1), value: false)
    }
}

// MARK: - Helper Methods
extension EnhancedContentCard {
    private func getFileExtension() -> String {
        if let filePath = item.filePath {
            return URL(fileURLWithPath: filePath).pathExtension
        }
        return ""
    }
    
    private func isDocumentFile() -> Bool {
        guard let filePath = item.filePath else { return false }
        let fileExtension = URL(fileURLWithPath: filePath).pathExtension.lowercased()
        return ["pdf"].contains(fileExtension)
    }
}

