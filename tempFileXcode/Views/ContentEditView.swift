import SwiftUI
import AppKit

// MARK: - Content Edit View
struct ContentEditView: View {
    let item: ContentItem
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var contentService: ContentService
    
    @State private var editedTitle: String = ""
    @State private var editedContent: String = ""
    @State private var editedTags: String = ""
    @State private var isSaving = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("编辑内容")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                // 编辑表单
                VStack(alignment: .leading, spacing: 16) {
                    // 标题编辑
                    VStack(alignment: .leading, spacing: 8) {
                        Text("标题")
                            .font(.headline)
                        
                        TextField("输入标题", text: $editedTitle)
                            .textFieldStyle(.roundedBorder)
                    }
                    
                    // 内容编辑（仅对文本类型）
                    if item.contentTypeEnum == .text {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("内容")
                                .font(.headline)
                            
                            TextField("输入内容", text: $editedContent, axis: .vertical)
                                .textFieldStyle(.roundedBorder)
                                .lineLimit(8...15)
                        }
                    }
                    
                    // 标签编辑
                    VStack(alignment: .leading, spacing: 8) {
                        Text("标签")
                            .font(.headline)
                        
                        TextField("用逗号分隔多个标签", text: $editedTags)
                            .textFieldStyle(.roundedBorder)
                        
                        Text("例如：工作, 重要, 待办")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // 文件信息（只读）
                    if item.contentTypeEnum != .text {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("文件信息")
                                .font(.headline)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                if let fileName = item.fileName {
                                    HStack {
                                        Text("文件名:")
                                            .foregroundColor(.secondary)
                                        Text(fileName)
                                    }
                                    .font(.caption)
                                }
                                
                                HStack {
                                    Text("文件大小:")
                                        .foregroundColor(.secondary)
                                    Text(item.formattedFileSize)
                                }
                                .font(.caption)
                                
                                if let filePath = item.filePath {
                                    HStack {
                                        Text("路径:")
                                            .foregroundColor(.secondary)
                                        Text(filePath)
                                            .lineLimit(2)
                                    }
                                    .font(.caption)
                                }
                            }
                            .padding(8)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                    }
                }
                
                Spacer()
                
                // 操作按钮
                HStack(spacing: 12) {
                    Button("取消") {
                        dismiss()
                    }
                    .keyboardShortcut(.escape)
                    
                    Spacer()
                    
                    Button("保存") {
                        saveChanges()
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(isSaving || editedTitle.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                    .keyboardShortcut(.return)
                    
                    if isSaving {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }
            }
            .padding()
            .navigationTitle("编辑内容")
        }
        .frame(width: 500, height: 600)
        .onAppear {
            loadCurrentValues()
        }
        .alert("保存失败", isPresented: $showingError) {
            Button("确定") { }
        } message: {
            Text(errorMessage)
        }
    }
    
    private func loadCurrentValues() {
        editedTitle = item.displayTitle
        editedContent = item.content ?? ""
        editedTags = item.tagNames.joined(separator: ", ")
    }
    
    private func saveChanges() {
        isSaving = true
        
        Task {
            do {
                // 更新标题
                if item.contentTypeEnum == .text {
                    item.setValue(editedTitle.trimmingCharacters(in: .whitespacesAndNewlines), forKey: "title")
                } else {
                    // 对于文件类型，可能需要重命名文件
                    item.setValue(editedTitle.trimmingCharacters(in: .whitespacesAndNewlines), forKey: "customTitle")
                }
                
                // 更新内容（仅文本类型）
                if item.contentTypeEnum == .text {
                    item.setValue(editedContent, forKey: "content")
                }
                
                // 更新标签
                let tagNames = editedTags
                    .split(separator: ",")
                    .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                    .filter { !$0.isEmpty }
                
                // 手动更新标签
                if let currentTags = item.tags?.allObjects as? [Tag] {
                    for tag in currentTags {
                        item.removeFromTags(tag)
                    }
                }
                
                if let context = item.managedObjectContext {
                    for tagName in tagNames {
                        let tag = Tag.findOrCreate(name: tagName, in: context)
                        item.addToTags(tag)
                    }
                    try context.save()
                }
                
                await MainActor.run {
                    isSaving = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isSaving = false
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }
    }
}

#Preview {
    ContentEditView(item: ContentItem())
        .environmentObject(ContentService())
}