import SwiftUI
import CoreData
import UniformTypeIdentifiers
import QuickLook

// MARK: - 视图模式枚举
enum ContentViewMode: String, CaseIterable {
    case grid = "grid"
    case list = "list"
    case thumbnail = "thumbnail"
    
    var displayName: String {
        switch self {
        case .grid: return "网格视图"
        case .list: return "列表视图"
        case .thumbnail: return "缩略图视图"
        }
    }
    
    var systemImage: String {
        switch self {
        case .grid: return "square.grid.2x2"
        case .list: return "list.bullet"
        case .thumbnail: return "photo.on.rectangle.angled"
        }
    }
}

// MARK: - 现代化内容视图
struct ModernContentView: View {
    let batch: NSManagedObject
    @Binding var selectedItems: Set<ContentItem>
    let onItemEdit: (ContentItem) -> Void
    
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var searchService: SearchService
    @EnvironmentObject private var contentService: ContentService
    
    @FetchRequest private var contentItems: FetchedResults<ContentItem>
    @State private var filteredItems: [ContentItem] = []
    @State private var viewMode: ContentViewMode = .grid
    @State private var sortOrder: SortOrder = .dateDescending
    @State private var quickLookURL: URL?
    @State private var showingQuickLook = false
    
    enum SortOrder: String, CaseIterable {
        case dateDescending = "date_desc"
        case dateAscending = "date_asc"
        case nameAscending = "name_asc"
        case nameDescending = "name_desc"
        case sizeAscending = "size_asc"
        case sizeDescending = "size_desc"
        
        var displayName: String {
            switch self {
            case .dateDescending: return "最新优先"
            case .dateAscending: return "最旧优先"
            case .nameAscending: return "名称 A-Z"
            case .nameDescending: return "名称 Z-A"
            case .sizeAscending: return "大小递增"
            case .sizeDescending: return "大小递减"
            }
        }
    }
    
    init(batch: NSManagedObject, selectedItems: Binding<Set<ContentItem>>, onItemEdit: @escaping (ContentItem) -> Void) {
        self.batch = batch
        self._selectedItems = selectedItems
        self.onItemEdit = onItemEdit
        
        let request: NSFetchRequest<ContentItem> = ContentItem.fetchRequest()
        request.predicate = NSPredicate(format: "batch == %@", batch)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \ContentItem.createdAt, ascending: false)]
        
        self._contentItems = FetchRequest(fetchRequest: request)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 工具栏
            contentToolbar
            
            Divider()
            
            // 内容区域
            contentArea
        }
        .onAppear {
            updateFilteredItems()
        }
        .onChange(of: appState.searchText) { _, _ in
            updateFilteredItems()
        }
        .onChange(of: appState.selectedTags) { _, _ in
            updateFilteredItems()
        }
        .onChange(of: contentItems.count) { _, _ in
            updateFilteredItems()
        }
        .onChange(of: sortOrder) { _, _ in
            updateFilteredItems()
        }
        .quickLookPreview($quickLookURL)
        .onKeyPress(.space) {
            if let firstSelected = selectedItems.first,
               let filePath = firstSelected.filePath {
                quickLookURL = URL(fileURLWithPath: filePath)
                return .handled
            }
            return .ignored
        }
    }
    
    // MARK: - 工具栏
    private var contentToolbar: some View {
        HStack {
            // 左侧：项目计数
            HStack(spacing: 8) {
                Text("\(filteredItems.count) 个项目")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 右侧：视图控制
            HStack(spacing: 12) {
                // 排序菜单
                Menu {
                    ForEach(SortOrder.allCases, id: \.self) { order in
                        Button(action: {
                            sortOrder = order
                        }) {
                            HStack {
                                Text(order.displayName)
                                if sortOrder == order {
                                    Image(systemName: "checkmark")
                                }
                            }
                        }
                    }
                } label: {
                    Image(systemName: "arrow.up.arrow.down")
                        .foregroundColor(.accentColor)
                }
                .menuStyle(.borderlessButton)
                .help("排序方式")
                
                // 视图模式切换
                HStack(spacing: 4) {
                    ForEach(ContentViewMode.allCases, id: \.self) { mode in
                        Button(action: {
                            viewMode = mode
                        }) {
                            Image(systemName: mode.systemImage)
                                .foregroundColor(viewMode == mode ? .accentColor : .secondary)
                                .font(.system(size: 14))
                        }
                        .buttonStyle(.plain)
                        .help(mode.displayName)
                    }
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    // MARK: - 内容区域
    @ViewBuilder
    private var contentArea: some View {
        if contentItems.isEmpty {
            emptyContentView
        } else if filteredItems.isEmpty && (!appState.searchText.isEmpty || !appState.selectedTags.isEmpty) {
            emptySearchView
        } else {
            switch viewMode {
            case .grid:
                gridView
            case .list:
                listView
            case .thumbnail:
                thumbnailView
            }
        }
    }
    
    // MARK: - 网格视图
    private var gridView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: 200), spacing: 12)
            ], spacing: 12) {
                ForEach(filteredItems, id: \.id) { item in
                    ModernContentCard(
                        item: item,
                        isSelected: false,
                        onSelect: { },
                        onEdit: { onItemEdit(item) },
                        onQuickLook: { showQuickLook(for: item) }
                    )
                }
            }
            .padding()
        }
    }
    
    // MARK: - 列表视图
    private var listView: some View {
        ScrollView {
            LazyVStack(spacing: 1) {
                ForEach(filteredItems, id: \.id) { item in
                    ModernContentRow(
                        item: item,
                        isSelected: false,
                        onSelect: { },
                        onEdit: { onItemEdit(item) },
                        onQuickLook: { showQuickLook(for: item) }
                    )
                }
            }
        }
    }
    
    // MARK: - 缩略图视图
    private var thumbnailView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: 120), spacing: 8)
            ], spacing: 8) {
                ForEach(filteredItems, id: \.id) { item in
                    ModernContentThumbnail(
                        item: item,
                        isSelected: false,
                        onSelect: { },
                        onEdit: { onItemEdit(item) },
                        onQuickLook: { showQuickLook(for: item) }
                    )
                }
            }
            .padding()
        }
    }
    
    // MARK: - 空状态视图
    private var emptyContentView: some View {
        VStack(spacing: 20) {
            Image(systemName: "doc.badge.plus")
                .font(.system(size: 48))
                .foregroundColor(.accentColor)
                .symbolEffect(.bounce, options: .repeat(.continuous))
            
            Text("批次为空")
                .font(.title2)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                Text("开始添加您的第一个内容项目")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                Text("支持拖拽文件、复制文本或使用快捷键")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .multilineTextAlignment(.center)
            
            HStack(spacing: 12) {
                ActionButton(
                    title: "选择文件",
                    action: selectFiles,
                    style: .primary
                )
                
                ActionButton(
                    title: "快速粘贴 ⌘⇧V",
                    action: quickPaste,
                    style: .secondary
                )
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(40)
    }
    
    private var emptySearchView: some View {
        VStack(spacing: 20) {
            Image(systemName: "magnifyingglass.circle")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            Text("未找到匹配内容")
                .font(.title2)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                Text("尝试以下操作：")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("• 检查搜索词的拼写")
                    Text("• 使用更简单的关键词")
                    Text("• 清除过滤条件")
                    Text("• 切换到其他批次")
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
            }
            
            ActionButton(
                title: "清除搜索和过滤",
                action: clearFilters,
                style: .secondary
            )
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(40)
    }
    
    // MARK: - 辅助方法
    private func toggleSelection(_ item: ContentItem) {
        if selectedItems.contains(item) {
            selectedItems.remove(item)
        } else {
            selectedItems.insert(item)
        }
    }
    
    private func showQuickLook(for item: ContentItem) {
        if let filePath = item.filePath {
            quickLookURL = URL(fileURLWithPath: filePath)
        }
    }
    
    private func selectFiles() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = true
        panel.canChooseDirectories = false
        panel.canChooseFiles = true
        
        if panel.runModal() == .OK {
            Task {
                for url in panel.urls {
                    do {
                        let contentData = try ContentData.fromFile(at: url)
                        _ = try await contentService.addContent(contentData, toBatch: batch)
                    } catch {
                        NSLog("Failed to add file: \(error)")
                    }
                }
            }
        }
    }
    
    private func quickPaste() {
        // TODO: 实现快速粘贴功能
    }
    
    private func clearFilters() {
        appState.searchText = ""
        appState.selectedTags.removeAll()
    }
    
    private func updateFilteredItems() {
        var items = Array(contentItems)
        
        // 应用搜索过滤
        if !appState.searchText.isEmpty {
            items = searchService.searchContent(query: appState.searchText, in: items)
        }
        
        // 应用标签过滤
        if !appState.selectedTags.isEmpty {
            items = items.filter { item in
                let itemTags = Set(item.tagNames)
                return !appState.selectedTags.isDisjoint(with: itemTags)
            }
        }
        
        // 应用排序
        items = sortItems(items)
        
        filteredItems = items
    }
    
    private func sortItems(_ items: [ContentItem]) -> [ContentItem] {
        switch sortOrder {
        case .dateDescending:
            return items.sorted { ($0.createdAt ?? Date.distantPast) > ($1.createdAt ?? Date.distantPast) }
        case .dateAscending:
            return items.sorted { ($0.createdAt ?? Date.distantPast) < ($1.createdAt ?? Date.distantPast) }
        case .nameAscending:
            return items.sorted { $0.displayTitle.localizedCaseInsensitiveCompare($1.displayTitle) == .orderedAscending }
        case .nameDescending:
            return items.sorted { $0.displayTitle.localizedCaseInsensitiveCompare($1.displayTitle) == .orderedDescending }
        case .sizeAscending:
            return items.sorted { $0.fileSize < $1.fileSize }
        case .sizeDescending:
            return items.sorted { $0.fileSize > $1.fileSize }
        }
    }
}