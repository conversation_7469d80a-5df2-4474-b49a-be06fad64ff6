import SwiftUI
import WebKit
import PDFKit
import QuickLook

// MARK: - Enhanced Preview View
struct EnhancedPreviewView: View {
    let item: ContentItem
    @StateObject private var previewService: FilePreviewService
    @State private var previewResult: PreviewResult?
    @State private var isLoading = true
    @State private var error: Error?
    
    init(item: ContentItem, contentService: ContentService) {
        self.item = item
        self._previewService = StateObject(wrappedValue: FilePreviewService(contentService: contentService))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 头部信息
            headerView
            
            Divider()
            
            // 预览内容
            if isLoading {
                loadingView
            } else if let error = error {
                errorView(error)
            } else if let result = previewResult {
                previewContentView(result)
            } else {
                unsupportedView
            }
        }
        .onAppear {
            loadPreview()
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: item.contentTypeEnum.systemImage)
                    .foregroundColor(Color(tagColor: item.contentTypeEnum.color))
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(item.displayTitle)
                        .font(.headline)
                        .lineLimit(2)
                    
                    HStack(spacing: 12) {
                        Text(item.contentTypeEnum.displayName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(item.formattedFileSize)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        if let createdAt = item.createdAt {
                            Text(createdAt, style: .date)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Spacer()
                
                // 预览类型标识
                if let result = previewResult {
                    Text(result.previewType.displayName)
                        .font(.caption2)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.accentColor.opacity(0.2))
                        .foregroundColor(.accentColor)
                        .cornerRadius(6)
                }
            }
            
            // 元数据信息
            if let result = previewResult, !result.metadata.isEmpty {
                metadataView(result.metadata)
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    private func metadataView(_ metadata: [String: Any]) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("文件信息")
                .font(.caption)
                .foregroundColor(.secondary)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                ForEach(Array(metadata.keys.prefix(6)), id: \.self) { key in
                    VStack(alignment: .leading, spacing: 2) {
                        Text(formatMetadataKey(key))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        Text(formatMetadataValue(metadata[key]))
                            .font(.caption)
                            .lineLimit(1)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.vertical, 2)
                }
            }
        }
        .padding(.top, 8)
    }
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("正在生成预览...")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func errorView(_ error: Error) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            Text("预览失败")
                .font(.title3)
                .foregroundColor(.secondary)
            
            Text(error.localizedDescription)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("重新尝试") {
                loadPreview()
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var unsupportedView: some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.questionmark")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("不支持预览")
                .font(.title3)
                .foregroundColor(.secondary)
            
            Text("此文件类型暂不支持预览")
                .font(.caption)
                .foregroundColor(.secondary)
            
            // 显示支持的格式
            supportedFormatsView
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var supportedFormatsView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("支持的格式:")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)
            
            let formats = previewService.getSupportedFormats()
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 6) {
                ForEach(formats.prefix(9)) { format in
                    Text(format.name)
                        .font(.caption2)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 3)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(4)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(8)
        .padding(.horizontal)
    }
    
    @ViewBuilder
    private func previewContentView(_ result: PreviewResult) -> some View {
        switch result.content {
        case .text(let text):
            TextPreviewView(text: text, previewType: result.previewType)
            
        case .image(let image):
            ImagePreviewView(image: image)
            
        case .pdf(let document):
            PDFPreviewView(document: document)
            
        case .html(let html):
            WebPreviewView(html: html)
            
        case .error(let message):
            errorView(PreviewError.customError(message))
        }
    }
    
    // MARK: - Helper Methods
    private func loadPreview() {
        isLoading = true
        error = nil
        previewResult = nil
        
        Task {
            do {
                let result = try await previewService.generatePreview(for: item)
                
                await MainActor.run {
                    self.previewResult = result
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.error = error
                    self.isLoading = false
                }
            }
        }
    }
    
    private func formatMetadataKey(_ key: String) -> String {
        switch key {
        case "size":
            return "尺寸"
        case "format":
            return "格式"
        case "fileSize":
            return "大小"
        case "pageCount":
            return "页数"
        case "length":
            return "长度"
        case "lines":
            return "行数"
        case "language":
            return "语言"
        case "isValid":
            return "有效"
        default:
            return key.capitalized
        }
    }
    
    private func formatMetadataValue(_ value: Any?) -> String {
        guard let value = value else { return "未知" }
        
        if let size = value as? CGSize {
            return "\(Int(size.width)) × \(Int(size.height))"
        } else if let number = value as? NSNumber {
            return number.stringValue
        } else if let bool = value as? Bool {
            return bool ? "是" : "否"
        } else {
            return String(describing: value)
        }
    }
}

// MARK: - Text Preview View
struct TextPreviewView: View {
    let text: String
    let previewType: PreviewType
    
    @State private var selectedText: String = ""
    @State private var searchText: String = ""
    @State private var showFullText: Bool = true
    
    var body: some View {
        VStack(spacing: 0) {
            // 搜索和控制栏
            if previewType == .text || previewType == .code || previewType == .json {
                controlBar
                Divider()
            }
            
            // 文本内容 - 使用ScrollView支持完整显示和滚动
            ScrollView([.horizontal, .vertical]) {
                VStack(alignment: .leading, spacing: 0) {
                    if previewType == .code {
                        codeTextView
                    } else {
                        plainTextView
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
            }
            .background(Color(NSColor.textBackgroundColor))
        }
    }
    
    private var controlBar: some View {
        HStack {
            // 搜索功能
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索文本...", text: $searchText)
                .textFieldStyle(.roundedBorder)
            
            if !searchText.isEmpty {
                Button("清除") {
                    searchText = ""
                }
                .buttonStyle(.plain)
                .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 文本统计信息
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(text.count) 个字符")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Text("\(text.components(separatedBy: .newlines).count) 行")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    private var plainTextView: some View {
        Text(displayText)
            .font(.system(.body, design: .default))
            .textSelection(.enabled)
            .frame(maxWidth: .infinity, alignment: .leading)
            .lineLimit(nil) // 移除行数限制
            .fixedSize(horizontal: false, vertical: true) // 确保垂直方向完整显示
    }
    
    private var codeTextView: some View {
        Text(displayText)
            .font(.system(.body, design: .monospaced))
            .textSelection(.enabled)
            .frame(maxWidth: .infinity, alignment: .leading)
            .lineLimit(nil) // 移除行数限制
            .fixedSize(horizontal: false, vertical: true) // 确保垂直方向完整显示
    }
    
    // 显示文本 - 完整显示，不再截断
    private var displayText: AttributedString {
        var attributedString = AttributedString(text)
        
        // 搜索高亮
        if !searchText.isEmpty {
            let lowercaseText = text.lowercased()
            let lowercaseSearch = searchText.lowercased()
            var searchStartIndex = lowercaseText.startIndex
            
            while let range = lowercaseText.range(of: lowercaseSearch, range: searchStartIndex..<lowercaseText.endIndex) {
                let nsRange = NSRange(range, in: text)
                if let attributedRange = Range<AttributedString.Index>(nsRange, in: attributedString) {
                    attributedString[attributedRange].backgroundColor = .yellow
                    attributedString[attributedRange].foregroundColor = .black
                }
                searchStartIndex = range.upperBound
            }
        }
        
        return attributedString
    }
    
}

// MARK: - Image Preview View
struct ImagePreviewView: View {
    let image: NSImage
    
    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastDragValue: CGSize = .zero
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView([.horizontal, .vertical]) {
                Image(nsImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .scaleEffect(scale)
                    .offset(offset)
                    .clipped()
                    .gesture(
                        SimultaneousGesture(
                            // 拖拽手势
                            DragGesture()
                                .onChanged { value in
                                    offset = CGSize(
                                        width: lastDragValue.width + value.translation.width,
                                        height: lastDragValue.height + value.translation.height
                                    )
                                }
                                .onEnded { _ in
                                    lastDragValue = offset
                                },
                            
                            // 缩放手势
                            MagnificationGesture()
                                .onChanged { value in
                                    scale = max(0.5, min(value, 5.0))
                                }
                        )
                    )
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .overlay(alignment: .bottomTrailing) {
            imageControls
        }
    }
    
    private var imageControls: some View {
        VStack(spacing: 8) {
            Button(action: { scale = min(scale * 1.2, 5.0) }) {
                Image(systemName: "plus.magnifyingglass")
            }
            
            Button(action: { scale = max(scale / 1.2, 0.5) }) {
                Image(systemName: "minus.magnifyingglass")
            }
            
            Button(action: resetView) {
                Image(systemName: "arrow.clockwise")
            }
        }
        .buttonStyle(.bordered)
        .background(.ultraThinMaterial)
        .cornerRadius(8)
        .padding()
    }
    
    private func resetView() {
        withAnimation(.easeInOut(duration: 0.3)) {
            scale = 1.0
            offset = .zero
            lastDragValue = .zero
        }
    }
}

// MARK: - PDF Preview View
struct PDFPreviewView: View {
    let document: PDFDocument
    
    @State private var currentPage: Int = 0
    
    var body: some View {
        VStack(spacing: 0) {
            // 页面控制
            if document.pageCount > 1 {
                pageControls
                Divider()
            }
            
            // PDF内容
            if let page = document.page(at: currentPage) {
                PDFPageView(page: page)
            } else {
                Text("无法显示PDF页面")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
    }
    
    private var pageControls: some View {
        HStack {
            Button(action: previousPage) {
                Image(systemName: "chevron.left")
            }
            .disabled(currentPage <= 0)
            
            Spacer()
            
            Text("第 \(currentPage + 1) 页，共 \(document.pageCount) 页")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Button(action: nextPage) {
                Image(systemName: "chevron.right")
            }
            .disabled(currentPage >= document.pageCount - 1)
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    private func previousPage() {
        if currentPage > 0 {
            currentPage -= 1
        }
    }
    
    private func nextPage() {
        if currentPage < document.pageCount - 1 {
            currentPage += 1
        }
    }
}

// MARK: - PDF Page View
struct PDFPageView: NSViewRepresentable {
    let page: PDFPage
    
    func makeNSView(context: Context) -> PDFView {
        let pdfView = PDFView()
        pdfView.document = PDFDocument()
        pdfView.document?.insert(page, at: 0)
        pdfView.autoScales = true
        pdfView.displayMode = .singlePage
        return pdfView
    }
    
    func updateNSView(_ nsView: PDFView, context: Context) {
        // 更新页面如果需要
    }
}

// MARK: - Web Preview View
struct WebPreviewView: View {
    let html: String
    
    var body: some View {
        WebView(html: html)
    }
}

// MARK: - Web View
struct WebView: NSViewRepresentable {
    let html: String
    
    func makeNSView(context: Context) -> WKWebView {
        let webView = WKWebView()
        webView.loadHTMLString(html, baseURL: nil)
        return webView
    }
    
    func updateNSView(_ nsView: WKWebView, context: Context) {
        nsView.loadHTMLString(html, baseURL: nil)
    }
}

// MARK: - Preview Error
enum PreviewError: LocalizedError {
    case customError(String)
    
    var errorDescription: String? {
        switch self {
        case .customError(let message):
            return message
        }
    }
}

#Preview {
    EnhancedPreviewView(item: ContentItem(), contentService: ContentService())
}