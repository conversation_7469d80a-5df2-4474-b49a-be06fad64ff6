import SwiftUI
import CoreData

// MARK: - 批次侧边栏视图
struct BatchSidebarView: View {
    @EnvironmentObject private var batchService: BatchService
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var searchService: SearchService
    
    @State private var searchText: String = ""
    @State private var showingNewBatchSheet = false
    @State private var newBatchName = ""
    @State private var newBatchNotes = ""
    @State private var selectedBatchForDeletion: NSManagedObject?
    @State private var showingDeleteAlert = false
    
    // 多选相关状态 - 使用ObjectID来标识选中的批次
    @State private var selectedBatchIDs: Set<NSManagedObjectID> = []
    @State private var isMultiSelectMode = false
    @State private var showingBatchOperationsMenu = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            headerView
            
            Divider()
            
            // 搜索栏
            searchView
            
            // 批次列表
            batchListView
            
            Spacer()
            
            // 底部操作区
            bottomActionsView
        }
        .background(Color(NSColor.controlBackgroundColor))
        .sheet(isPresented: $showingNewBatchSheet) {
            newBatchSheet
        }
        .alert("删除批次", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                if let batch = selectedBatchForDeletion {
                    deleteBatch(batch)
                }
            }
        } message: {
            if let batch = selectedBatchForDeletion {
                let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
                Text("确定要删除批次 \"\(batchName)\" 吗？此操作不可撤销。")
            }
        }
    }
    
    // MARK: - 标题栏
    private var headerView: some View {
        VStack(spacing: 8) {
            HStack {
                Text("批次管理")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                // 多选模式切换按钮
                Button(action: { toggleMultiSelectMode() }) {
                    Image(systemName: isMultiSelectMode ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(isMultiSelectMode ? .orange : .secondary)
                        .font(.system(size: 16))
                }
                .buttonStyle(.plain)
                .help(isMultiSelectMode ? "退出多选模式" : "进入多选模式")
                
                Button(action: { showingNewBatchSheet = true }) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.accentColor)
                        .font(.system(size: 18))
                }
                .buttonStyle(.plain)
                .help("新建批次")
            }
            
            // 多选模式工具栏
            if isMultiSelectMode {
                multiSelectToolbar
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
    
    // MARK: - 多选工具栏
    private var multiSelectToolbar: some View {
        HStack(spacing: 12) {
            // 全选/取消全选按钮
            Button(action: { toggleSelectAll() }) {
                HStack(spacing: 4) {
                    Image(systemName: isAllSelected ? "checkmark.square.fill" : "square")
                        .foregroundColor(isAllSelected ? .accentColor : .secondary)
                        .font(.system(size: 14))
                    
                    Text(isAllSelected ? "取消全选" : "全选")
                        .font(.caption)
                        .foregroundColor(.primary)
                }
            }
            .buttonStyle(.plain)
            
            // 选中数量显示
            if !selectedBatchIDs.isEmpty {
                Text("已选 \(selectedBatchIDs.count) 项")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.accentColor.opacity(0.1))
                    )
            }
            
            Spacer()
            
            // 批量操作菜单
            if !selectedBatchIDs.isEmpty {
                Menu {
                    Button("删除选中批次", role: .destructive) {
                        deleteSelectedBatches()
                    }
                    .disabled(selectedBatchIDs.isEmpty)
                } label: {
                    Image(systemName: "ellipsis.circle")
                        .foregroundColor(.accentColor)
                        .font(.system(size: 16))
                }
                .buttonStyle(.plain)
                .help("批量操作")
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.accentColor.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(Color.accentColor.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 搜索栏
    private var searchView: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
                .font(.system(size: 14))
            
            TextField("搜索批次...", text: $searchText)
                .textFieldStyle(.plain)
                .font(.system(size: 13))
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(NSColor.textBackgroundColor))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1)
                )
        )
        .padding(.horizontal, 16)
        .padding(.bottom, 12)
    }
    
    // MARK: - 批次列表
    private var batchListView: some View {
        ScrollView {
            LazyVStack(spacing: 2) {
                ForEach(filteredBatches, id: \.objectID) { batch in
                    BatchRowView(
                        batch: batch,
                        isSelected: batch == batchService.currentBatch,
                        isMultiSelectMode: isMultiSelectMode,
                        isMultiSelected: selectedBatchIDs.contains(batch.objectID),
                        onSelect: { 
                            if isMultiSelectMode {
                                toggleBatchSelection(batch)
                            } else {
                                batchService.setCurrentBatch(batch)
                            }
                        },
                        onDelete: { 
                            selectedBatchForDeletion = batch
                            showingDeleteAlert = true
                        }
                    )
                }
            }
            .padding(.horizontal, 8)
        }
    }
    
    // MARK: - 底部操作区
    private var bottomActionsView: some View {
        VStack(spacing: 12) {
            Divider()
            
            // 当前批次信息
            if let currentBatch = batchService.currentBatch {
                currentBatchInfo(currentBatch)
            }
            
            // 活跃批次信息
            if let activeBatch = batchService.activeBatch {
                activeBatchInfo(activeBatch)
            }
            
            // 快速操作按钮已移至顶部工具栏
        }
        .padding(16)
    }
    
    // MARK: - 当前批次信息
    private func currentBatchInfo(_ batch: NSManagedObject) -> some View {
        VStack(alignment: .leading, spacing: 6) {
            Text("当前批次")
                .font(.caption)
                .foregroundColor(.secondary)
            
            let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
            let itemCount = (batch.value(forKey: "contentItems") as? NSSet)?.count ?? 0
            
            VStack(alignment: .leading, spacing: 4) {
                Text(batchName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(2)
                
                Text("\(itemCount) 个项目")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.accentColor.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(Color.accentColor.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 活跃批次信息
    private func activeBatchInfo(_ batch: NSManagedObject) -> some View {
        VStack(alignment: .leading, spacing: 6) {
            Text("活跃批次 (拖拽目标)")
                .font(.caption)
                .foregroundColor(.orange)
            
            let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
            let itemCount = (batch.value(forKey: "contentItems") as? NSSet)?.count ?? 0
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: "star.fill")
                            .foregroundColor(.orange)
                            .font(.system(size: 12))
                        
                        Text(batchName)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .lineLimit(1)
                    }
                    
                    Text("\(itemCount) 个项目")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 切换活跃批次按钮
                Menu {
                    ForEach(batchService.batches, id: \.objectID) { otherBatch in
                        let otherBatchName = otherBatch.value(forKey: "name") as? String ?? "未命名批次"
                        let isCurrentActive = otherBatch == batchService.activeBatch
                        
                        Button(action: {
                            batchService.setActiveBatch(otherBatch)
                        }) {
                            HStack {
                                Text(otherBatchName)
                                if isCurrentActive {
                                    Image(systemName: "star.fill")
                                        .foregroundColor(.orange)
                                }
                            }
                        }
                        .disabled(isCurrentActive)
                    }
                } label: {
                    Image(systemName: "arrow.triangle.2.circlepath")
                        .foregroundColor(.orange)
                        .font(.system(size: 14))
                }
                .buttonStyle(.plain)
                .help("切换活跃批次")
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.orange.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(Color.orange.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - 新建批次表单
    private var newBatchSheet: some View {
        VStack(spacing: 0) {
            // 顶部标题区域
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("新建批次")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("创建一个新的内容管理批次")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Button(action: {
                    showingNewBatchSheet = false
                    resetForm()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                        .font(.system(size: 20))
                }
                .buttonStyle(.plain)
                .help("关闭")
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 20)
            .background(.regularMaterial)
            
            // 表单内容区域
            ScrollView {
                VStack(spacing: 24) {
                    // 批次名称输入
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "folder.fill")
                                .foregroundColor(.accentColor)
                                .font(.system(size: 16))
                            
                            Text("批次名称")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        
                        TextField("输入批次名称", text: $newBatchName)
                            .textFieldStyle(.plain)
                            .font(.system(size: 14))
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(NSColor.textBackgroundColor))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .strokeBorder(
                                                newBatchName.isEmpty ? Color.gray.opacity(0.3) : Color.accentColor.opacity(0.5),
                                                lineWidth: 1.5
                                            )
                                    )
                            )
                        
                        // 字符计数
                        HStack {
                            Spacer()
                            Text("\(newBatchName.count)/50")
                                .font(.caption2)
                                .foregroundColor(newBatchName.count > 50 ? .red : .secondary)
                        }
                    }
                    
                    // 批次描述输入
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "text.alignleft")
                                .foregroundColor(.accentColor)
                                .font(.system(size: 16))
                            
                            Text("批次描述")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            Spacer()
                            
                            Text("可选")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(
                                    Capsule()
                                        .fill(Color.secondary.opacity(0.1))
                                )
                        }
                        
                        TextField("为这个批次添加描述信息...", text: $newBatchNotes, axis: .vertical)
                            .textFieldStyle(.plain)
                            .font(.system(size: 14))
                            .lineLimit(3...6)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(NSColor.textBackgroundColor))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1.5)
                                    )
                            )
                        
                        // 字符计数
                        HStack {
                            Spacer()
                            Text("\(newBatchNotes.count)/200")
                                .font(.caption2)
                                .foregroundColor(newBatchNotes.count > 200 ? .red : .secondary)
                        }
                    }
                    
                    // 预设模板选项
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "doc.on.doc")
                                .foregroundColor(.accentColor)
                                .font(.system(size: 16))
                            
                            Text("快速模板")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        
                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: 12) {
                            ForEach(batchTemplates, id: \.name) { template in
                                Button(action: {
                                    applyTemplate(template)
                                }) {
                                    VStack(spacing: 8) {
                                        Image(systemName: template.icon)
                                            .foregroundColor(.accentColor)
                                            .font(.system(size: 20))
                                        
                                        Text(template.name)
                                            .font(.caption)
                                            .fontWeight(.medium)
                                            .foregroundColor(.primary)
                                    }
                                    .frame(height: 60)
                                    .frame(maxWidth: .infinity)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(Color.accentColor.opacity(0.05))
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 8)
                                                    .strokeBorder(Color.accentColor.opacity(0.2), lineWidth: 1)
                                            )
                                    )
                                }
                                .buttonStyle(.plain)
                                .help(template.description)
                            }
                        }
                    }
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 20)
            }
            
            // 底部操作按钮
            HStack(spacing: 16) {
                Button("取消") {
                    showingNewBatchSheet = false
                    resetForm()
                }
                .keyboardShortcut(.escape)
                .buttonStyle(.bordered)
                .controlSize(.regular)
                
                Spacer()
                
                Button("创建批次") {
                    createNewBatch()
                }
                .keyboardShortcut(.return)
                .buttonStyle(.borderedProminent)
                .controlSize(.regular)
                .disabled(newBatchName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || newBatchName.count > 50 || newBatchNotes.count > 200)
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 16)
            .background(.regularMaterial)
        }
        .frame(width: 520, height: 600)
        .background(.regularMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.15), radius: 20, x: 0, y: 8)
        .onAppear {
            newBatchName = generateDefaultBatchName()
        }
    }
    
    // MARK: - 批次模板
    private struct BatchTemplate {
        let name: String
        let icon: String
        let description: String
        let nameTemplate: String
        let notesTemplate: String
    }
    
    private var batchTemplates: [BatchTemplate] {
        [
            BatchTemplate(
                name: "工作项目",
                icon: "briefcase.fill",
                description: "用于工作相关的文档和资料",
                nameTemplate: "工作项目 - \(Date().formatted(date: .abbreviated, time: .omitted))",
                notesTemplate: "工作相关的文档、表格、演示文稿等资料的集合"
            ),
            BatchTemplate(
                name: "个人资料",
                icon: "person.crop.circle.fill",
                description: "个人文档和资料整理",
                nameTemplate: "个人资料 - \(Date().formatted(date: .abbreviated, time: .omitted))",
                notesTemplate: "个人文档、照片、笔记等资料的整理和管理"
            ),
            BatchTemplate(
                name: "学习笔记",
                icon: "book.fill",
                description: "学习资料和笔记整理",
                nameTemplate: "学习笔记 - \(Date().formatted(date: .abbreviated, time: .omitted))",
                notesTemplate: "课程资料、学习笔记、参考文档等学习相关内容"
            ),
            BatchTemplate(
                name: "媒体素材",
                icon: "photo.on.rectangle.angled",
                description: "图片、视频等媒体文件",
                nameTemplate: "媒体素材 - \(Date().formatted(date: .abbreviated, time: .omitted))",
                notesTemplate: "图片、视频、音频等多媒体素材的整理和管理"
            )
        ]
    }
    
    private func applyTemplate(_ template: BatchTemplate) {
        newBatchName = template.nameTemplate
        newBatchNotes = template.notesTemplate
    }
    
    // MARK: - 计算属性
    private var filteredBatches: [NSManagedObject] {
        if searchText.isEmpty {
            return batchService.batches
        } else {
            return batchService.batches.filter { batch in
                let batchName = batch.value(forKey: "name") as? String ?? ""
                return batchName.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    private var isAllSelected: Bool {
        !filteredBatches.isEmpty && filteredBatches.allSatisfy { selectedBatchIDs.contains($0.objectID) }
    }
    
    // MARK: - 辅助方法
    private func deleteBatch(_ batch: NSManagedObject) {
        do {
            try batchService.deleteBatch(batch)
        } catch {
            NSLog("Failed to delete batch: \(error)")
        }
    }
    
    // MARK: - 多选相关方法
    private func toggleMultiSelectMode() {
        isMultiSelectMode.toggle()
        if !isMultiSelectMode {
            selectedBatchIDs.removeAll()
        }
    }
    
    private func toggleBatchSelection(_ batch: NSManagedObject) {
        if selectedBatchIDs.contains(batch.objectID) {
            selectedBatchIDs.remove(batch.objectID)
        } else {
            selectedBatchIDs.insert(batch.objectID)
        }
    }
    
    private func toggleSelectAll() {
        if isAllSelected {
            selectedBatchIDs.removeAll()
        } else {
            selectedBatchIDs = Set(filteredBatches.map { $0.objectID })
        }
    }
    
    private func deleteSelectedBatches() {
        // 获取要删除的批次对象
        let batchesToDelete = filteredBatches.filter { selectedBatchIDs.contains($0.objectID) }
        
        // 记录删除失败的批次
        var failedDeletions: [NSManagedObject] = []
        
        for batch in batchesToDelete {
            do {
                try batchService.deleteBatch(batch)
                selectedBatchIDs.remove(batch.objectID)
            } catch {
                NSLog("Failed to delete batch: \(error)")
                failedDeletions.append(batch)
            }
        }
        
        // 如果有删除失败的，显示提示
        if !failedDeletions.isEmpty {
            let alert = NSAlert()
            alert.messageText = "删除部分失败"
            alert.informativeText = "有 \(failedDeletions.count) 个批次删除失败"
            alert.alertStyle = .warning
            alert.addButton(withTitle: "确定")
            alert.runModal()
        }
        
        // 如果所有选中的都删除完毕，退出多选模式
        if selectedBatchIDs.isEmpty {
            isMultiSelectMode = false
        }
    }
    
    private func createNewBatch() {
        let trimmedName = newBatchName.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedNotes = newBatchNotes.trimmingCharacters(in: .whitespacesAndNewlines)
        
        do {
            let newBatch = try batchService.createNewBatch(
                name: trimmedName,
                notes: trimmedNotes.isEmpty ? nil : trimmedNotes
            )
            batchService.setCurrentBatch(newBatch)
            showingNewBatchSheet = false
            resetForm()
        } catch {
            NSLog("Failed to create new batch: \(error)")
        }
    }
    
    private func resetForm() {
        newBatchName = ""
        newBatchNotes = ""
    }
    
    private func generateDefaultBatchName() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return "批次 \(formatter.string(from: Date()))"
    }
}

// MARK: - 批次行视图
struct BatchRowView: View {
    let batch: NSManagedObject
    let isSelected: Bool
    let isMultiSelectMode: Bool
    let isMultiSelected: Bool
    let onSelect: () -> Void
    let onDelete: () -> Void
    
    @EnvironmentObject private var batchService: BatchService
    @State private var isHovered = false
    
    var body: some View {
        let isActiveBatch = batch == batchService.activeBatch
        
        HStack(spacing: 12) {
            // 多选模式下的选择框
            if isMultiSelectMode {
                Button(action: onSelect) {
                    Image(systemName: isMultiSelected ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(isMultiSelected ? .accentColor : .secondary)
                        .font(.system(size: 18))
                }
                .buttonStyle(.plain)
            }
            
            // 批次图标
            HStack(spacing: 4) {
                Image(systemName: "folder.fill")
                    .foregroundColor(isSelected ? .accentColor : .secondary)
                    .font(.system(size: 16))
                
                // 活跃批次标识
                if isActiveBatch {
                    Image(systemName: "star.fill")
                        .foregroundColor(.orange)
                        .font(.system(size: 10))
                }
            }
            
            // 批次信息
            VStack(alignment: .leading, spacing: 2) {
                let batchName = batch.value(forKey: "name") as? String ?? "未命名批次"
                let itemCount = (batch.value(forKey: "contentItems") as? NSSet)?.count ?? 0
                let createdAt = batch.value(forKey: "createdAt") as? Date ?? Date()
                
                Text(batchName)
                    .font(.system(size: 13, weight: isSelected ? .semibold : .regular))
                    .foregroundColor(isSelected ? .accentColor : .primary)
                    .lineLimit(1)
                
                HStack(spacing: 8) {
                    Text("\(itemCount) 项")
                        .font(.system(size: 11))
                        .foregroundColor(.secondary)
                    
                    Text(createdAt.formatted(date: .abbreviated, time: .omitted))
                        .font(.system(size: 11))
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // 删除按钮（悬停时显示）
            if isHovered && !isSelected {
                InteractiveButton(action: onDelete) {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                        .font(.system(size: 12))
                        .frame(minWidth: 20, minHeight: 20)
                }
                .help("删除批次")
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(
                    isMultiSelectMode ? 
                        (isMultiSelected ? Color.accentColor.opacity(0.15) : 
                         (isHovered ? Color.gray.opacity(0.1) : Color.clear)) :
                        (isSelected ? Color.accentColor.opacity(0.15) : 
                         (isHovered ? Color.gray.opacity(0.1) : Color.clear))
                )
        )
        .contentShape(Rectangle())
        .onTapGesture {
            onSelect()
        }
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .contextMenu {
            Button("选择此批次") {
                DispatchQueue.main.async {
                    onSelect()
                }
            }
            .disabled(isSelected)
            
            Button(isActiveBatch ? "已设为活跃批次" : "设为活跃批次") {
                DispatchQueue.main.async {
                    batchService.setActiveBatch(batch)
                }
            }
            .disabled(isActiveBatch)
            
            Divider()
            
            Button("删除批次", role: .destructive) {
                DispatchQueue.main.async {
                    onDelete()
                }
            }
        }
    }
}