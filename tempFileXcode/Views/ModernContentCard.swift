import SwiftUI
import AppKit

// MARK: - 现代化内容卡片
struct ModernContentCard: View {
    let item: ContentItem
    let isSelected: Bool
    let onSelect: () -> Void
    let onEdit: () -> Void
    let onQuickLook: () -> Void
    
    @EnvironmentObject private var contentService: ContentService
    @State private var isHovered = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 顶部：类型图标
            HStack {
                Image(systemName: item.contentTypeEnum.systemImage)
                    .foregroundColor(Color(tagColor: item.contentTypeEnum.color))
                    .font(.system(size: 18))
                
                Spacer()
                
                // 快速操作按钮（悬停时显示）
                if isHovered {
                    HStack(spacing: 4) {
                        But<PERSON>(action: onQuickLook) {
                            Image(systemName: "eye")
                                .font(.system(size: 12))
                        }
                        .buttonStyle(.plain)
                        .help("空格预览")
                        
                        Button(action: onEdit) {
                            Image(systemName: "pencil")
                                .font(.system(size: 12))
                        }
                        .buttonStyle(.plain)
                        .help("编辑")
                    }
                    .foregroundColor(.accentColor)
                }
            }
            
            // 内容预览
            contentPreview
            
            // 标题和信息
            VStack(alignment: .leading, spacing: 4) {
                Text(item.displayTitle)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                HStack {
                    Text(item.formattedCreatedDate)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(item.formattedFileSize)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(NSColor.controlBackgroundColor))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .strokeBorder(
                            isHovered ? Color.accentColor.opacity(0.5) : Color.clear,
                            lineWidth: 1
                        )
                )
        )
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .onTapGesture(count: 2) {
            // 双击打开文件
            if let filePath = item.filePath {
                NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
            }
        }
        .contextMenu {
            contextMenuItems
        }
    }
    
    @ViewBuilder
    private var contentPreview: some View {
        switch item.contentTypeEnum {
        case .text:
            if let content = item.content {
                Text(content)
                    .font(.caption)
                    .lineLimit(3)
                    .padding(8)
                    .frame(maxWidth: .infinity, minHeight: 60, alignment: .topLeading)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(6)
            } else {
                Rectangle()
                    .fill(Color.gray.opacity(0.1))
                    .frame(height: 60)
                    .cornerRadius(6)
            }
            
        case .image:
            if let filePath = item.filePath,
               let image = NSImage(contentsOfFile: filePath) {
                Image(nsImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 100)
                    .clipped()
                    .cornerRadius(6)
            } else {
                Rectangle()
                    .fill(Color.gray.opacity(0.1))
                    .frame(height: 100)
                    .cornerRadius(6)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.system(size: 24))
                            .foregroundColor(.secondary)
                    )
            }
            
        case .file:
            VStack(spacing: 8) {
                Image(systemName: getFileIcon())
                    .font(.system(size: 32))
                    .foregroundColor(.accentColor)
                
                if let fileName = item.fileName {
                    Text(fileName)
                        .font(.caption2)
                        .lineLimit(2)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(height: 80)
            .frame(maxWidth: .infinity)
        }
    }
    
    @ViewBuilder
    private var contextMenuItems: some View {
        Button("打开") {
            if let filePath = item.filePath {
                NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
            }
        }
        
        Button("快速预览") {
            onQuickLook()
        }
        
        Divider()
        
        Button("编辑") {
            onEdit()
        }
        
        if item.contentTypeEnum == .text, let content = item.content {
            Button("复制文本") {
                NSPasteboard.general.clearContents()
                NSPasteboard.general.setString(content, forType: .string)
            }
        }
        
        if item.contentTypeEnum == .file || item.contentTypeEnum == .image {
            Button("在Finder中显示") {
                if let filePath = item.filePath {
                    NSWorkspace.shared.selectFile(filePath, inFileViewerRootedAtPath: "")
                }
            }
        }
        
        Button("分享") {
            shareItem()
        }
        
        Divider()
        
        Button("删除", role: .destructive) {
            deleteItem()
        }
    }
    
    private func getFileIcon() -> String {
        guard let fileName = item.fileName else { return "doc" }
        let ext = (fileName as NSString).pathExtension.lowercased()
        
        switch ext {
        case "pdf": return "doc.richtext"
        case "doc", "docx": return "doc.text"
        case "xls", "xlsx": return "tablecells"
        case "ppt", "pptx": return "rectangle.3.group.bubble.left"
        case "zip", "rar", "7z": return "archivebox"
        case "mp3", "wav", "m4a": return "music.note"
        case "mp4", "mov", "avi": return "play.rectangle"
        default: return "doc"
        }
    }
    
    private func shareItem() {
        if let filePath = item.filePath {
            let sharingPicker = NSSharingServicePicker(items: [URL(fileURLWithPath: filePath)])
            if let view = NSApp.keyWindow?.contentView {
                sharingPicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
            }
        } else if let content = item.content {
            let sharingPicker = NSSharingServicePicker(items: [content])
            if let view = NSApp.keyWindow?.contentView {
                sharingPicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
            }
        }
    }
    
    private func deleteItem() {
        Task {
            do {
                try await contentService.deleteContent(item)
            } catch {
                NSLog("删除失败: \(error)")
            }
        }
    }
}

// MARK: - 现代化内容行
struct ModernContentRow: View {
    let item: ContentItem
    let isSelected: Bool
    let onSelect: () -> Void
    let onEdit: () -> Void
    let onQuickLook: () -> Void
    
    @EnvironmentObject private var contentService: ContentService
    @State private var isHovered = false
    
    var body: some View {
        HStack(spacing: 12) {
            // 文件图标
            Image(systemName: item.contentTypeEnum.systemImage)
                .foregroundColor(Color(tagColor: item.contentTypeEnum.color))
                .font(.system(size: 18))
                .frame(width: 20)
            
            // 文件名
            VStack(alignment: .leading, spacing: 2) {
                Text(item.displayTitle)
                    .font(.subheadline)
                    .lineLimit(1)
                
                Text(item.contentTypeEnum.displayName)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 修改时间
            Text(item.formattedCreatedDate)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 100, alignment: .trailing)
            
            // 文件大小
            Text(item.formattedFileSize)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 60, alignment: .trailing)
            
            // 快速操作
            if isHovered {
                HStack(spacing: 8) {
                    Button(action: onQuickLook) {
                        Image(systemName: "eye")
                            .font(.system(size: 14))
                    }
                    .buttonStyle(.plain)
                    .help("预览")
                    
                    Button(action: onEdit) {
                        Image(systemName: "pencil")
                            .font(.system(size: 14))
                    }
                    .buttonStyle(.plain)
                    .help("编辑")
                }
                .foregroundColor(.accentColor)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(
            Rectangle()
                .fill(isHovered ? Color.gray.opacity(0.05) : Color.clear)
        )
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .onTapGesture(count: 2) {
            if let filePath = item.filePath {
                NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
            }
        }
        .contextMenu {
            contextMenuItems
        }
    }
    
    @ViewBuilder
    private var contextMenuItems: some View {
        Button("打开") {
            if let filePath = item.filePath {
                NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
            }
        }
        
        Button("快速预览") {
            onQuickLook()
        }
        
        Divider()
        
        Button("编辑") {
            onEdit()
        }
        
        if item.contentTypeEnum == .file || item.contentTypeEnum == .image {
            Button("在Finder中显示") {
                if let filePath = item.filePath {
                    NSWorkspace.shared.selectFile(filePath, inFileViewerRootedAtPath: "")
                }
            }
        }
        
        Button("分享") {
            shareRowItem()
        }
        
        Divider()
        
        Button("删除", role: .destructive) {
            deleteRowItem()
        }
    }
    
    private func shareRowItem() {
        if let filePath = item.filePath {
            let sharingPicker = NSSharingServicePicker(items: [URL(fileURLWithPath: filePath)])
            if let view = NSApp.keyWindow?.contentView {
                sharingPicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
            }
        } else if let content = item.content {
            let sharingPicker = NSSharingServicePicker(items: [content])
            if let view = NSApp.keyWindow?.contentView {
                sharingPicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
            }
        }
    }
    
    private func deleteRowItem() {
        Task {
            do {
                try await contentService.deleteContent(item)
            } catch {
                NSLog("删除失败: \(error)")
            }
        }
    }
}

// MARK: - 现代化内容缩略图
struct ModernContentThumbnail: View {
    let item: ContentItem
    let isSelected: Bool
    let onSelect: () -> Void
    let onEdit: () -> Void
    let onQuickLook: () -> Void
    
    @EnvironmentObject private var contentService: ContentService
    @State private var isHovered = false
    
    var body: some View {
        VStack(spacing: 6) {
            // 缩略图
            ZStack {
                thumbnailContent
            }
            .frame(width: 100, height: 80)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(6)
            
            // 文件名
            Text(item.displayTitle)
                .font(.caption2)
                .lineLimit(2)
                .multilineTextAlignment(.center)
                .frame(height: 24)
        }
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .onTapGesture(count: 2) {
            if let filePath = item.filePath {
                NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
            }
        }
        .contextMenu {
            contextMenuItems
        }
    }
    
    @ViewBuilder
    private var thumbnailContent: some View {
        switch item.contentTypeEnum {
        case .image:
            if let filePath = item.filePath,
               let image = NSImage(contentsOfFile: filePath) {
                Image(nsImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 100, height: 80)
                    .clipped()
            } else {
                Image(systemName: "photo")
                    .font(.system(size: 24))
                    .foregroundColor(.secondary)
            }
            
        case .text:
            Image(systemName: "doc.text")
                .font(.system(size: 24))
                .foregroundColor(.accentColor)
            
        case .file:
            Image(systemName: item.contentTypeEnum.systemImage)
                .font(.system(size: 24))
                .foregroundColor(.accentColor)
        }
    }
    
    @ViewBuilder
    private var contextMenuItems: some View {
        Button("打开") {
            if let filePath = item.filePath {
                NSWorkspace.shared.open(URL(fileURLWithPath: filePath))
            }
        }
        
        Button("快速预览") {
            onQuickLook()
        }
        
        Divider()
        
        Button("编辑") {
            onEdit()
        }
        
        if item.contentTypeEnum == .file || item.contentTypeEnum == .image {
            Button("在Finder中显示") {
                if let filePath = item.filePath {
                    NSWorkspace.shared.selectFile(filePath, inFileViewerRootedAtPath: "")
                }
            }
        }
        
        Button("分享") {
            shareThumbnailItem()
        }
        
        Divider()
        
        Button("删除", role: .destructive) {
            deleteThumbnailItem()
        }
    }
    
    private func shareThumbnailItem() {
        if let filePath = item.filePath {
            let sharingPicker = NSSharingServicePicker(items: [URL(fileURLWithPath: filePath)])
            if let view = NSApp.keyWindow?.contentView {
                sharingPicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
            }
        } else if let content = item.content {
            let sharingPicker = NSSharingServicePicker(items: [content])
            if let view = NSApp.keyWindow?.contentView {
                sharingPicker.show(relativeTo: .zero, of: view, preferredEdge: .minY)
            }
        }
    }
    
    private func deleteThumbnailItem() {
        Task {
            do {
                try await contentService.deleteContent(item)
            } catch {
                NSLog("删除失败: \(error)")
            }
        }
    }
}