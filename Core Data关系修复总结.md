# Core Data关系修复总结

## 问题描述

应用启动时出现以下错误：
```
FAULT: NSUnknownKeyException: [<Batch 0x7df404e60> valueForUndefinedKey:]: 
the entity Batch is not key value coding-compliant for the key "items"
```

## 问题原因

代码中使用了错误的Core Data关系键名：
- **错误使用**: `batch.value(forKey: "items")`
- **正确应该**: `batch.value(forKey: "contentItems")`

根据Core Data模型定义，Batch实体的关系名称是`contentItems`，而不是`items`。

## 修复内容

### 1. BatchSettingsPopover.swift
```swift
// 修复前
let itemCount = currentBatch.value(forKey: "items") as? NSSet

// 修复后  
let itemCount = currentBatch.value(forKey: "contentItems") as? NSSet
```

### 2. BatchInfoView.swift
```swift
// 修复前
let itemCount = (batch.value(forKey: "items") as? NSSet)?.count ?? 0
guard let items = batch.value(forKey: "items") as? NSSet else { return 0 }

// 修复后
let itemCount = (batch.value(forKey: "contentItems") as? NSSet)?.count ?? 0
guard let items = batch.value(forKey: "contentItems") as? NSSet else { return 0 }
```

## Core Data模型验证

从`tempFileXcode.xcdatamodeld`中确认的关系定义：
```xml
<entity name="Batch">
    <relationship name="contentItems" optional="YES" toMany="YES" 
                  deletionRule="Cascade" destinationEntity="ContentItem" 
                  inverseName="batch" inverseEntity="ContentItem"/>
</entity>

<entity name="ContentItem">
    <relationship name="batch" optional="YES" maxCount="1" 
                  deletionRule="Nullify" destinationEntity="Batch" 
                  inverseName="contentItems" inverseEntity="Batch"/>
</entity>
```

## 修复结果

✅ **编译成功**: 项目现在可以正常编译  
✅ **关系正确**: 所有Core Data关系访问都使用正确的键名  
✅ **启动修复**: NSUnknownKeyException错误已解决  

## 测试建议

启动应用后，请测试以下功能：

1. **批次信息显示**
   - [ ] 批次统计信息正确显示
   - [ ] 项目数量计算正确
   - [ ] 批次大小计算正常

2. **批次设置功能**
   - [ ] 批次设置弹窗正常打开
   - [ ] 项目数量显示正确
   - [ ] 保存和删除功能正常

3. **Core Data操作**
   - [ ] 创建新批次正常
   - [ ] 添加内容到批次正常
   - [ ] 批次和内容项关系正确

## 预防措施

为了避免类似问题，建议：

1. **使用类型安全的访问方式**：
   ```swift
   // 推荐使用生成的属性访问器
   let items = batch.contentItems
   
   // 而不是字符串键访问
   let items = batch.value(forKey: "contentItems")
   ```

2. **代码审查**：确保所有Core Data关系访问使用正确的键名

3. **单元测试**：为Core Data操作添加测试用例

## 下一步

应用现在应该可以正常启动了。如果还有其他问题，请提供具体的错误信息。