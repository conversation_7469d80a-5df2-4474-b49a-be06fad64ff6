# 启动问题排查指南

## 已修复的问题

✅ **编译问题已解决**
- 修复了环境对象依赖问题
- 修复了ContentData.fromFile方法中缺少filePath的问题
- 所有语法错误已修复，项目可以成功编译

## 常见启动问题及解决方案

### 1. Core Data 相关错误

**可能的错误信息**:
- "Failed to load persistent store"
- "Core Data model version mismatch"
- "NSManagedObjectContext error"

**解决方案**:
```bash
# 清理应用数据
rm -rf ~/Library/Containers/no-url.tempFileXcode/
# 或者在应用中重置Core Data存储
```

### 2. 权限相关错误

**可能的错误信息**:
- "Accessibility permissions not granted"
- "File access denied"

**解决方案**:
1. 打开系统偏好设置 > 安全性与隐私 > 隐私
2. 在"辅助功能"中添加应用
3. 在"文件和文件夹"中给予必要权限

### 3. 环境对象注入错误

**可能的错误信息**:
- "EnvironmentObject not found"
- "Missing environment object"

**解决方案**:
已在代码中修复，确保所有环境对象都正确注入到视图层次结构中。

### 4. 服务初始化错误

**可能的错误信息**:
- "Service initialization failed"
- "Dependency injection error"

**解决方案**:
检查ContentManagerApp.swift中的服务初始化顺序。

## 调试步骤

### 1. 查看控制台日志
```bash
# 在终端中运行应用并查看日志
open /Applications/Console.app
# 或者直接在Xcode中运行并查看调试输出
```

### 2. 检查应用沙盒
```bash
# 查看应用容器
open ~/Library/Containers/no-url.tempFileXcode/
```

### 3. 重置应用状态
```bash
# 删除用户偏好设置
defaults delete no-url.tempFileXcode
```

## 启动流程检查清单

- [ ] Core Data模型加载成功
- [ ] 所有服务正确初始化
- [ ] 环境对象正确注入
- [ ] 权限检查通过
- [ ] 主窗口显示正常

## 如果问题仍然存在

1. **提供具体错误信息**: 请分享控制台中的具体错误日志
2. **检查系统版本**: 确保运行在支持的macOS版本上
3. **清理构建**: 在Xcode中执行Product > Clean Build Folder
4. **重新构建**: 完全重新编译项目

## 测试建议

启动成功后，请测试以下功能：

1. **基本功能测试**:
   - [ ] 应用正常启动
   - [ ] 主界面显示正常
   - [ ] 可以创建新批次

2. **按钮交互测试**:
   - [ ] 工具栏按钮可点击
   - [ ] 侧边栏按钮响应正常
   - [ ] 弹窗按钮工作正常

3. **拖拽功能测试**:
   - [ ] 可以拖拽文件到应用
   - [ ] 拖拽反馈正常显示
   - [ ] 文件成功添加到批次

4. **预览功能测试**:
   - [ ] 可以查看文件详情
   - [ ] 文本内容可以复制
   - [ ] 图片预览正常显示

如果在测试过程中发现任何问题，请提供具体的错误信息和操作步骤。