// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		68D305E72E351FE900E3FD86 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 68D305CF2E351FE700E3FD86 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 68D305D62E351FE700E3FD86;
			remoteInfo = tempFileXcode;
		};
		68D305F12E351FE900E3FD86 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 68D305CF2E351FE700E3FD86 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 68D305D62E351FE700E3FD86;
			remoteInfo = tempFileXcode;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		68D305D72E351FE700E3FD86 /* tempFileXcode.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = tempFileXcode.app; sourceTree = BUILT_PRODUCTS_DIR; };
		68D305E62E351FE900E3FD86 /* tempFileXcodeTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = tempFileXcodeTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		68D305F02E351FE900E3FD86 /* tempFileXcodeUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = tempFileXcodeUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		68D305D92E351FE700E3FD86 /* tempFileXcode */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = tempFileXcode;
			sourceTree = "<group>";
		};
		68D305E92E351FE900E3FD86 /* tempFileXcodeTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = tempFileXcodeTests;
			sourceTree = "<group>";
		};
		68D305F32E351FE900E3FD86 /* tempFileXcodeUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = tempFileXcodeUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		68D305D42E351FE700E3FD86 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		68D305E32E351FE900E3FD86 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		68D305ED2E351FE900E3FD86 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		68D305CE2E351FE700E3FD86 = {
			isa = PBXGroup;
			children = (
				68D305D92E351FE700E3FD86 /* tempFileXcode */,
				68D305E92E351FE900E3FD86 /* tempFileXcodeTests */,
				68D305F32E351FE900E3FD86 /* tempFileXcodeUITests */,
				68D305D82E351FE700E3FD86 /* Products */,
			);
			sourceTree = "<group>";
		};
		68D305D82E351FE700E3FD86 /* Products */ = {
			isa = PBXGroup;
			children = (
				68D305D72E351FE700E3FD86 /* tempFileXcode.app */,
				68D305E62E351FE900E3FD86 /* tempFileXcodeTests.xctest */,
				68D305F02E351FE900E3FD86 /* tempFileXcodeUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		68D305D62E351FE700E3FD86 /* tempFileXcode */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 68D305FA2E351FE900E3FD86 /* Build configuration list for PBXNativeTarget "tempFileXcode" */;
			buildPhases = (
				68D305D32E351FE700E3FD86 /* Sources */,
				68D305D42E351FE700E3FD86 /* Frameworks */,
				68D305D52E351FE700E3FD86 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				68D305D92E351FE700E3FD86 /* tempFileXcode */,
			);
			name = tempFileXcode;
			packageProductDependencies = (
			);
			productName = tempFileXcode;
			productReference = 68D305D72E351FE700E3FD86 /* tempFileXcode.app */;
			productType = "com.apple.product-type.application";
		};
		68D305E52E351FE900E3FD86 /* tempFileXcodeTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 68D305FD2E351FE900E3FD86 /* Build configuration list for PBXNativeTarget "tempFileXcodeTests" */;
			buildPhases = (
				68D305E22E351FE900E3FD86 /* Sources */,
				68D305E32E351FE900E3FD86 /* Frameworks */,
				68D305E42E351FE900E3FD86 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				68D305E82E351FE900E3FD86 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				68D305E92E351FE900E3FD86 /* tempFileXcodeTests */,
			);
			name = tempFileXcodeTests;
			packageProductDependencies = (
			);
			productName = tempFileXcodeTests;
			productReference = 68D305E62E351FE900E3FD86 /* tempFileXcodeTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		68D305EF2E351FE900E3FD86 /* tempFileXcodeUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 68D306002E351FE900E3FD86 /* Build configuration list for PBXNativeTarget "tempFileXcodeUITests" */;
			buildPhases = (
				68D305EC2E351FE900E3FD86 /* Sources */,
				68D305ED2E351FE900E3FD86 /* Frameworks */,
				68D305EE2E351FE900E3FD86 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				68D305F22E351FE900E3FD86 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				68D305F32E351FE900E3FD86 /* tempFileXcodeUITests */,
			);
			name = tempFileXcodeUITests;
			packageProductDependencies = (
			);
			productName = tempFileXcodeUITests;
			productReference = 68D305F02E351FE900E3FD86 /* tempFileXcodeUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		68D305CF2E351FE700E3FD86 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 2600;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					68D305D62E351FE700E3FD86 = {
						CreatedOnToolsVersion = 26.0;
					};
					68D305E52E351FE900E3FD86 = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = 68D305D62E351FE700E3FD86;
					};
					68D305EF2E351FE900E3FD86 = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = 68D305D62E351FE700E3FD86;
					};
				};
			};
			buildConfigurationList = 68D305D22E351FE700E3FD86 /* Build configuration list for PBXProject "tempFileXcode" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 68D305CE2E351FE700E3FD86;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 68D305D82E351FE700E3FD86 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				68D305D62E351FE700E3FD86 /* tempFileXcode */,
				68D305E52E351FE900E3FD86 /* tempFileXcodeTests */,
				68D305EF2E351FE900E3FD86 /* tempFileXcodeUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		68D305D52E351FE700E3FD86 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		68D305E42E351FE900E3FD86 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		68D305EE2E351FE900E3FD86 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		68D305D32E351FE700E3FD86 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		68D305E22E351FE900E3FD86 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		68D305EC2E351FE900E3FD86 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		68D305E82E351FE900E3FD86 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 68D305D62E351FE700E3FD86 /* tempFileXcode */;
			targetProxy = 68D305E72E351FE900E3FD86 /* PBXContainerItemProxy */;
		};
		68D305F22E351FE900E3FD86 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 68D305D62E351FE700E3FD86 /* tempFileXcode */;
			targetProxy = 68D305F12E351FE900E3FD86 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		68D305F82E351FE900E3FD86 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 62HQ3GK3PW;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 26.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		68D305F92E351FE900E3FD86 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 62HQ3GK3PW;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 26.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		68D305FB2E351FE900E3FD86 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 62HQ3GK3PW;
				ENABLE_APP_SANDBOX = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SELECTED_FILES = readonly;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "no-url.tempBox";
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		68D305FC2E351FE900E3FD86 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 62HQ3GK3PW;
				ENABLE_APP_SANDBOX = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SELECTED_FILES = readonly;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "no-url.tempBox";
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		68D305FE2E351FE900E3FD86 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 62HQ3GK3PW;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "no-url.tempFileXcodeTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/tempFileXcode.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/tempFileXcode";
			};
			name = Debug;
		};
		68D305FF2E351FE900E3FD86 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 62HQ3GK3PW;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "no-url.tempFileXcodeTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/tempFileXcode.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/tempFileXcode";
			};
			name = Release;
		};
		68D306012E351FE900E3FD86 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 62HQ3GK3PW;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "no-url.tempFileXcodeUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = tempFileXcode;
			};
			name = Debug;
		};
		68D306022E351FE900E3FD86 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 62HQ3GK3PW;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "no-url.tempFileXcodeUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = tempFileXcode;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		68D305D22E351FE700E3FD86 /* Build configuration list for PBXProject "tempFileXcode" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				68D305F82E351FE900E3FD86 /* Debug */,
				68D305F92E351FE900E3FD86 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		68D305FA2E351FE900E3FD86 /* Build configuration list for PBXNativeTarget "tempFileXcode" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				68D305FB2E351FE900E3FD86 /* Debug */,
				68D305FC2E351FE900E3FD86 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		68D305FD2E351FE900E3FD86 /* Build configuration list for PBXNativeTarget "tempFileXcodeTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				68D305FE2E351FE900E3FD86 /* Debug */,
				68D305FF2E351FE900E3FD86 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		68D306002E351FE900E3FD86 /* Build configuration list for PBXNativeTarget "tempFileXcodeUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				68D306012E351FE900E3FD86 /* Debug */,
				68D306022E351FE900E3FD86 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 68D305CF2E351FE700E3FD86 /* Project object */;
}
