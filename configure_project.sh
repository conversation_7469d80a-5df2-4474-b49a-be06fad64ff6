#!/bin/bash

# Content Manager 项目自动配置脚本
echo "🚀 开始配置 Content Manager 项目..."

PROJECT_DIR="/Users/<USER>/IdeaProjects/kiro小项目/tempFileXcode"
PROJECT_NAME="tempFileXcode"

cd "$PROJECT_DIR"

echo "📁 检查项目结构..."

# 统计文件数量
SWIFT_FILES=$(find "$PROJECT_NAME" -name "*.swift" | wc -l)
TEST_FILES=$(find "${PROJECT_NAME}Tests" -name "*.swift" | wc -l)

echo "📊 项目统计:"
echo "   - Swift 源文件: $SWIFT_FILES 个"
echo "   - 测试文件: $TEST_FILES 个"

echo "✅ 项目文件迁移完成！"
echo "📖 请查看 SETUP_GUIDE.md 获取详细配置说明"
