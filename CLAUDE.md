# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

TempBox 是一个 macOS SwiftUI 应用，用于管理剪贴板内容、文件和媒体。支持自动监听剪贴板、全局快捷键、内容搜索和导出功能。

## 开发命令

### 构建和运行
```bash
# 在 Xcode 中构建项目
open tempBox.xcodeproj

# 或使用命令行构建
xcodebuild -project tempBox.xcodeproj -scheme tempBox -configuration Debug build

# 运行测试
xcodebuild test -project tempBox.xcodeproj -scheme tempBox -destination 'platform=macOS'

# 单个测试类
xcodebuild test -project tempBox.xcodeproj -scheme tempBox -destination 'platform=macOS' -only-testing:tempBoxTests/ContentServiceTests

# 查找应用路径（用于辅助功能权限设置）
find ~/Library/Developer/Xcode/DerivedData -name "tempBox.app" -type d 2>/dev/null
```

### 项目配置
```bash
# 运行项目配置脚本
./configure_project.sh
```

## 代码架构

### 应用结构
- **ContentManagerApp.swift**: 主应用入口，配置所有服务和依赖注入
- **AppState**: 全局应用状态管理（视图模式、搜索状态等）
- **ViewMode**: 支持时间视图和类型视图两种显示模式

### 核心服务层 (Services/)
- **ContentService**: 内容管理核心服务，处理 CRUD 操作和文件存储
- **SearchService**: 全文搜索功能，支持标题、内容和标签搜索
- **PasteboardMonitor**: 剪贴板监听服务，自动检测新内容
- **HotkeyService**: 全局快捷键服务（⌘⇧V），需要辅助功能权限
- **BatchService**: 批量操作服务
- **ExportService**: 内容导出服务

### 数据模型层 (Models/)
- **ContentData**: 数据传输对象，包含内容的所有属性
- **ContentType**: 内容类型枚举（文本、图片、文件）
- **PersistenceController**: Core Data 持久化控制器
- **CoreDataExtensions**: Core Data 实体扩展

### 视图层 (Views/)
- **MainContentView**: 主界面
- **ContentEditView**: 内容编辑界面
- **QuickPasteWindow**: 快速粘贴窗口
- **SettingsView**: 设置界面

### 测试结构
- **ContentServiceTests**: 内容服务测试
- **SearchServiceTests**: 搜索服务测试
- **PasteboardMonitorTests**: 剪贴板监听测试

## 重要配置

### 权限要求
- **辅助功能权限**: 全局快捷键功能必需
- **文件系统权限**: 支持用户选择的文件读写
- **Apple Events**: 系统事件访问

### 应用设置
- **最低系统版本**: macOS 12.0
- **沙盒模式**: 启用
- **应用类别**: 生产力工具

### 开发注意事项
- 所有服务使用 @MainActor 确保线程安全
- Core Data 使用内存模式进行测试
- 文件存储在 Application Support/ContentManager/ 目录
- 最大文件大小限制: 100MB
- 最大文本长度限制: 1M 字符

### 权限设置指南
参考 `tempBox/辅助功能权限设置指南.md` 了解如何配置辅助功能权限。

## 依赖关系
- SwiftUI (界面框架)
- Core Data (数据持久化)
- Combine (响应式编程)
- Carbon (全局快捷键)
- UniformTypeIdentifiers (文件类型识别)