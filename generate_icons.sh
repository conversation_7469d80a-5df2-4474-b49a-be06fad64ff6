#!/bin/bash

# 检查是否安装了 rsvg-convert
if ! command -v rsvg-convert &> /dev/null; then
    echo "rsvg-convert 未安装。正在尝试安装..."
    if command -v brew &> /dev/null; then
        brew install librsvg
    else
        echo "请先安装 Homebrew，然后运行: brew install librsvg"
        exit 1
    fi
fi

# 设置路径
SVG_FILE="tempFileXcode/Assets.xcassets/AppIcon.appiconset/icon_design.svg"
ICON_DIR="tempFileXcode/Assets.xcassets/AppIcon.appiconset"

# 生成不同尺寸的图标
echo "正在生成 App 图标..."

# 16x16
rsvg-convert -w 16 -h 16 "$SVG_FILE" > "$ICON_DIR/icon_16x16.png"
rsvg-convert -w 32 -h 32 "$SVG_FILE" > "$ICON_DIR/<EMAIL>"

# 32x32
rsvg-convert -w 32 -h 32 "$SVG_FILE" > "$ICON_DIR/icon_32x32.png"
rsvg-convert -w 64 -h 64 "$SVG_FILE" > "$ICON_DIR/<EMAIL>"

# 128x128
rsvg-convert -w 128 -h 128 "$SVG_FILE" > "$ICON_DIR/icon_128x128.png"
rsvg-convert -w 256 -h 256 "$SVG_FILE" > "$ICON_DIR/<EMAIL>"

# 256x256
rsvg-convert -w 256 -h 256 "$SVG_FILE" > "$ICON_DIR/icon_256x256.png"
rsvg-convert -w 512 -h 512 "$SVG_FILE" > "$ICON_DIR/<EMAIL>"

# 512x512
rsvg-convert -w 512 -h 512 "$SVG_FILE" > "$ICON_DIR/icon_512x512.png"
rsvg-convert -w 1024 -h 1024 "$SVG_FILE" > "$ICON_DIR/<EMAIL>"

echo "图标生成完成！"

# 更新 Contents.json 文件
cat > "$ICON_DIR/Contents.json" << 'EOF'
{
  "images" : [
    {
      "filename" : "icon_16x16.png",
      "idiom" : "mac",
      "scale" : "1x",
      "size" : "16x16"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "mac",
      "scale" : "2x",
      "size" : "16x16"
    },
    {
      "filename" : "icon_32x32.png",
      "idiom" : "mac",
      "scale" : "1x",
      "size" : "32x32"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "mac",
      "scale" : "2x",
      "size" : "32x32"
    },
    {
      "filename" : "icon_128x128.png",
      "idiom" : "mac",
      "scale" : "1x",
      "size" : "128x128"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "mac",
      "scale" : "2x",
      "size" : "128x128"
    },
    {
      "filename" : "icon_256x256.png",
      "idiom" : "mac",
      "scale" : "1x",
      "size" : "256x256"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "mac",
      "scale" : "2x",
      "size" : "256x256"
    },
    {
      "filename" : "icon_512x512.png",
      "idiom" : "mac",
      "scale" : "1x",
      "size" : "512x512"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "mac",
      "scale" : "2x",
      "size" : "512x512"
    }
  ],
  "info" : {
    "author" : "xcode",
    "version" : 1
  }
}
EOF

echo "Contents.json 已更新！"