import XCTest
import CoreData
@testable import tempBox

class ContentServiceTests: XCTestCase {
    
    var contentService: ContentService!
    var testPersistenceController: PersistenceController!
    
    override func setUpWithError() throws {
        // Create in-memory persistence controller for testing
        testPersistenceController = PersistenceController(inMemory: true)
        contentService = ContentService(persistenceController: testPersistenceController)
    }
    
    override func tearDownWithError() throws {
        contentService = nil
        testPersistenceController = nil
    }
    
    // MARK: - Content Creation Tests
    func testAddTextContent() async throws {
        let textContent = ContentData.fromText("Test content", title: "Test Title")
        
        let contentItem = try await contentService.addContent(textContent)
        
        XCTAssertEqual(contentItem.title, "Test Title")
        XCTAssertEqual(contentItem.content, "Test content")
        XCTAssertEqual(contentItem.contentTypeEnum, .text)
        XCTAssertFalse(contentItem.isPermanent)
    }
    
    func testAddImageContent() async throws {
        let imageData = Data([0x89, 0x50, 0x4E, 0x47]) // PNG header
        let imageContent = ContentData.fromImage(imageData, fileName: "test.png")
        
        let contentItem = try await contentService.addContent(imageContent)
        
        XCTAssertEqual(contentItem.title, "test.png")
        XCTAssertEqual(contentItem.contentTypeEnum, .image)
        XCTAssertEqual(contentItem.fileSize, Int64(imageData.count))
        XCTAssertNotNil(contentItem.filePath)
    }
    
    func testAddFileContent() async throws {
        let fileData = Data("Test file content".utf8)
        let fileURL = URL(fileURLWithPath: "/tmp/test.txt")
        try fileData.write(to: fileURL)
        
        let fileContent = try ContentData.fromFile(at: fileURL)
        let contentItem = try await contentService.addContent(fileContent)
        
        XCTAssertEqual(contentItem.title, "test.txt")
        XCTAssertEqual(contentItem.contentTypeEnum, .text) // .txt files are detected as text
        XCTAssertEqual(contentItem.fileSize, Int64(fileData.count))
        
        // Cleanup
        try? FileManager.default.removeItem(at: fileURL)
    }
    
    // MARK: - Content Retrieval Tests
    func testGetAllContent() async throws {
        // Add multiple content items
        let textContent = ContentData.fromText("Text 1")
        let imageContent = ContentData.fromImage(Data([0x89, 0x50, 0x4E, 0x47]))
        
        _ = try await contentService.addContent(textContent)
        _ = try await contentService.addContent(imageContent)
        
        let allContent = try await contentService.getAllContent()
        
        XCTAssertEqual(allContent.count, 2)
    }
    
    func testGetContentByType() async throws {
        // Add content of different types
        let textContent = ContentData.fromText("Text content")
        let imageContent = ContentData.fromImage(Data([0x89, 0x50, 0x4E, 0x47]))
        
        _ = try await contentService.addContent(textContent)
        _ = try await contentService.addContent(imageContent)
        
        let textItems = try await contentService.getContentByType(.text)
        let imageItems = try await contentService.getContentByType(.image)
        
        XCTAssertEqual(textItems.count, 1)
        XCTAssertEqual(imageItems.count, 1)
        XCTAssertEqual(textItems.first?.contentTypeEnum, .text)
        XCTAssertEqual(imageItems.first?.contentTypeEnum, .image)
    }
    
    // MARK: - Content Update Tests
    func testUpdateContent() async throws {
        let originalContent = ContentData.fromText("Original content", title: "Original Title")
        let contentItem = try await contentService.addContent(originalContent)
        
        let updatedContent = originalContent.updatingTitle("Updated Title").updatingNotes("Updated notes")
        try await contentService.updateContent(contentItem, with: updatedContent)
        
        // Refresh the item from database
        let allItems = try await contentService.getAllContent()
        let updatedItem = allItems.first { $0.id == contentItem.id }
        
        XCTAssertEqual(updatedItem?.title, "Updated Title")
        XCTAssertEqual(updatedItem?.notes, "Updated notes")
    }
    
    // MARK: - Content Deletion Tests
    func testDeleteContent() async throws {
        let textContent = ContentData.fromText("Content to delete")
        let contentItem = try await contentService.addContent(textContent)
        
        try await contentService.deleteContent(contentItem)
        
        let allContent = try await contentService.getAllContent()
        XCTAssertTrue(allContent.isEmpty)
    }
    
    // MARK: - Content Type Detection Tests
    func testDetectContentTypeFromData() {
        // Test PNG image detection
        let pngData = Data([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A])
        XCTAssertEqual(contentService.detectContentType(from: pngData), .image)
        
        // Test JPEG image detection
        let jpegData = Data([0xFF, 0xD8, 0xFF, 0xE0])
        XCTAssertEqual(contentService.detectContentType(from: jpegData), .image)
        
        // Test text detection
        let textData = Data("Hello, world!".utf8)
        XCTAssertEqual(contentService.detectContentType(from: textData), .text)
        
        // Test unknown data (should default to file)
        let unknownData = Data([0x00, 0x01, 0x02, 0x03])
        XCTAssertEqual(contentService.detectContentType(from: unknownData), .file)
    }
    
    func testDetectContentTypeFromURL() {
        let imageURL = URL(fileURLWithPath: "/path/to/image.png")
        XCTAssertEqual(contentService.detectContentType(from: imageURL), .image)
        
        let textURL = URL(fileURLWithPath: "/path/to/document.txt")
        XCTAssertEqual(contentService.detectContentType(from: textURL), .text)
        
        let fileURL = URL(fileURLWithPath: "/path/to/document.pdf")
        XCTAssertEqual(contentService.detectContentType(from: fileURL), .file)
    }
    
    func testDetectContentTypeFromString() {
        let text = "This is a text string"
        XCTAssertEqual(contentService.detectContentType(from: text), .text)
    }
    
    // MARK: - Validation Tests
    func testValidateValidContent() {
        let validContent = ContentData.fromText("Valid content")
        XCTAssertNoThrow(try contentService.validateContent(validContent))
        XCTAssertTrue(contentService.isContentValid(validContent))
    }
    
    func testValidateInvalidContent() {
        // Test empty text content
        let emptyTextContent = ContentData(
            content: "",
            contentType: .text,
            fileSize: 0
        )
        XCTAssertThrowsError(try contentService.validateContent(emptyTextContent))
        XCTAssertFalse(contentService.isContentValid(emptyTextContent))
        
        // Test negative file size
        let negativeSize = ContentData(
            content: "Valid content",
            contentType: .text,
            fileSize: -1
        )
        XCTAssertThrowsError(try contentService.validateContent(negativeSize))
        XCTAssertFalse(contentService.isContentValid(negativeSize))
    }
    
    // MARK: - File Operations Tests
    func testSaveAndLoadContentFile() async throws {
        let testData = Data("Test file content".utf8)
        let textContent = ContentData.fromText("Text with file", title: "Test")
        let contentItem = try await contentService.addContent(textContent)
        
        let filePath = try await contentService.saveContentFile(testData, for: contentItem)
        XCTAssertFalse(filePath.isEmpty)
        
        let loadedData = try await contentService.loadContentFile(for: contentItem)
        XCTAssertEqual(loadedData, testData)
    }
    
    // MARK: - Storage Statistics Tests
    func testGetStorageStatistics() async throws {
        // Add various types of content
        let textContent = ContentData.fromText("Text content")
        let imageContent = ContentData.fromImage(Data([0x89, 0x50, 0x4E, 0x47]))
        
        _ = try await contentService.addContent(textContent)
        _ = try await contentService.addContent(imageContent)
        
        let statistics = try await contentService.getStorageStatistics()
        
        XCTAssertEqual(statistics.totalItems, 2)
        XCTAssertEqual(statistics.textItems, 1)
        XCTAssertEqual(statistics.imageItems, 1)
        XCTAssertEqual(statistics.fileItems, 0)
        XCTAssertGreaterThan(statistics.totalSize, 0)
    }
    
    // MARK: - Performance Tests
    func testPerformanceAddMultipleItems() async throws {
        measure {
            let expectation = XCTestExpectation(description: "Add multiple items")
            
            Task {
                for i in 0..<100 {
                    let content = ContentData.fromText("Content \(i)")
                    _ = try await contentService.addContent(content)
                }
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
    }
}