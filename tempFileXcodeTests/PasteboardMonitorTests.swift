import XCTest
import AppKit
@testable import tempBox

@MainActor
class PasteboardMonitorTests: XCTestCase {
    
    var pasteboardMonitor: PasteboardMonitor!
    var testPasteboard: NSPasteboard!
    
    override func setUpWithError() throws {
        pasteboardMonitor = PasteboardMonitor()
        testPasteboard = NSPasteboard.general
        
        // Clear pasteboard before each test
        testPasteboard.clearContents()
    }
    
    override func tearDownWithError() throws {
        pasteboardMonitor.stopMonitoring()
        pasteboardMonitor = nil
        testPasteboard.clearContents()
        testPasteboard = nil
    }
    
    // MARK: - Monitoring Tests
    func testStartStopMonitoring() {
        XCTAssertFalse(pasteboardMonitor.isMonitoring)
        
        pasteboardMonitor.startMonitoring()
        XCTAssertTrue(pasteboardMonitor.isMonitoring)
        
        pasteboardMonitor.stopMonitoring()
        XCTAssertFalse(pasteboardMonitor.isMonitoring)
    }
    
    func testMonitoringCallback() {
        let expectation = XCTestExpectation(description: "Content detected callback")
        
        pasteboardMonitor.onContentDetected = { content in
            XCTAssertEqual(content.type, .text)
            XCTAssertEqual(content.text, "Test content")
            expectation.fulfill()
        }
        
        pasteboardMonitor.startMonitoring()
        
        // Add text to pasteboard
        testPasteboard.setString("Test content", forType: .string)
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    // MARK: - Text Content Detection Tests
    func testDetectTextContent() throws {
        testPasteboard.setString("Hello, World!", forType: .string)
        
        let content = try pasteboardMonitor.detectPasteboardContent()
        
        XCTAssertNotNil(content)
        XCTAssertEqual(content?.type, .text)
        XCTAssertEqual(content?.text, "Hello, World!")
        XCTAssertEqual(content?.mimeType, "text/plain")
        XCTAssertGreaterThan(content?.fileSize ?? 0, 0)
    }
    
    func testDetectEmptyText() throws {
        testPasteboard.setString("", forType: .string)
        
        let content = try pasteboardMonitor.detectPasteboardContent()
        
        // Empty text should not be detected as valid content
        XCTAssertNil(content)
    }
    
    // MARK: - Image Content Detection Tests
    func testDetectImageContent() throws {
        // Create a simple test image
        let image = NSImage(size: NSSize(width: 100, height: 100))
        image.lockFocus()
        NSColor.red.setFill()
        NSRect(x: 0, y: 0, width: 100, height: 100).fill()
        image.unlockFocus()
        
        // Add image to pasteboard
        testPasteboard.clearContents()
        testPasteboard.writeObjects([image])
        
        let content = try pasteboardMonitor.detectPasteboardContent()
        
        XCTAssertNotNil(content)
        XCTAssertEqual(content?.type, .image)
        XCTAssertNotNil(content?.data)
        XCTAssertNotNil(content?.fileName)
        XCTAssertTrue(content?.fileName?.hasSuffix(".png") ?? false)
        XCTAssertGreaterThan(content?.fileSize ?? 0, 0)
    }
    
    // MARK: - File Content Detection Tests
    func testDetectFileContent() throws {
        // Create a temporary test file
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("test.txt")
        let testContent = "Test file content"
        try testContent.write(to: tempURL, atomically: true, encoding: .utf8)
        
        // Add file URL to pasteboard
        testPasteboard.clearContents()
        testPasteboard.writeObjects([tempURL as NSURL])
        
        let content = try pasteboardMonitor.detectPasteboardContent()
        
        XCTAssertNotNil(content)
        XCTAssertEqual(content?.type, .text) // .txt files are detected as text
        XCTAssertEqual(content?.fileName, "test.txt")
        XCTAssertEqual(content?.text, testContent)
        XCTAssertEqual(content?.sourceURL, tempURL)
        XCTAssertGreaterThan(content?.fileSize ?? 0, 0)
        
        // Cleanup
        try? FileManager.default.removeItem(at: tempURL)
    }
    
    func testDetectNonExistentFile() throws {
        let nonExistentURL = URL(fileURLWithPath: "/path/to/nonexistent/file.txt")
        
        testPasteboard.clearContents()
        testPasteboard.writeObjects([nonExistentURL as NSURL])
        
        XCTAssertThrowsError(try pasteboardMonitor.detectPasteboardContent()) { error in
            if case ContentManagerError.fileNotFound(let path) = error {
                XCTAssertEqual(path, nonExistentURL.path)
            } else {
                XCTFail("Expected fileNotFound error, got \(error)")
            }
        }
    }
    
    // MARK: - Content Validation Tests
    func testValidateValidContent() {
        let validContent = PasteboardContent(
            type: .text,
            data: Data("Valid content".utf8),
            text: "Valid content",
            fileName: nil,
            mimeType: "text/plain",
            fileSize: 13
        )
        
        XCTAssertTrue(pasteboardMonitor.validatePasteboardContent(validContent))
    }
    
    func testValidateInvalidContent() {
        let invalidContent = PasteboardContent(
            type: .text,
            data: nil,
            text: "",
            fileName: nil,
            mimeType: "text/plain",
            fileSize: 0
        )
        
        XCTAssertFalse(pasteboardMonitor.validatePasteboardContent(invalidContent))
    }
    
    // MARK: - Supported Types Tests
    func testGetSupportedTypes() {
        let supportedTypes = pasteboardMonitor.getSupportedPasteboardTypes()
        
        XCTAssertTrue(supportedTypes.contains(.string))
        XCTAssertTrue(supportedTypes.contains(.png))
        XCTAssertTrue(supportedTypes.contains(.fileURL))
        XCTAssertTrue(supportedTypes.contains(.rtf))
    }
    
    func testHasSupportedContent() {
        // Initially should have no supported content
        XCTAssertFalse(pasteboardMonitor.hasSupportedContent())
        
        // Add text content
        testPasteboard.setString("Test", forType: .string)
        XCTAssertTrue(pasteboardMonitor.hasSupportedContent())
        
        // Clear and test again
        testPasteboard.clearContents()
        XCTAssertFalse(pasteboardMonitor.hasSupportedContent())
    }
    
    // MARK: - Drag and Drop Tests
    func testHandleDroppedText() {
        let testText = "Dropped text content"
        let content = pasteboardMonitor.handleDroppedText(testText)
        
        XCTAssertEqual(content.type, .text)
        XCTAssertEqual(content.text, testText)
        XCTAssertEqual(content.mimeType, "text/plain")
        XCTAssertGreaterThan(content.fileSize, 0)
    }
    
    func testHandleDroppedFiles() throws {
        // Create temporary test files
        let tempDir = FileManager.default.temporaryDirectory
        let textFile = tempDir.appendingPathComponent("test.txt")
        let imageFile = tempDir.appendingPathComponent("test.png")
        
        try "Text content".write(to: textFile, atomically: true, encoding: .utf8)
        
        // Create a simple PNG data
        let pngData = Data([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]) // PNG header
        try pngData.write(to: imageFile)
        
        let urls = [textFile, imageFile]
        let contents = pasteboardMonitor.handleDroppedFiles(urls)
        
        XCTAssertEqual(contents.count, 2)
        
        let textContent = contents.first { $0.fileName == "test.txt" }
        XCTAssertNotNil(textContent)
        XCTAssertEqual(textContent?.type, .text)
        XCTAssertEqual(textContent?.text, "Text content")
        
        let imageContent = contents.first { $0.fileName == "test.png" }
        XCTAssertNotNil(imageContent)
        XCTAssertEqual(imageContent?.type, .image)
        
        // Cleanup
        try? FileManager.default.removeItem(at: textFile)
        try? FileManager.default.removeItem(at: imageFile)
    }
    
    func testHandleDroppedImage() {
        // Create a test image
        let image = NSImage(size: NSSize(width: 50, height: 50))
        image.lockFocus()
        NSColor.blue.setFill()
        NSRect(x: 0, y: 0, width: 50, height: 50).fill()
        image.unlockFocus()
        
        let content = pasteboardMonitor.handleDroppedImage(image)
        
        XCTAssertNotNil(content)
        XCTAssertEqual(content?.type, .image)
        XCTAssertEqual(content?.mimeType, "image/png")
        XCTAssertTrue(content?.fileName?.contains("dropped_image_") ?? false)
        XCTAssertTrue(content?.fileName?.hasSuffix(".png") ?? false)
        XCTAssertGreaterThan(content?.fileSize ?? 0, 0)
    }
    
    // MARK: - Content Conversion Tests
    func testPasteboardContentToContentData() {
        let pasteboardContent = PasteboardContent(
            type: .text,
            data: Data("Test content".utf8),
            text: "Test content",
            fileName: "test.txt",
            mimeType: "text/plain",
            fileSize: 12
        )
        
        let contentData = pasteboardContent.toContentData()
        
        XCTAssertEqual(contentData.contentType, .text)
        XCTAssertEqual(contentData.content, "Test content")
        XCTAssertEqual(contentData.fileName, "test.txt")
        XCTAssertEqual(contentData.mimeType, "text/plain")
        XCTAssertEqual(contentData.fileSize, 12)
    }
    
    // MARK: - Error Handling Tests
    func testEmptyPasteboardError() {
        testPasteboard.clearContents()
        
        XCTAssertThrowsError(try pasteboardMonitor.detectPasteboardContent()) { error in
            XCTAssertEqual(error as? ContentManagerError, ContentManagerError.pasteboardEmpty)
        }
    }
    
    func testClearPasteboard() {
        testPasteboard.setString("Test content", forType: .string)
        XCTAssertTrue(pasteboardMonitor.hasSupportedContent())
        
        pasteboardMonitor.clearPasteboard()
        XCTAssertFalse(pasteboardMonitor.hasSupportedContent())
    }
}