#!/bin/bash

# 智能文件管理器项目设置脚本

echo "🚀 设置智能文件管理器项目..."

# 检查Xcode是否安装
if ! command -v xcodebuild &> /dev/null; then
    echo "❌ 错误: 未找到Xcode，请确保已安装Xcode。"
    exit 1
fi

echo "✅ Xcode已安装"

# 项目路径
PROJECT_PATH="./tempFileXcode.xcodeproj"

if [ ! -d "$PROJECT_PATH" ]; then
    echo "❌ 错误: 未找到项目文件 $PROJECT_PATH"
    exit 1
fi

echo "✅ 找到项目文件"

# 检查权限配置文件
ENTITLEMENTS_FILE="./tempFileXcode/tempFileXcode.entitlements"

echo "📝 配置项目权限..."

# 确保权限文件存在
if [ ! -f "$ENTITLEMENTS_FILE" ]; then
    echo "创建权限配置文件..."
    cat > "$ENTITLEMENTS_FILE" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.app-sandbox</key>
    <true/>
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
    <key>com.apple.security.files.downloads.read-write</key>
    <true/>
    <key>com.apple.security.network.client</key>
    <true/>
    <key>com.apple.developer.icloud-container-identifiers</key>
    <array>
        <string>iCloud.com.contentmanager.app</string>
    </array>
    <key>com.apple.developer.icloud-services</key>
    <array>
        <string>CloudKit</string>
        <string>CloudDocuments</string>
    </array>
    <key>com.apple.developer.ubiquity-kvstore-identifier</key>
    <string>$(TeamIdentifierPrefix)com.contentmanager.app</string>
</dict>
</plist>
EOF
    echo "✅ 权限配置文件已创建"
else
    echo "✅ 权限配置文件已存在"
fi

# 创建Info.plist文件
INFO_PLIST="./tempFileXcode/Info.plist"

if [ ! -f "$INFO_PLIST" ]; then
    echo "创建Info.plist文件..."
    cat > "$INFO_PLIST" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>zh_CN</string>
    <key>CFBundleDisplayName</key>
    <string>智能文件管理器</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>$(PRODUCT_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundleVersion</key>
    <string>1</string>
    <key>LSMinimumSystemVersion</key>
    <string>13.0</string>
    <key>NSPrincipalClass</key>
    <string>NSApplication</string>
    <key>NSMainStoryboardFile</key>
    <string>Main</string>
    <key>NSSupportsAutomaticTermination</key>
    <true/>
    <key>NSSupportsSuddenTermination</key>
    <true/>
    <key>NSUserNotificationAlertStyle</key>
    <string>alert</string>
    <key>NSAppleEventsUsageDescription</key>
    <string>此应用需要发送Apple Events来实现系统集成功能。</string>
    <key>NSSystemAdministrationUsageDescription</key>
    <string>此应用需要系统管理权限来管理文件和监听剪贴板。</string>
    <key>NSDesktopFolderUsageDescription</key>
    <string>此应用需要访问桌面文件夹来管理您的文件。</string>
    <key>NSDocumentsFolderUsageDescription</key>
    <string>此应用需要访问文档文件夹来管理您的文件。</string>
    <key>NSDownloadsFolderUsageDescription</key>
    <string>此应用需要访问下载文件夹来管理您的文件。</string>
    <key>LSUIElement</key>
    <false/>
    <key>LSBackgroundOnly</key>
    <false/>
</dict>
</plist>
EOF
    echo "✅ Info.plist文件已创建"
else
    echo "✅ Info.plist文件已存在"
fi

echo "📚 功能说明:"
echo "• ✨ 智能文件整理和重复检测"
echo "• 🔍 增强文件预览支持 (PDF, 代码, Markdown, 图片等)"
echo "• ☁️  iCloud同步和协作功能"
echo "• 🏷️  智能标签和批次管理"
echo "• 🔄 自动备份和恢复"

echo ""
echo "🔧 配置步骤:"
echo "1. 在Apple Developer Portal创建App ID"
echo "2. 启用CloudKit和iCloud容器"
echo "3. 在Xcode中配置Signing & Capabilities"
echo "4. 添加CloudKit和App Sandbox能力"
echo "5. 配置iCloud容器 ID: iCloud.com.contentmanager.app"

echo ""
echo "⚠️  重要提醒:"
echo "• 需要Apple Developer账户才能使用CloudKit功能"
echo "• 首次运行时需要授权辅助功能权限"
echo "• 建议在macOS 13.0+上运行"

echo ""
echo "🎉 项目设置完成！"
echo "现在可以在Xcode中打开项目并开始开发。"